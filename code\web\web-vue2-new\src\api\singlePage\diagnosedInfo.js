import request from '@/utils/request'

// 门诊医生站》基本信息&&就诊列表
export function GetDiagnosedAndBase(data) {
    return request({
        url: '/DiagnosedInformation/GetDiagnosedAndBase',
        method: 'get',
        params: data
    })
}

// 门诊医生站》以往处方查询
export function GetPrescriptionList(data) {
    return request({
        url: '/DiagnosedInformation/GetPrescriptionList',
        method: 'get',
        params: data
    })
}

// 门诊医生站》检查查询查询
export function GetInspectList(data) {
    return request({
        url: '/DiagnosedInformation/GetInspectList',
        method: 'get',
        params: data
    })
}

// 门诊医生站》以往处置查询
export function GetDisposeList(data) {
    return request({
        url: '/DiagnosedInformation/GetDisposeList',
        method: 'get',
        params: data
    })
}
// 门诊医生站》血糖查询
export function GetBloodSugarList(data) {
    return request({
        url: '/DiagnosedInformation/GetBloodSugarList',
        method: 'get',
        params: data
    })
}
// 门诊医生站》化验查询
export function GetAssayList(data) {
    return request({
        url: '/DiagnosedInformation/GetAssayList',
        method: 'get',
        params: data
    })
}

// 门诊医生站》化验结果查询
export function GetAssayResultList(data) {
    return request({
        url: '/DiagnosedInformation/GetAssayResultList',
        method: 'get',
        params: data
    })
}
// 门诊医生站》
export function GetOutMrList(data) {
    return request({
        url: '/DiagnosedInformation/GetOutMrList',
        method: 'get',
        params: data
    })
}
// 检查结果
export function GetExamResultList(data) {
    return request({
        url: '/DiagnosedInformation/GetExamResultList',
        method: 'get',
        params: data
    })
}




