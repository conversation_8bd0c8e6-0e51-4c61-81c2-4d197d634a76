<template>
  <div>
    <el-dialog
      title="门诊患者病历信息"
      :visible.sync="outpStatus"
      width="85%">
      <div class="outp-patient">
        <div class="patient-info-master">
          <div class="info-master">
            <div class="info-item" style="width: 40%">
              <div class="info-title" >病人ID：</div>
              <div class="info-text">{{basicsData.patientId}}</div>
            </div>
            <div class="info-item">
              <div class="info-title">出生日期：</div>
              <div class="info-text">{{basicsData.dateOfBirth}}</div>
            </div>
          </div>
          <div class="info-master">
            <div class="info-item" style="width: 40%">
              <div class="info-title">病人姓名：</div>
              <div class="info-text">{{basicsData.name}}</div>
            </div>
            <div class="info-item">
              <div class="info-title">费别：</div>
              <div class="info-text">{{basicsData.chargeType}}</div>
            </div>
          </div>
          <div class="info-master">
            <div class="info-item" style="width: 40%">
              <div class="info-title" >性别：</div>
              <div class="info-text">{{basicsData.sex}}</div>
            </div>
            <div class="info-item">
              <div class="info-title">通讯地址：</div>
              <div class="info-text">{{basicsData.mailingAddress}}</div>
            </div>
          </div>
        </div>
        <div class="patient-case-master">
          <div class="case-master">
            <div class="case-title">主诉：</div>
            <div class="case-text">{{outpData.illnesS_DESC}}</div>
          </div>
          <div class="case-master">
            <div class="case-title">现病史：</div>
            <div class="case-text">{{outpData.meD_HISTORY}}</div>
          </div>
          <div class="case-master" style="margin-top: 15px;">
            <div class="case-title">既往史：</div>
            <div class="case-text">{{outpData.anamnesis}}</div>
          </div>
          <div class="case-master">
            <div class="case-title">过敏史：</div>
            <div class="case-text">{{outpData.individual}}</div>
          </div>
          <div class="case-master">
            <div class="case-title">体格检查：</div>
            <div class="case-text">{{outpData.bodY_EXAM}}</div>
          </div>
          <div class="case-master" style="margin-top: 15px;">
            <div class="case-title">辅助检查：</div>
            <div class="case-text">{{outpData.advice}}</div>
          </div>
          <div class="case-master">
            <div class="case-title">结果：</div>
            <div class="case-text"></div>
          </div>
          <div class="case-master" style="margin-top: 15px;">
            <div class="case-title">初步诊断：</div>
            <div class="case-text">{{outpData.diaG_DESC}}</div>
          </div>
          <div class="case-master">
            <div class="case-title">治疗处理意见：</div>
            <div class="case-text">{{outpData.operatioN_RECORD}}</div>
          </div>
        </div>
        <div class="patient-exam-master">
          <div class="exam-master">
            <div class="exam-item">
              <div class="exam-title">检查项目：</div>
              <div class="exam-text">{{basicsData.device}}</div>
            </div>
            <div class="exam-item">
              <div class="exam-title">价格：</div>
              <div class="exam-text" style="color: #a95812;font-size: 22px;">{{basicsData.costs}}</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      title="住院患者病历信息"
      :visible.sync="inStatus"
      width="85%">
      <div class="in-patient">
        <div class="patient-info-master">
          <div class="info-master">
            <div class="info-item">
              <div class="info-title">病人ID：</div>
              <div class="info-text">{{inData.patienT_ID}}</div>
            </div>
            <div class="info-item">
              <div class="info-title">住院号：</div>
              <div class="info-text">{{inData.inP_NO}}</div>
            </div>
            <div class="info-item">
              <div class="info-title">姓名：</div>
              <div class="info-text">{{inData.name}}</div>
            </div>
            <div class="info-item">
              <div class="info-title">性别：</div>
              <div class="info-text">{{inData.sex}}</div>
            </div>
            <div class="info-item">
              <div class="info-title">年龄：</div>
              <div class="info-text">{{inData.c_AGE}}</div>
            </div>
            <div class="info-item">
              <div class="info-title">费别：</div>
              <div class="info-text">{{inData.chargE_TYPE}}</div>
            </div>
          </div>
          <div class="info-master">
            <div class="info-item-two">
              <div class="info-title">入院日期：</div>
              <div class="info-text">{{inData.admissioN_DATE_TIME}}</div>
            </div>
            <div class="info-item-two">
              <div class="info-title">入院科室：</div>
              <div class="info-text">{{inData.depT_NAME}}</div>
            </div>
            <div class="info-item-two" style="width: 15%">
              <div class="info-title">主治医师：</div>
              <div class="info-text">{{inData.attendinG_DOCTOR}}</div>
            </div>
          </div>
          <div class="info-master">
            <div class="info-item-two">
              <div class="info-title">诊断：</div>
              <div class="info-text">{{inData.diagnosis}}</div>
            </div>
            <div class="info-item-two">
              <div class="info-title">病情：</div>
              <div class="info-text">{{inData.patienT_CONDITION}}</div>
            </div>
            <div class="info-item-two" style="width: 15%">
              <div class="info-title">经治医师：</div>
              <div class="info-text">{{inData.doctoR_IN_CHARGE}}</div>
            </div>
          </div>
        </div>
        <div class="patient-case-master">
          <div class="case-master">
            <div class="case-item" style="margin-top:5px;">
              <div class="case-title">症状：</div>
              <div class="case-text">{{basicsData.clinSymp}}</div>
            </div>
            <div class="case-item">
              <div class="case-title">体征：</div>
              <div class="case-text">{{basicsData.physSign}}</div>
            </div>
            <div class="case-item">
              <div class="case-title">临床诊断：</div>
              <div class="case-text">{{basicsData.clinDiag}}</div>
            </div>
            <div class="case-bottom">
              <div class="case-item-two">
                <div class="case-title-two">检查项目：</div>
                <div class="case-text">{{basicsData.device}}</div>
              </div>
              <div class="case-item-two">
                <div class="case-title-two">价格：</div>
                <div class="case-text">{{basicsData.costs}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {GetPatientThisDayQueueMessage,GetOutpPatientCaseInfoMsg} from "@/api/checkAndConfirm/patientCaseInfo"
export default {
  name: 'caseHistoryMessage',
  props: [],
  components: {},
  data() {
    return {
      outpStatus: false,
      inStatus: false,
      basicsData:{},
      inData:{},
      outpData: {},
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    caseMsgInitVerify(data){

      this.basicsData = {};

      if (data.visitId === '0'){
        this.outpCaseDataInit(data);
      }else{
        this.inCaseDataInit(data);
      }
      this.basicsData = data;
    },
    outpCaseDataInit(data){
      GetOutpPatientCaseInfoMsg(data.patientId,data.examNo).then(res => {
        if (res.code === 200){
          this.outpData = res.data[0]
          this.inStatus = false;
          this.outpStatus = true;
        }
      })
    },
    inCaseDataInit(data){
      GetPatientThisDayQueueMessage(data.patientId,data.visitId).then(res => {
        if (res.code === 200){
          this.inData = res.data[0]
          this.outpStatus = false;
          this.inStatus = true;
        }
      })

    },
  }
}
</script>

<style scoped lang="scss">
.in-patient{
  .patient-info-master{
    border: 1px solid #00a19b;
    .info-master{
      padding: 10px 20px;
      display: flex;
      justify-content: space-around;

      .info-item{
        width: 13%;
        display: flex;
      }
      .info-item-two{
        width: 30%;
        display: flex;
      }
      .info-title{
        font-size: 16px;
        color: black;
      }
      .info-text{
        font-size: 14px;
        display: flex;
        align-items: center;
        color: black;
      }
    }
  }
  .patient-case-master{
    margin-top: 10px;
    border: 1px solid #00a19b;
    .case-master{
      padding: 10px 150px;
      min-height: 300px;
      .case-item{
        display: flex;
        margin-top: 20px;
      }
      .case-title{
        width: 15%;
        font-size: 18px;
        display: flex;
        align-items: center;
        color: black;
      }
      .case-title-two{
        font-size: 18px;
        color: black;
      }
      .case-text{
        font-size: 16px;
        line-height: 1.7;
        color: black;
      }
      .case-bottom{
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
      }
      .case-item-two{
        display: flex;
      }
    }
  }
}
.outp-patient{
  .patient-info-master{
    padding: 10px 250px;
    border: 1px solid #00a19b;
    .info-master{
      display: flex;
      .info-item{
        display: flex;
        .info-title{
          font-size: 18px;
          color: black;
        }
        .info-text{
          font-size: 16px;
          display: flex;
          align-items: center;
          color: black;
        }
      }
    }
  }
  .patient-case-master{
    padding: 10px 50px;
    border: 1px solid #00a19b;
    margin-top: 15px;
    .case-master{
      display: flex;
      .case-title{
        width: 13%;
        font-size: 18px;
        line-height: 1.7;
        color: black;
      }
      .case-text{
        font-size: 16px;
        color: black;
      }
    }
  }
  .patient-exam-master{
    padding: 10px 100px;
    border: 1px solid #00a19b;
    margin-top: 15px;
    .exam-master{
      display: flex;
      justify-content: space-between;
      .exam-item{
        display: flex;
        .exam-title{
          font-size: 18px;
          color: black;
        }
        .exam-text{
          font-size: 16px;
          display: flex;
          align-items: center;
          color: black;
        }
      }
    }
  }
}

</style>
