function keyboard() {
  this.code = ''
  this.lastTime = new Date().getTime()
  this.caseFormat = false
  this.timer = undefined
  this.run = function(callback) {
    if (!this.timer) {
      this.timer = setInterval(() => {
        var now = new Date().getTime()
        if (now - this.lastTime > 200) {
          this.code = ''
        }
      }, 200)
    }
    document.onkeydown = (e) => {
      var nextTime = new Date().getTime()
      var _code = e.keyCode ? e.keyCode : e.which ? e.which : e.charCode
      //shift键
      if (_code == 16) {
        this.caseFormat = true
      } else {
        if (this.caseFormat) {
          if (_code >= 65 && _code <= 90) {
            //转小写
            _code = _code + 32
          } else if (_code >= 97 && _code <= 122) {
            //转大写
            _code = _code - 32
          }
          this.caseFormat = false
        }
        var char = String.fromCharCode(_code)
        if (this.code == '') {
          this.code += char
        } else if (nextTime - this.lastTime <= 30) {
          this.code += char
        }
      }
      this.lastTime = nextTime
    }

    window.onkeydown = (e) => {
      let _code = e.keyCode ? e.keyCode : e.which ? e.which : e.charCode
      if (_code == 13 && this.code.length > 0) {
        if (callback) {
          this.code = this.code.substring(0, this.code.length - 1)
          callback(this.code)
        }
        this.code = ''
      }
    }
  },
    this.close = function() {
      document.onkeydown = null
      window.onkeydown = null
      if (this.timer) {
        clearInterval(this.timer)
      }
    }
}

var key = new keyboard()

export default key
