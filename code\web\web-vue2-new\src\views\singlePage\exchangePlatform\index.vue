<template>

  <div :style="'height:' + tableHeight + 'px'">
    <div class="single-master" v-if="bodyStatus" :style="'height:' + tableHeight + 'px'">
      <div class="single-title">交换平台</div>
      <div class="single-element">
        <div class="element-master">
          <div class="element-item">
            <div class="item-left" :style="'height:' + (tableHeight - 70) + 'px'">
              <el-menu
                :default-active="menuData"
                class="el-menu-vertical-demo"
                @select="handleOpen" style="height: 100%;"
              >
                <el-menu-item index="send" v-if="hostStatus">
                  <i class="el-icon-s-promotion"></i>
                  <span slot="title">文件交换</span>
                </el-menu-item>
                <el-menu-item index="reception">
                  <i class="el-icon-message"></i>
                  <span slot="title">接收列表</span>
                </el-menu-item>
                <el-menu-item index="history" v-if="hostStatus">
                  <i class="el-icon-postcard"></i>
                  <span slot="title">发送记录</span>
                </el-menu-item>
              </el-menu>
            </div>
            <div class="item-right">
              <send v-if="menuData === 'send'" :master="masterData" :table-height="(tableHeight - 70)" :key="tableKey"
              ></send>
              <reception v-if="menuData === 'reception'" :table-height="(tableHeight)" :employee-id="employeeId"
                         :key="tableKey"
              ></reception>
              <history v-if="menuData === 'history'" :table-height="(tableHeight)" :employee-id="employeeId"
                       :key="tableKey"
              ></history>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="logo-home" v-if="!bodyStatus">
      <div class="login-master">
        <!--    登录代码-->
        <div class="login-form">
          <div class="logo_container"></div>
          <div class="logo_title">{{ systemTitle }}</div>
          <div class="logo_subtitle">Get started Ready to use the exchange platform</div>

          <el-tabs type="card"  v-model="loginForm.loginType" style="width: 90%;">
            <el-tab-pane label="工号登录" name="1">
              <div class="input_container">
                <label class="input_label">账号：</label>
                <svg t="1735979690188" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                     p-id="4257"
                >
                  <path
                    d="M511.913993 941.605241c-255.612968 0-385.311608-57.452713-385.311608-170.810012 0-80.846632 133.654964-133.998992 266.621871-151.88846L393.224257 602.049387c-79.986561-55.904586-118.86175-153.436587-118.86175-297.240383 0-139.33143 87.211154-222.586259 233.423148-222.586259l7.912649 0c146.211994 0 233.423148 83.254829 233.423148 222.586259 0 54.184445 0 214.67361-117.829666 297.412397l-0.344028 16.685369c132.966907 18.061482 266.105829 71.041828 266.105829 151.716445C897.225601 884.152528 767.526961 941.605241 511.913993 941.605241zM507.957668 141.567613c-79.470519 0-174.250294 28.382328-174.250294 163.241391 0 129.698639 34.230808 213.469511 104.584579 255.784982 8.944734 5.332437 14.277171 14.965228 14.277171 25.286074l0 59.344868c0 15.309256-11.524945 28.0383-26.662187 29.414413-144.319839 14.449185-239.959684 67.429531-239.959684 95.983874 0 92.199563 177.346548 111.637158 325.966739 111.637158 148.792206 0 325.966739-19.26558 325.966739-111.637158 0-28.726356-95.639845-81.534688-239.959684-95.983874-15.48127-1.548127-27.006215-14.621199-26.662187-30.102469l1.376113-59.344868c0.172014-10.148833 5.676466-19.437594 14.277171-24.770032 70.525785-42.487485 103.208466-123.678145 103.208466-255.784982 0-135.031077-94.779775-163.241391-174.250294-163.241391L507.957668 141.567613 507.957668 141.567613z"
                    fill="#575B66" p-id="4258"
                  >
                  </path>
                </svg>
                <input placeholder="请使用HIS工号登录" title="请输入您的HIS账号" v-model="loginForm.userName" type="text"
                       class="input_field" id="email_field"
                >
              </div>
              <div class="input_container">
                <label class="input_label">密码：</label>
                <svg fill="none" viewBox="0 0 24 24" height="24" width="24" xmlns="http://www.w3.org/2000/svg" class="icon">
                  <path stroke-linecap="round" stroke-width="1.5" stroke="#141B34"
                        d="M18 11.0041C17.4166 9.91704 16.273 9.15775 14.9519 9.0993C13.477 9.03404 11.9788 9 10.329 9C8.67911 9 7.18091 9.03404 5.70604 9.0993C3.95328 9.17685 2.51295 10.4881 2.27882 12.1618C2.12602 13.2541 2 14.3734 2 15.5134C2 16.6534 2.12602 17.7727 2.27882 18.865C2.51295 20.5387 3.95328 21.8499 5.70604 21.9275C6.42013 21.9591 7.26041 21.9834 8 22"
                  >
                  </path>
                  <path stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5" stroke="#141B34"
                        d="M6 9V6.5C6 4.01472 8.01472 2 10.5 2C12.9853 2 15 4.01472 15 6.5V9"
                  ></path>
                  <path fill="#141B34"
                        d="M21.2046 15.1045L20.6242 15.6956V15.6956L21.2046 15.1045ZM21.4196 16.4767C21.7461 16.7972 22.2706 16.7924 22.5911 16.466C22.9116 16.1395 22.9068 15.615 22.5804 15.2945L21.4196 16.4767ZM18.0228 15.1045L17.4424 14.5134V14.5134L18.0228 15.1045ZM18.2379 18.0387C18.5643 18.3593 19.0888 18.3545 19.4094 18.028C19.7299 17.7016 19.7251 17.1771 19.3987 16.8565L18.2379 18.0387ZM14.2603 20.7619C13.7039 21.3082 12.7957 21.3082 12.2394 20.7619L11.0786 21.9441C12.2794 23.1232 14.2202 23.1232 15.4211 21.9441L14.2603 20.7619ZM12.2394 20.7619C11.6914 20.2239 11.6914 19.358 12.2394 18.82L11.0786 17.6378C9.86927 18.8252 9.86927 20.7567 11.0786 21.9441L12.2394 20.7619ZM12.2394 18.82C12.7957 18.2737 13.7039 18.2737 14.2603 18.82L15.4211 17.6378C14.2202 16.4587 12.2794 16.4587 11.0786 17.6378L12.2394 18.82ZM14.2603 18.82C14.8082 19.358 14.8082 20.2239 14.2603 20.7619L15.4211 21.9441C16.6304 20.7567 16.6304 18.8252 15.4211 17.6378L14.2603 18.82ZM20.6242 15.6956L21.4196 16.4767L22.5804 15.2945L21.785 14.5134L20.6242 15.6956ZM15.4211 18.82L17.8078 16.4767L16.647 15.2944L14.2603 17.6377L15.4211 18.82ZM17.8078 16.4767L18.6032 15.6956L17.4424 14.5134L16.647 15.2945L17.8078 16.4767ZM16.647 16.4767L18.2379 18.0387L19.3987 16.8565L17.8078 15.2945L16.647 16.4767ZM21.785 14.5134C21.4266 14.1616 21.0998 13.8383 20.7993 13.6131C20.4791 13.3732 20.096 13.1716 19.6137 13.1716V14.8284C19.6145 14.8284 19.619 14.8273 19.6395 14.8357C19.6663 14.8466 19.7183 14.8735 19.806 14.9391C19.9969 15.0822 20.2326 15.3112 20.6242 15.6956L21.785 14.5134ZM18.6032 15.6956C18.9948 15.3112 19.2305 15.0822 19.4215 14.9391C19.5091 14.8735 19.5611 14.8466 19.5879 14.8357C19.6084 14.8273 19.6129 14.8284 19.6137 14.8284V13.1716C19.1314 13.1716 18.7483 13.3732 18.4281 13.6131C18.1276 13.8383 17.8008 14.1616 17.4424 14.5134L18.6032 15.6956Z"
                  ></path>
                </svg>
                <input placeholder="请使用HIS密码登录" title="请输入您的HIS密码" v-model="loginForm.password"
                       type="password" class="input_field" id="password_field"
                >
              </div>
            </el-tab-pane>
            <el-tab-pane label="oa登录" name="0">
              <div class="input_container">
                <label class="input_label">账号：</label>
                <svg t="1735979690188" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                     p-id="4257"
                >
                  <path
                    d="M511.913993 941.605241c-255.612968 0-385.311608-57.452713-385.311608-170.810012 0-80.846632 133.654964-133.998992 266.621871-151.88846L393.224257 602.049387c-79.986561-55.904586-118.86175-153.436587-118.86175-297.240383 0-139.33143 87.211154-222.586259 233.423148-222.586259l7.912649 0c146.211994 0 233.423148 83.254829 233.423148 222.586259 0 54.184445 0 214.67361-117.829666 297.412397l-0.344028 16.685369c132.966907 18.061482 266.105829 71.041828 266.105829 151.716445C897.225601 884.152528 767.526961 941.605241 511.913993 941.605241zM507.957668 141.567613c-79.470519 0-174.250294 28.382328-174.250294 163.241391 0 129.698639 34.230808 213.469511 104.584579 255.784982 8.944734 5.332437 14.277171 14.965228 14.277171 25.286074l0 59.344868c0 15.309256-11.524945 28.0383-26.662187 29.414413-144.319839 14.449185-239.959684 67.429531-239.959684 95.983874 0 92.199563 177.346548 111.637158 325.966739 111.637158 148.792206 0 325.966739-19.26558 325.966739-111.637158 0-28.726356-95.639845-81.534688-239.959684-95.983874-15.48127-1.548127-27.006215-14.621199-26.662187-30.102469l1.376113-59.344868c0.172014-10.148833 5.676466-19.437594 14.277171-24.770032 70.525785-42.487485 103.208466-123.678145 103.208466-255.784982 0-135.031077-94.779775-163.241391-174.250294-163.241391L507.957668 141.567613 507.957668 141.567613z"
                    fill="#575B66" p-id="4258"
                  >
                  </path>
                </svg>
                <input placeholder="请输入您的oa登录账号" title="请输入您的oa登录账号" v-model="loginForm.userName" type="text"
                       class="input_field" id="email_field"
                >
              </div>
              <div class="input_container">
                <label class="input_label">密码：</label>
                <svg fill="none" viewBox="0 0 24 24" height="24" width="24" xmlns="http://www.w3.org/2000/svg" class="icon">
                  <path stroke-linecap="round" stroke-width="1.5" stroke="#141B34"
                        d="M18 11.0041C17.4166 9.91704 16.273 9.15775 14.9519 9.0993C13.477 9.03404 11.9788 9 10.329 9C8.67911 9 7.18091 9.03404 5.70604 9.0993C3.95328 9.17685 2.51295 10.4881 2.27882 12.1618C2.12602 13.2541 2 14.3734 2 15.5134C2 16.6534 2.12602 17.7727 2.27882 18.865C2.51295 20.5387 3.95328 21.8499 5.70604 21.9275C6.42013 21.9591 7.26041 21.9834 8 22"
                  >
                  </path>
                  <path stroke-linejoin="round" stroke-linecap="round" stroke-width="1.5" stroke="#141B34"
                        d="M6 9V6.5C6 4.01472 8.01472 2 10.5 2C12.9853 2 15 4.01472 15 6.5V9"
                  ></path>
                  <path fill="#141B34"
                        d="M21.2046 15.1045L20.6242 15.6956V15.6956L21.2046 15.1045ZM21.4196 16.4767C21.7461 16.7972 22.2706 16.7924 22.5911 16.466C22.9116 16.1395 22.9068 15.615 22.5804 15.2945L21.4196 16.4767ZM18.0228 15.1045L17.4424 14.5134V14.5134L18.0228 15.1045ZM18.2379 18.0387C18.5643 18.3593 19.0888 18.3545 19.4094 18.028C19.7299 17.7016 19.7251 17.1771 19.3987 16.8565L18.2379 18.0387ZM14.2603 20.7619C13.7039 21.3082 12.7957 21.3082 12.2394 20.7619L11.0786 21.9441C12.2794 23.1232 14.2202 23.1232 15.4211 21.9441L14.2603 20.7619ZM12.2394 20.7619C11.6914 20.2239 11.6914 19.358 12.2394 18.82L11.0786 17.6378C9.86927 18.8252 9.86927 20.7567 11.0786 21.9441L12.2394 20.7619ZM12.2394 18.82C12.7957 18.2737 13.7039 18.2737 14.2603 18.82L15.4211 17.6378C14.2202 16.4587 12.2794 16.4587 11.0786 17.6378L12.2394 18.82ZM14.2603 18.82C14.8082 19.358 14.8082 20.2239 14.2603 20.7619L15.4211 21.9441C16.6304 20.7567 16.6304 18.8252 15.4211 17.6378L14.2603 18.82ZM20.6242 15.6956L21.4196 16.4767L22.5804 15.2945L21.785 14.5134L20.6242 15.6956ZM15.4211 18.82L17.8078 16.4767L16.647 15.2944L14.2603 17.6377L15.4211 18.82ZM17.8078 16.4767L18.6032 15.6956L17.4424 14.5134L16.647 15.2945L17.8078 16.4767ZM16.647 16.4767L18.2379 18.0387L19.3987 16.8565L17.8078 15.2945L16.647 16.4767ZM21.785 14.5134C21.4266 14.1616 21.0998 13.8383 20.7993 13.6131C20.4791 13.3732 20.096 13.1716 19.6137 13.1716V14.8284C19.6145 14.8284 19.619 14.8273 19.6395 14.8357C19.6663 14.8466 19.7183 14.8735 19.806 14.9391C19.9969 15.0822 20.2326 15.3112 20.6242 15.6956L21.785 14.5134ZM18.6032 15.6956C18.9948 15.3112 19.2305 15.0822 19.4215 14.9391C19.5091 14.8735 19.5611 14.8466 19.5879 14.8357C19.6084 14.8273 19.6129 14.8284 19.6137 14.8284V13.1716C19.1314 13.1716 18.7483 13.3732 18.4281 13.6131C18.1276 13.8383 17.8008 14.1616 17.4424 14.5134L18.6032 15.6956Z"
                  ></path>
                </svg>
                <input placeholder="请输入您的oa登录密码" title="请输入您的oa登录密码" v-model="loginForm.password"
                       type="password" class="input_field" id="password_field"
                >
              </div>
            </el-tab-pane>
          </el-tabs>

          <el-button type="primary" style="width: 100%; height: 45px; font-size: 24px;" @click="login" tabindex="0">
            登录
          </el-button>

        </div>
      </div>
    </div>
  </div>

</template>

<script>
/**
 * 交换平台
 * /exchangePlatform/index
 */
import { getUserInfo, userLogin } from '@/api/singlePage/exchangePlatform'
import env from '@/utils/ApiConfig3'
import Send from './module/send.vue'
import Reception from './module/reception.vue'
import History from './module/history.vue'
import { setToken,setAccessToken,setAccessId } from '@/utils/auth'

export default {
  name: 'index',
  props: [],
  components: { History, Reception, Send },
  data() {
    return {
      loginForm: {
        userName: '',
        password: '',
        codeStatus: '1',
        loginType: '1',
        os: '交换平台',
        osType: '2',
        integrationToken: '',
      },
      systemTitle: '交换平台',
      menuData: '',
      bodyStatus: false,
      employeeId: '',
      masterData: {
        senderNo: '',
        senderName: '',
        senderTitle: '',
        senderText: '',
        memo: ''
      },
      tableHeight: undefined,
      hostStatus: false,
      tableKey: 0
    }
  },
  created() {
    this.loginVerify()
    this.getHost()
    this.handleResize()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    loginVerify() {
      let thisToken =this.$store.getters.token;
      let loginType = '0';
      if (!thisToken){
        let accessTokenTwo = this.$store.getters.accessToken;
        let accessIdTwo = this.$store.getters.accessId;
        let accessToken = this.$route.query && this.$route.query.access_token;
        let accessId = this.$route.query && this.$route.query.GID;
        if (accessToken || accessTokenTwo){
          //集成平台校验
          loginType = '1';
          this.loginForm.loginType = '4';
          if (accessToken && accessToken !== '' && accessToken !== undefined){
            setAccessToken(accessToken)
            this.loginForm.integrationToken = accessToken;
          }else if (accessTokenTwo && accessTokenTwo !== '' && accessTokenTwo !== undefined){
            this.loginForm.integrationToken = accessTokenTwo;
          }
          this.login();
        }else if (accessId || accessIdTwo){
          //oa平台校验
          loginType = '2';
          let host = env.get_base_url()
          if (!host.includes('10.1')){
            this.loginForm.loginType = '5';
          }else{
            this.loginForm.loginType = '6';
          }
          if (accessId && accessId !== '' && accessId !== undefined){
            setAccessId(accessId)
            this.loginForm.integrationToken = accessId;
          }else if (accessIdTwo && accessIdTwo !== '' && accessIdTwo !== undefined){
            this.loginForm.integrationToken = accessIdTwo;
          }
          this.login();
        }else{
          //使用本页面登录
          this.loginForm.loginType = '1';
          this.bodyStatus = false;
        }
      }else{
        this.loginInfo();
      }
      this.$store.commit('LOGIN_TYPE', loginType)
    },
    loginInfo(){
      getUserInfo().then(res => {
        this.$store.commit('SET_ID', res.data.oaUserId)
        this.$store.commit('SET_NAME', res.data.oaUserName)
        this.masterData = {
          senderNo: res.data.oaUserId,
          senderName: res.data.oaName,
          senderTitle: '',
          senderText: '',
          memo: ''
        }
        this.bodyStatus = true;
      })
    },
    login() {
      const loading = this.$loading({
        lock: true,
        text: "登录中,请稍后...(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      userLogin(this.loginForm).then(res => {
        if (res.code === 200) {
          setToken(res.data.accessToken)
          this.loginInfo();
        }
      }).finally(() => {
        loading.close();
      })
    },
    handleOpen(key) {
      this.menuData = key
      ++this.tableKey
    },
    getHost() {
      let host = env.get_base_url()
      this.hostStatus = !host.includes('10.1')
      if (this.hostStatus) {
        this.menuData = 'send'
      } else {
        this.menuData = 'reception'
      }
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage2";

.item-left {
  height: 100%;
  width: 15%;
  border-right: 1xp solid #DFECFD;

  ::v-deep.el-menu-item {
    border-bottom: 1px solid #DFECFD;
  }
}

.item-right {
  width: 85%;
}

.logo-home {
  background-color: #E8E8E8;
  height: 100%;

  .login-master {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80%;
    @media screen and (max-width: 2000px) {
      .login-form {
        width: 25%
      }
    }
    @media screen and (max-width: 1600px) {
      .login-form {
        width: 30%
      }
    }
    @media screen and (max-width: 1400px) {
      .login-form {
        width: 35%
      }
    }
    @media screen and (max-width: 1200px) {
      .login-form {
        width: 40%
      }
    }
    @media screen and (max-width: 1000px) {
      .login-form {
        width: 50%
      }
    }
    @media screen and (max-width: 600px) {
      .login-form {
        width: 80%
      }
    }
    @media screen and (max-height: 500px) {
      .login-form{
        height: 120% !important;
        width: 50%;
        .logo_container {
          width: 60px;
          height: 60px;
        }
        .logo_title {
          font-size: 1rem;
          letter-spacing: 0.3em;
        }
        .logo_subtitle {
          font-size: 0.625rem;
          line-height: 1rem;
        }
        .input_container {
          width: 80% !important;
        }
        .input_label {
          font-size: 0.65rem !important;
        }
        .input_field {
          height: 35px !important;
        }
      }
    }

    .login-form {
      height: 80%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      gap: 15px;
      padding: 50px 40px 20px 40px;
      background-color: #ffffff;
      box-shadow: 0px 106px 42px rgba(0, 0, 0, 0.01),
      0px 59px 36px rgba(0, 0, 0, 0.05), 0px 26px 26px rgba(0, 0, 0, 0.09),
      0px 7px 15px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
      border-radius: 11px;
      font-family: "Inter", sans-serif;

      .logo_container {
        box-sizing: border-box;
        width: 80px;
        height: 80px;
        background: url("../../../assets/logo/honliv.jpg") no-repeat;
        background-size: 100% 100%;
        border: 1px solid #F7F7F8;
        filter: drop-shadow(0px 0.5px 0.5px #EFEFEF) drop-shadow(0px 1px 0.5px rgba(239, 239, 239, 0.5));
        border-radius: 11px;
      }

      .logo_title {
        margin: 0;
        font-size: 1.35rem;
        font-weight: 700;
        color: #212121;
        letter-spacing: 0.5em;
      }

      .logo_subtitle {
        font-size: 0.725rem;
        max-width: 80%;
        text-align: center;
        line-height: 1.1rem;
        color: #8B8E98
      }

      .input_container {
        width: 100%;
        height: fit-content;
        position: relative;
        display: flex;
        flex-direction: column;
        gap: 5px;
      }

      .icon {
        width: 20px;
        position: absolute;
        z-index: 99;
        left: 12px;
        bottom: 9px;
      }

      .input_label {
        font-size: 0.75rem;
        color: #8B8E98;
        font-weight: 600;
      }

      .input_field {
        width: auto;
        height: 40px;
        padding: 0 0 0 40px;
        border-radius: 7px;
        outline: none;
        border: 1px solid #e5e5e5;
        filter: drop-shadow(0px 1px 0px #efefef) drop-shadow(0px 1px 0.5px rgba(239, 239, 239, 0.5));
        transition: all 0.3s cubic-bezier(0.15, 0.83, 0.66, 1);
      }

      .input_field:focus {
        border: 1px solid transparent;
        box-shadow: 0px 0px 0px 2px #242424;
        background-color: transparent;
      }
    }
  }
  ::v-deep.el-tabs__nav {
    width: 100%;
  }
  ::v-deep.el-tabs__item {
    width: 50%;
    text-align: center;
  }
  ::v-deep.el-tabs__item.is-active {
    color: #1890ff;
    text-align: center;
  }
  ::v-deep.el-tabs--card > .el-tabs__header .el-tabs__item {
    border-bottom: 1px solid #DFE4ED;
  }
}

</style>
