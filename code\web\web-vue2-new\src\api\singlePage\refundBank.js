import request from '@/utils/request'

// 行内
export function GetRefundBankHang(data) {
    return request({
        url: '/RefundBank/GetRefundBankHang',
        method: 'get',
        params: data
    })
}

// 跨内
export function GetRefundBankKua(data) {
    return request({
        url: '/RefundBank/GetRefundBankKua',
        method: 'get',
        params: data
    })
}

// 其它
export function GetRefundBankQt(data) {
    return request({
        url: '/RefundBank/GetRefundBankQt',
        method: 'get',
        params: data
    })
}