<template>
  <div>
      <el-dialog
        :visible.sync="callSelectButton"
        :title="callSelectDialogTitle"
        width="70%"
      >
        <div style="display: flex;justify-content: center;margin-bottom: 20px;">
          <el-input
            @keyup.enter.native="callSelectCreateData"
            style="width: 30%"
            v-model="callSelectForm.patient"
            placeholder="请输入患者ID号进行查询"></el-input>
          <el-button type="primary" @click="callSelectCreateData">搜索</el-button>
        </div>
        <div style="height: 500px">
          <el-scrollbar style="height: 90%">
            <el-table ref="multipleTable3" :data="callSelectTable" size="mini" border style="width: 100%">
              <el-table-column align="center" prop="unitName"  label="单元" width="90"></el-table-column>
              <el-table-column align="center" prop="windowName" width="85" label="窗口"></el-table-column>
              <el-table-column align="center" prop="queueId" width="90" label="ID号"></el-table-column>
              <el-table-column align="center" prop="queueName" width="80" label="姓名"></el-table-column>
              <el-table-column align="center" prop="queueNo" width="75" label="队列号"></el-table-column>
              <el-table-column align="center" prop="content" label="检查项目" width="180"></el-table-column>
              <el-table-column align="center" prop="priority" width="80" label="号源">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.priority === 1" type="success">回诊</el-tag>
                    <el-tag v-else-if="scope.row.priority === 2">预约</el-tag>
                    <el-tag v-else-if="scope.row.priority === 3">普通</el-tag>
                    <el-tag v-else-if="scope.row.priority === 4" type="info">过号</el-tag>
                    <el-tag v-else-if="scope.row.priority === 5" type="danger">急诊</el-tag>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="status" width="90" label="状态">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.deleteFlag !== 0" type="info">已删除</el-tag>
                  <el-tag v-else-if="scope.row.status === 1">等待中</el-tag>
                  <el-tag v-else-if="scope.row.status === 2" type="success">就诊结束</el-tag>
                  <el-tag v-else-if="scope.row.status === 3" type="success">就诊结束</el-tag>
                  <el-tag v-else-if="scope.row.status === 4" type="success">就诊结束</el-tag>
                  <el-tag v-else-if="scope.row.status === 5" type="info">过号</el-tag>
                  <el-tag v-else-if="scope.row.status === 6" type="info">作废</el-tag>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="callNum" width="110" label="呼叫次数"></el-table-column>
              <el-table-column align="center" prop="time" width="180" label="登记时间"></el-table-column>
              <el-table-column align="center" prop="memo" label="备注"></el-table-column>
            </el-table>
          </el-scrollbar>
        </div>
      </el-dialog>
  </div>
</template>

<script>
import {GetCallQueueMessage} from "@/api/appointment/reservationCenter";
export default {
  name: 'callQueueMessage',
  props: [],
  components: {},
  data() {
    return {
      callSelectButton: false,
      callSelectDialogTitle: "",
      callSelectTable:[],
      title: " -> 叫号信息查询",
      callSelectForm:{},
    }
  },
  methods: {
    callSelectInit(data){
      if (data){
        this.callSelectDialogTitle = data.unitName + this.title;
        this.callSelectForm = data;
        this.callSelectCreateData();
      }
    },
    callSelectCreateData(){
      GetCallQueueMessage(this.callSelectForm).then((res) => {
        this.callSelectTable = res.data;
        this.callSelectButton = true;
      });
    },
  }
}
</script>

<style scoped lang="scss">

</style>
