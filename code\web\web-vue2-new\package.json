{"name": "oa-vue2", "version": "3.8.7", "description": "宏力医院网络协同办公系统", "author": "honlivhp", "license": "MIT", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "http://**********:8666/devprogram/globalproject/oa"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "clear-module": "^4.1.2", "clipboard": "2.0.8", "core-js": "^3.25.3", "echarts": "4.9.0", "element-ui": "2.15.14", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsbarcode": "^3.11.6", "jsencrypt": "3.0.0-rc.1", "moment": "^2.29.4", "nprogress": "0.2.0", "print-js": "^1.6.0", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "spark-md5": "^3.0.2", "vue": "2.6.12", "vue-barcode": "^1.3.0", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-meta": "2.4.0", "vue-print-nb": "^1.7.5", "vue-qr": "^4.0.9", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "html-webpack-plugin": "^5.6.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "script-loader": "^0.7.2", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"], "volta": {"node": "16.20.0"}}