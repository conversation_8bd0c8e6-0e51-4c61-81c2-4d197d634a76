<template>
    <div class="element-table" :style="bodyWidth">
      <el-table v-loading="loading" :data="tableData" style="width: 100%" border :height="(tableHeight - 260)" highlight-current-row>
        <el-table-column type="index" width="50" align="center"></el-table-column>
        <el-table-column prop="DRUG_NAME" align="center" label="药品名称"></el-table-column>
        <el-table-column prop="PACKAGE_SPEC" align="center" label="包装规格" width="80"></el-table-column>
        <el-table-column prop="PACKAGE_UNITS" align="center" label="包装单位" width="80"></el-table-column>
        <el-table-column prop="FIRM_ID" align="center" label="厂商" width="120"></el-table-column>
        <el-table-column prop="CQUANTITY" align="center" label="库存总量" width="75"></el-table-column>
        <el-table-column prop="RETAIL_PRICE" align="center" label="市场零售价" width="90"></el-table-column>
        <el-table-column prop="DRUG_SPEC" align="center" label="单位" width="70"></el-table-column>
        <el-table-column prop="UNITS" align="center" label="规格" width="60"></el-table-column>
      </el-table>
    </div>
</template>

<script>
import {getDrugProcurementPlanTableMasterApi} from "@/api/singlePage/drugProcurementPlan"
export default {
  name: 'procurementPlanLeft',
  props: ['queueForm','tableHeight','bodyWidth'],
  components: {},
  data() {
    return {
      loading: true,
      tableData: [],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    getDrugProcurementPlanTableMaster(){
      this.loading = true;
      getDrugProcurementPlanTableMasterApi(this.queueForm).then(res => {
        if (res.code === 200){
          this.tableData = res.data;
          this.loading = false;
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">

</style>
