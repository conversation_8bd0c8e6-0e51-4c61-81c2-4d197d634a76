<template>
  <div id="app">
    <!-- 导航栏 -->
    <header class="navbar" :class="{ 'scrolled': isScrolled, 'hidden': !isNavbarVisible }">
      <div class="logo">河南宏力医院官网</div>
      <nav>
        <ul>
          <li><a href="#home">首页</a></li>
          <li><a href="#about">医院简介</a></li>
          <li><a href="#services">服务项目</a></li>
          <li><a href="#contact">联系我们</a></li>
        </ul>
      </nav>
    </header>

    <!-- 大横幅 轮播图 -->
    <section id="hero" class="hero" :style="{ backgroundImage: 'url(' + currentSlide.image + ')' }">
      <div class="hero-content">
        <h1>{{ currentSlide.title }}</h1>
        <p>{{ currentSlide.subtitle }}</p>
      </div>
      <div class="slider">
        <img :src="currentSlide.image" alt="医院轮播图" class="slider-image" />
        <!-- 左右箭头 -->
        <button @click="prevSlide" class="slider-arrow left-arrow">←</button>
        <button @click="nextSlide" class="slider-arrow right-arrow">→</button>
      </div>
    </section>

    <!-- 关于我们 -->
    <section id="about" class="about-us">
      <h2>关于我们</h2>
      <p>河南宏力医院成立于3000年，是一所以患者为中心的综合性医院，致力于提供高质量的医疗服务。</p>
    </section>

    <!-- 服务项目 -->
    <section id="services" class="services">
      <h2>我们的服务</h2>
      <div class="service-item">
        <h3>门诊服务</h3>
        <p>为广大患者提供快速便捷的门诊服务。</p>
      </div>
      <div class="service-item">
        <h3>住院服务</h3>
        <p>提供舒适的住院环境和优质的医疗护理。</p>
      </div>
      <div class="service-item">
        <h3>专家诊疗</h3>
        <p>汇聚顶尖专家，为您的健康保驾护航。</p>
      </div>
    </section>

    <!-- 底部 -->
    <footer class="footer">
      <p>&copy; 2025 河南宏力医院 | 地址：长垣市 | 电话：123-456-7890</p>
      <p>关注我们: <a href="#">微博</a> | <a href="#">微信公众号</a></p>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      currentSlideIndex: 0,
      slides: [
        {
          title: '欢迎光临河南宏力医院',
          subtitle: '致力于为您提供最专业的医疗服务',
          image: require('@/assets/images/1.jpg')
        },
        {
          title: '创新医疗，守护健康',
          subtitle: '领先的技术，温馨的服务',
          image: require('@/assets/images/2.jpg')
        },
        {
          title: '您的健康，我们的责任',
          subtitle: '一流设备，专业医护团队',
          image: require('@/assets/images/3.jpg')
        }
      ],
      isScrolled: false,  // 判断是否滚动超过一定位置
      isNavbarVisible: true,  // 控制导航栏的显示与隐藏
      lastScrollY: 0  // 上次滚动的位置
    };
  },

  mounted() {
    this.startSlider();
    window.addEventListener('scroll', this.handleScroll);  // 监听滚动事件
  },

  beforeDestroy() {
    clearInterval(this.sliderInterval);  // 清除定时器
    window.removeEventListener('scroll', this.handleScroll);  // 移除滚动事件监听
  },

  methods: {
    startSlider() {
      this.sliderInterval = setInterval(() => {
        this.currentSlideIndex = (this.currentSlideIndex + 1) % this.slides.length;
      }, 5000); // 每5秒切换一次
    },

    prevSlide() {
      this.currentSlideIndex = (this.currentSlideIndex - 1 + this.slides.length) % this.slides.length;
    },

    nextSlide() {
      this.currentSlideIndex = (this.currentSlideIndex + 1) % this.slides.length;
    },

    handleScroll() {
      const scrollY = window.scrollY;

      // 判断是否滚动超过100px
      this.isScrolled = scrollY > 100;

      // 判断滚动方向：向上滚动时显示，向下滚动时隐藏
      if (scrollY > this.lastScrollY) {
        // 向下滚动时隐藏导航栏
        this.isNavbarVisible = false;
      } else {
        // 向上滚动时显示导航栏
        this.isNavbarVisible = true;
      }

      // 更新lastScrollY
      this.lastScrollY = scrollY;
    }
  },

  computed: {
    currentSlide() {
      return this.slides[this.currentSlideIndex];
    }
  }
};
</script>

<style scoped>
/* 全局样式 */
body {
  margin: 0;
  font-family: Arial, sans-serif;
  background-color: #f4f6f9;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: rgba(42, 61, 102, 0.8);
  color: white;
  transition: background-color 0.3s ease, padding 0.3s ease, transform 0.3s ease;
}

.navbar.scrolled {
  background-color: #2a3d66;
  padding: 10px 20px;
}

.navbar.hidden {
  transform: translateY(-100%);
  /* 隐藏导航栏，向上移动它的高度 */
}

.navbar .logo {
  font-size: 24px;
  font-weight: bold;
}

.navbar nav ul {
  display: flex;
  list-style: none;
  padding: 0;
}

.navbar nav ul li {
  margin: 0 15px;
}

.navbar nav ul li a {
  color: white;
  text-decoration: none;
  font-size: 18px;
}

.navbar nav ul li a:hover {
  color: #ffca28;
}

/* 大横幅 轮播图 */
.hero {
  position: relative;
  height: 60vh;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  text-align: center;
  background-size: cover;
  background-position: center;
}

/* 设置hero-content的位置和样式，使其位于背景图片之上 */
.hero-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  /* 确保文字层级在图片上 */
  color: #fff;
  padding: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  /* 增加文字阴影，提高可读性 */
}

.hero-content h1 {
  font-size: 50px;
  margin-bottom: 10px;
}

.hero-content p {
  font-size: 24px;
}

/* 轮播图图片的样式 */
.slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.slider-image {
  display: none;
  /* 图片本身不显示 */
}

/* 左右箭头样式 */
.slider-arrow {
  position: absolute;
  top: 50%;
  font-size: 30px;
  color: white;
  background: rgba(0, 0, 0, 0.5);
  border: none;
  padding: 10px;
  cursor: pointer;
  transform: translateY(-50%);
  z-index: 10;
}

.left-arrow {
  left: 10px;
}

.right-arrow {
  right: 10px;
}

/* 关于我们 */
.about-us {
  padding: 60px 20px;
  text-align: center;
  background-color: #fff;
}

.about-us h2 {
  font-size: 36px;
  margin-bottom: 20px;
}

.about-us p {
  font-size: 18px;
  color: #666;
  margin-bottom: 40px;
}

/* 服务项目 */
.services {
  padding: 60px 20px;
  text-align: center;
  background-color: #f9f9f9;
}

.services h2 {
  font-size: 36px;
  margin-bottom: 20px;
}

.service-item {
  width: 30%;
  display: inline-block;
  margin: 10px;
  background-color: #fff;
  padding: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.service-item h3 {
  font-size: 24px;
  margin-bottom: 10px;
}

.service-item p {
  font-size: 16px;
  color: #555;
}

/* 底部 */
.footer {
  background-color: #2a3d66;
  color: white;
  padding: 20px 0;
  text-align: center;
}

.footer a {
  color: #ffca28;
  text-decoration: none;
}

.footer a:hover {
  text-decoration: underline;
}
</style>
