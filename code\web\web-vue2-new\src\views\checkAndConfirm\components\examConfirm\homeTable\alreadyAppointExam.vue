<template>
  <div class="already-master-home">
    <el-dialog
      title="当前患者以下项目已预约时间"
      :visible.sync="alreadyAppointStatus"
      width="70%"
    >
      <div class="already-table">
        <el-table :data="tableData" style="width: 100%" border max-height="500px">
          <el-table-column type="index" align="center" label="序号" width="60"></el-table-column>
          <el-table-column prop="examNo" align="center" label="检查号" width="95"></el-table-column>
          <el-table-column prop="itemName" align="center" label="检查项目"></el-table-column>
          <el-table-column prop="appointDate" align="center" label="预约日期" width="120"></el-table-column>
          <el-table-column prop="appointTime" align="center" label="预约时间" width="120"></el-table-column>
          <el-table-column prop="appointDateTime" align="center" label="操作时间" width="160"></el-table-column>
          <el-table-column prop="createBy" align="center" label="操作人" width="80"></el-table-column>
        </el-table>
      </div>

      <div class="already-button">
        <el-button type="primary" @click="alreadyAppointStatus = false">已阅</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { VerifyPatientAppointMsg } from '@/api/checkAndConfirm/checkCommon'
export default {
  name: 'alreadyAppointExam',
  props: [],
  components: {},
  data() {
    return {
      tableData: [],
      alreadyAppointStatus: false,
    }
  },
  methods: {
    alreadyAppointMonitor(status,data){
      if (status && data){
        if (data.length > 0){
          let appointmentExamNos = [];
          data.forEach(t => {
            if (!t.resultStatus){
              appointmentExamNos.push(t.examNo);
            }
          })
          let formData = {
            examNos: appointmentExamNos
          }
          VerifyPatientAppointMsg(formData).then(res => {
            if (res.data.length > 0){
              this.tableData = res.data;
              this.alreadyAppointStatus = status;
            }
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.already-master-home{

  .already-table{

  }
  .already-button{
    margin-top: 25px;
    display: flex;
    justify-content: center;

    ::v-deep.el-button {
      border-radius: 20px;
      width: 150px;
      font-size: 18px;
    }
  }
}
</style>
