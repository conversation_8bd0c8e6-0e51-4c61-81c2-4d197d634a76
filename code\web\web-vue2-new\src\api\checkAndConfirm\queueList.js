import request from "@/utils/request";

export function getQueueList(data) {
  return request({
    url: "/ReservationQueue/InitializeQueueMessage",
    method: "post",
    data: data,
  });
}

export function DeleteQueueListById(queueId) {
  return request({
    url: "/ReservationQueue/DeleteQueueListById?queueId=" + queueId,
    method: "delete",
  });
}

export function RecoverQueueListById(queueId) {
  return request({
    url: "/ReservationQueue/RecoverQueueListById?queueId=" + queueId,
    method: "put",
  });
}


export function GetPatientThisDayQueueMessage(queueId) {
  return request({
    url: "/ReservationQueue/GetPatientThisDayQueueMessage?queueId=" + queueId,
    method: "get",
  });
}

export function GetPatientMsgByQueueId(queueId){
  return request({
    url: "/ReservationQueue/GetPatientMsgByQueueId?queueId=" + queueId,
    method: "get",
  });
}

export function SaveQueueData(data){
  return request({
    url: "/ReservationQueue/SaveQueueData",
    method: "post",
    data: data
  });
}
