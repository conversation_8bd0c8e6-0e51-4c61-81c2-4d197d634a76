<template>
  <div class="single-master">
    <div class="single-title">急诊科 - 检验项目报告未上传列表</div>
    <div class="single-element">
      <div style="margin: 0 0 0 5px">
        <el-alert
          title="注意：点击报告按钮后，会查询出当前报告以申请时间为中心点1周内的数据进行抓取"
          type="info"
          show-icon
        >
        </el-alert>
      </div>
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="开始时间:">
              <el-date-picker
                v-model="queueForm.beginDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker
                v-model="queueForm.endDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="患者ID:">
              <el-input v-model="queueForm.patientId" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getPageList">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="element-table">
          <el-table :data="tableData" style="width: 100%" border :height="(tableHeight-150)">
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column prop="testno" align="center" label="检验号"></el-table-column>
            <el-table-column prop="itemname" align="center" label="检验项目" width="200"></el-table-column>
            <el-table-column prop="reqdatetime" align="center" label="申请时间" width="200"></el-table-column>
            <el-table-column prop="patientid" align="center" label="患者ID"></el-table-column>
            <el-table-column prop="name" align="center" label="患者姓名"></el-table-column>
            <el-table-column prop="chargetype" align="center" label="医疗类型"></el-table-column>
            <el-table-column prop="sex" align="center" label="性别"></el-table-column>
            <el-table-column align="center" label="操作" width="130">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-s-order" @click="reportClick(scope.row)">报告</el-button>
                <el-button type="text" icon="el-icon-s-order" @click="markUp(scope.row)">补录</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="single-dialog">
      <el-dialog title="检验报告确认" :visible.sync="reportStatus" width="70%">
        <div class="dialog-master element-table" :style="'height:' + (tableHeight - 200) + 'px'">
          <el-table :data="reportData" style="width: 100%" border :height="((tableHeight-210) / 2)">
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column prop="patient_id" align="center" label="患者ID"></el-table-column>
            <el-table-column prop="create_time" align="center" label="报告时间"></el-table-column>
            <el-table-column prop="result_time" align="center" label="结果时间"></el-table-column>
            <el-table-column align="center" label="操作" width="130">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-s-order" @click="resultClick(scope.row)">结果</el-button>
                <el-button type="text" icon="el-icon-check" @click="resultConfirmClick(scope.row)">确定</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-table :data="resultData" style="width: 100%" border :height="((tableHeight-210) / 2)">
            <el-table-column prop="sort" align="center" label="序号"></el-table-column>
            <el-table-column prop="itemName" align="center" label="结果名称"></el-table-column>
            <el-table-column prop="itemNameEng" align="center" label="学名"></el-table-column>
            <el-table-column prop="value" align="center" label="结果">
              <template slot-scope="scope">
                <span v-if="scope.row.status" style="color: #a95812;font-size: 18px;">{{scope.row.value}}</span>
                <span v-else>{{scope.row.value}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="context" align="center" label="范围"></el-table-column>
            <el-table-column prop="unit" align="center" label="单位"></el-table-column>
          </el-table>
        </div>
      </el-dialog>
      <el-dialog title="审核/报告人员选择" :visible.sync="confirmStatus" width="40%">
        <div style="display:flex;justify-content: center;align-items: center;margin-top: 15px;">
          <span>操作人：</span>
          <el-select style="width: 150px;" v-model="confirmData.operator" :filter-method="filterOne" filterable placeholder="请选择">
            <el-option v-for="item in newStaffDict.operator" :key="item.value" :label="item.label"
                       :value="item.label"
            ></el-option>
          </el-select>
        </div>
        <div style="margin-top: 10px; display:flex;justify-content: center;align-items: center;">
          <span>审核人：</span>
          <el-select style="width: 150px;" v-model="confirmData.auditor" :filter-method="filterTwo" filterable placeholder="请选择">
            <el-option v-for="item in newStaffDict.auditor" :key="item.value" :label="item.label"
                       :value="item.label"
            ></el-option>
          </el-select>
        </div>
        <div style="display:flex;justify-content: center;align-items: center;margin-top: 15px;">
          <el-button style="width: 120px;" type="primary" @click="confirmClick">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getThisDateDefault, getThisDateMonthBeginDefault } from '@/utils/DateUtils'
import {
  GetEmergencyDeptLabData,
  GetLabReport,
  GetLabReportResult,
  GetEmergencyDeptStaffDict,
  ReportResultConfirm,
  GetMarkUpList,
} from '@/api/singlePage/labTestEmergencyDept'

export default {
  name: 'index',
  props: [],
  components: {},
  data() {
    return {
      tableHeight: undefined,
      queueForm: {
        beginDate: getThisDateMonthBeginDefault(),
        endDate: getThisDateDefault(),
        patientId: ''
      },
      tableData: [],
      tableRow: {},
      reportStatus: false,
      reportData: [],
      resultData: [],
      confirmData: {
        operator: '',
        auditor: ''
      },
      confirmStatus: false,
      staffDict: {
        auditor: [],
        operator: []
      },
      newStaffDict: {
        auditor: [],
        operator: []
      }
    }
  },
  created() {
    this.getPageList()
    this.handleResize()
    this.getEmergencyDeptStaffDict()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    confirmClick() {
      if (!this.confirmData.operator){
        this.$msgbox.alert(
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
          '请选择审核人!!! ' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
        return
      }
      if (!this.confirmData.auditor){
        this.$msgbox.alert(
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
          '请选择操作人!!! ' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
        return
      }
      ReportResultConfirm(this.confirmData).then(res => {
        if (res.code === 200){
          this.$message.success(res.message);
          this.getPageList();
          this.reportStatus = false;
          this.confirmStatus = false;
        }
      })
    },
    filterOne(query) {
      this.confirmData.operator = query
      if (query !== '' || query) {
        this.newStaffDict.operator = this.staffDict.operator.filter((item) => {
          if (item.label.toString().indexOf(query) > -1 ||
            item.son.toUpperCase().toString().indexOf(query.toUpperCase()) > -1) {
            return true
          }
        })
      } else {
        this.newStaffDict.operator = this.staffDict.operator
      }
    },
    filterTwo(query) {
      this.confirmData.auditor = query
      if (query !== '' || query) {
        this.newStaffDict.auditor = this.staffDict.auditor.filter((item) => {
          if (item.label.toString().indexOf(query) > -1 ||
            item.son.toUpperCase().toString().indexOf(query.toUpperCase()) > -1) {
            return true
          }
        })
      } else {
        this.newStaffDict.auditor = this.staffDict.auditor
      }
    },
    resultConfirmClick(row) {
      if (this.resultData.length === 0) {
        this.$msgbox.alert(
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
          '请先点击想要查看的结果信息，再进行确定 ' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
        return
      }
      this.confirmData = {
        masterData: this.tableRow,
        reportData: row,
        resultData: this.resultData,
        auditor: '',
        operator: ''
      }
      this.confirmStatus = true
    },
    resultClick(row) {
      this.resultData = []
      GetLabReportResult(row.id).then(res => {
        this.resultData = res.data
      })
    },
    reportClick(row) {
      this.reportData = []
      this.resultData = []
      this.confirmData = {};
      this.newStaffDict = this.staffDict;
      this.tableRow = row
      GetLabReport({
        reqDateTime: row.reqdatetime,
        patientId: row.patientid,
        itemCode: row.itemcode,
      }).then(res => {
        if (res.code === 200) {
          let data = res.data
          if (data.length === 0) {
            this.$msgbox.alert(
              '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
              '当前检验项目,咱无相关报告结果' + '</div>',
              '系统提示',
              {
                confirmButtonText: '确定',
                type: 'warning',
                dangerouslyUseHTMLString: true
              }).then(() => {
            })
          } else {
            this.reportData = data
            this.reportStatus = true
          }

        }
      })
    },
    markUp(row){
      this.reportData = []
      this.resultData = []
      this.confirmData = {};
      this.newStaffDict = this.staffDict;
      this.tableRow = row
      GetMarkUpList({
        reqDateTime: row.reqdatetime,
        itemCode: row.itemcode,
      }).then(res => {
        if (res.code === 200) {
          let data = res.data
          if (data.length === 0) {
            this.$msgbox.alert(
              '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
              '当前检验项目,咱无符合补录的信息' + '</div>',
              '系统提示',
              {
                confirmButtonText: '确定',
                type: 'warning',
                dangerouslyUseHTMLString: true
              }).then(() => {
            })
          } else {
            this.reportData = data
            this.reportStatus = true
          }

        }
      })
    },
    getPageList() {
      this.tableData = []
      this.tableRow = {}
      GetEmergencyDeptLabData(this.queueForm).then(res => {
        if (res.code === 200) {
          this.tableData = res.data
        }
      })
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
    },
    getEmergencyDeptStaffDict() {
      GetEmergencyDeptStaffDict().then(res => {
        this.staffDict = res.data
        this.newStaffDict = res.data
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.dialog-master {
  border: 1px solid #3A71A8;
}
</style>
