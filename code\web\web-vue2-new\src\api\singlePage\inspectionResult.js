import request from '@/utils/request'

export function InitializePatientAndDoctorMessage(data) {
  return request({
    url: '/InspectionResult/InitializePatientAndDoctorMessage',
    method: 'get',
    params: data
  })
}

export function KeepAndRegister(data) {
  return request({
    url: '/InspectionResult/KeepAndRegister',
    method: 'post',
    data: data,
  })
}

export function DeleteById(id) {
  return request({
    url: '/InspectionResult/DeleteById?id='+id,
    method: 'delete',
  })
}
