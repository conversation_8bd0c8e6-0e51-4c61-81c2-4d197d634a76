// 会议管理模块
import request from '@/utils/request'

export function MeetingManagementFile(data) {
    return request({
        url: '/MeetingManagement/MeetingManagementFile',
        method: 'post',
        data: data
    })
}

export function MeetingManagementFileOne(data) {
    return request({
        url: '/MeetingManagement/MeetingManagementFileOne',
        method: 'post',
        data: data
    })
}

export function GetMeetingManagementFile(data) {
    return request({
        url: '/MeetingManagement/GetMeetingManagementFile',
        method: 'post',
        data: data
    })
}

export function GetMeetingManagementFileHistorical(data) {
    return request({
        url: '/MeetingManagement/GetMeetingManagementFileHistorical',
        method: 'post',
        data: data
    })
}

export function MeetingManagementDel(fileid) {
    return request({
        url: '/MeetingManagement/MeetingManagementDel?fileid=' + fileid,
        method: 'get'
    })
}

export function MeetingManagementDelHis(fileid) {
    return request({
        url: '/MeetingManagement/MeetingManagementDelHis?fileid=' + fileid,
        method: 'get'
    })
}

export function GetMeetingManagementFileDetail(data) {
    return request({
        url: '/MeetingManagement/GetMeetingManagementFileDetail',
        method: 'post',
        data: data
    })
}