<template>
  <div class="case-table-home">
    <div class="time-table">
      <el-table :data="caseList" :height="styleData.right.table" size="mini" border style="width: 100%">
        <el-table-column align="center" prop="inspectionPurpose" label="检查目的"></el-table-column>
        <el-table-column align="center" prop="clinSymp" label="症状"></el-table-column>
        <el-table-column align="center" prop="physSign" label="体征"></el-table-column>
        <el-table-column align="center" prop="clinDiag" label="临床诊断"></el-table-column>
        <el-table-column align="center" prop="relevantDiag" width="75" label="其他诊断"></el-table-column>
        <el-table-column align="center" prop="relevantLabTest" width="85" label="相关化验结果"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'caseTable',
  props: ['styleData'],
  components: {},
  data() {
    return {
      caseList:[],
    }
  },
  methods: {
    receiveRowTable(data){
      if (!data){
        data = this.$store.getters.reservationRowTable;
      }
      this.caseList = [];
      let cases = {
        clinDiag: data.clinDiag,
        clinSymp: data.clinSymp,
        inspectionPurpose: data.inspectionPurpose,
        physSign: data.physSign,
        relevantDiag: data.relevantDiag,
        relevantLabTest: data.relevantLabTest,
      };
      this.caseList.push(cases);
    },
  }
}
</script>

<style scoped lang="scss">
.case-table-home{
  padding-bottom: 15px;
  .time-table {
    ::v-deep.el-table--medium .el-table__cell {
      padding: 0 !important;
    }

    ::v-deep.el-table .el-table__header-wrapper th,
    .el-table .el-table__fixed-header-wrapper th {
      word-break: break-word;
      background-color: #f8f8f9;
      color: #515a6e;
      height: 30px;
      font-size: 14px;
    }

    ::v-deep.el-table th.el-table__cell > .cell {
      padding: 0;
    }

    ::v-deep.el-table--border .el-table__cell:first-child .cell {
      padding: 0;
    }

    ::v-deep.el-button + .el-button {
      margin-left: 2px;
    }

    ::v-deep.el-table .cell {
      padding: 1px;
    }
  }
}
</style>
