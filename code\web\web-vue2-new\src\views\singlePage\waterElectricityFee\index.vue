<template>
    <div class="single-master">
        <div class="single-title">职工水电费缴费情况查询</div>
        <div class="single-element">
            <div class="element-master">
                <div class="element-form">
                    <el-form :inline="true" :model="queueForm" class="demo-form-inline">
                        <el-form-item label="开始月份:">
                            <el-date-picker v-model="queueForm.beginDate" type="month" value-format="yyyy-MM"
                                format="yyyy-MM" placeholder="选择月">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="终止月份:">
                            <el-date-picker v-model="queueForm.endDate" type="month" value-format="yyyy-MM" format="yyyy-MM"
                                placeholder="选择月">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="姓名:">
                            <el-input v-model="queueForm.ygName" placeholder="请输入患者姓名" clearable></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-radio-group v-model="queueForm.radio" @change="agreeChange">
                                <el-radio :label="1">全部</el-radio>
                                <el-radio :label="2">已缴费</el-radio>
                                <el-radio :label="3">未缴费</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <div class="element-button">
                                <el-button type="primary" @click="timeJudge">查询</el-button>
                                <el-button type="primary" @click="cashPayment">现金缴费</el-button>
                                <el-button type="primary" @click="costModification">未缴费费用修改</el-button>
                                <el-button type="primary" @click="noDisplay">未交费处理为自助机不显示</el-button>
                                <el-button type="primary" @click="delPayFees">删除离职未缴费记录</el-button>
                                <export-excel excelName="水电费数据导出" :excelData="tableDate || []" :columnMap="columnMap">
                                    <el-button type="primary" slot="trigger" style="margin-left: 10%;">导出</el-button>
                                </export-excel>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="element-table">
                    <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 167" highlight-current-row
                        @row-click="handleRowClick">
                        <el-table-column type="index" :index="indexMethod" align="center" label="行号" />
                        <el-table-column prop="statiS_DATE_TIME" align="center" label="抄表月度" />
                        <el-table-column prop="ygname" align="center" label="姓名" />
                        <el-table-column prop="ygnamE_1" align="center" label="姓名" />
                        <el-table-column prop="ygnamE_2" align="center" label="姓名" />
                        <el-table-column prop="ygnamE_3" align="center" label="姓名" />
                        <el-table-column prop="address" align="center" label="住址" width="230px" />
                        <el-table-column prop="colwateR_COST" align="center" label="冷水" />
                        <el-table-column prop="hotwateR_COST" align="center" label="热水" />
                        <el-table-column prop="ammeteR_COST" align="center" label="用电" />
                        <el-table-column prop="maintaiN_COST" align="center" label="维修" />
                        <el-table-column prop="collecT_COSTS" align="center" label="总计" />
                        <el-table-column prop="paymenT_RCPT_NO" align="center" label="缴费状态">
                            <template slot-scope="scope">
                                <el-tag :type="scope.row.paymenT_RCPT_NO !== null ? 'success' : ''" close-transition>
                                    {{ scope.row.paymenT_RCPT_NO | assets_paymentRcpTNo }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column prop="iS_ENABLED" align="center" label="是否显示">
                            <template slot-scope="scope">
                                <span :type="scope.row.iS_ENABLED !== null ? 'success' : ''" close-transition>
                                    {{ scope.row.iS_ENABLED | assets_isEnabled }}
                                </span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="paymenT_RCPT_NO" align="center" label="缴费编号" width="130px" />
                        <el-table-column prop="amount" align="center" label="缴费金额" />
                        <el-table-column prop="balance" align="center" label="E卡通余额" width="90px" />
                        <el-table-column prop="transacT_DATE" align="center" label="缴费日期" width="150px" />
                        <el-table-column prop="anotheR_TYPE" align="center" label="支付方式" />
                        <el-table-column prop="paymenT_YGNAME" align="center" label="付款人姓名" width="90px" />
                    </el-table>
                    <!-- 分页 -->
                    <pagination v-show="total > 0" :limit.sync="queueForm.pageSize" :page.sync="queueForm.pageNum"
                        :total="total" @pagination="timeJudge" />
                </div>
                <!-- 弹框 -->
                <div>
                    <el-dialog title="修改水电费数据" :visible.sync="dialogVisible" width="60%">
                        <div style="font-size: 20px">你正在修改住址为:
                            {{ this.rowClickModel.address }}的费用数据
                        </div>
                        <div style="margin-top: 15px;">
                            <fieldset>
                                <legend class="myLegend">目前的水电费数据:单位(元)</legend>
                                <el-form :inline="true" :model="formDialogMq" size="mini">
                                    <el-form-item label="冷水费用">
                                        <el-input v-model="formDialogMq.colwateR_COST" :disabled="true" />
                                    </el-form-item>
                                    <el-form-item label="维修费用">
                                        <el-input v-model="formDialogMq.maintaiN_COST" :disabled="true" />
                                    </el-form-item>
                                    <el-form-item label="热水费用">
                                        <el-input v-model="formDialogMq.hotwateR_COST" :disabled="true" />
                                    </el-form-item>
                                    <el-form-item label="电费费用">
                                        <el-input v-model="formDialogMq.ammeteR_COST" :disabled="true" />
                                    </el-form-item>
                                    <el-form-item label="总计费用">
                                        <el-input v-model="formDialogMq.collecT_COSTS" :disabled="true" />
                                    </el-form-item>
                                </el-form>
                            </fieldset>
                        </div>
                        <div style="margin-top: 15px;">
                            <fieldset>
                                <legend class="myLegend">修改后的水电费数据:单位(元)</legend>
                                <el-form :inline="true" :model="formDialogXgh" size="mini">
                                    <el-form-item label="冷水费用">
                                        <el-input v-model="formDialogXgh.colwateR_COST" />
                                    </el-form-item>
                                    <el-form-item label="维修费用">
                                        <el-input v-model="formDialogXgh.maintaiN_COST" />
                                    </el-form-item>
                                    <el-form-item label="热水费用">
                                        <el-input v-model="formDialogXgh.hotwateR_COST" />
                                    </el-form-item>
                                    <el-form-item label="电费费用">
                                        <el-input v-model="formDialogXgh.ammeteR_COST" />
                                    </el-form-item>
                                    <el-form-item label="总计费用">
                                        <el-input v-model="formDialogXgh.collecT_COSTS" :disabled="true" />
                                    </el-form-item>
                                    <el-form-item>
                                        <el-button type="primary" @click="calculateTotal">计算</el-button>
                                    </el-form-item>
                                </el-form>
                            </fieldset>
                        </div>
                        <span slot="footer" class="dialog-footer">
                            <el-button type="primary" @click="saveCostModification">确 定</el-button>
                            <el-button type="primary" @click="dialogVisible = false">取 消</el-button>
                        </span>
                    </el-dialog>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    WaterElectricityFeeList, ChangeCashPayment, ChangeCostModification, ChangeNoDisplay, ChangeDelPayFees
} from "@/api/singlePage/waterElectricityFee";
import ExportExcel from "@/components/excel/exportExcel";
export default {
    name: 'waterElectricityFee',
    props: [],
    components: {
        ExportExcel
    },
    data() {
        return {
            queueForm: {
                pageNum: 1,
                pageSize: 50,
                radio: 1,
                beginDate: this.formatDate(new Date()),
                endDate: this.formatDate(new Date()),
            },
            formDialogMq: {},
            formDialogXgh: {
                colwateR_COST: undefined,
                hotwateR_COST: undefined,
                ammeteR_COST: undefined,
                maintaiN_COST: undefined,
                collecT_COSTS: undefined,
            },
            dialogVisible: false,
            rowClickModel: {},
            tableDate: [],
            total: 0,
            tableHeight: undefined,
            columnMap: [
                {
                    label: "抄表月度",
                    key: "statiS_DATE_TIME",
                },
                {
                    label: "姓名",
                    key: "ygname",
                },
                {
                    label: "姓名",
                    key: "ygnamE_1",
                },
                {
                    label: "姓名",
                    key: "ygnamE_2",
                },
                {
                    label: "姓名",
                    key: "ygnamE_3",
                },
                {
                    label: "地址",
                    key: "address",
                },
                {
                    label: "冷水",
                    key: "colwateR_COST",
                },
                {
                    label: "热水",
                    key: "hotwateR_COST",
                },
                {
                    label: "用电",
                    key: "ammeteR_COST",
                },
                {
                    label: "维修",
                    key: "maintaiN_COST",
                },
                {
                    label: "总计",
                    key: "collecT_COSTS",
                },
                {
                    label: "缴费编号",
                    key: "paymenT_RCPT_NO",
                },
                {
                    label: "缴费金额",
                    key: "amount",
                },
                {
                    label: "E卡通余额",
                    key: "balance",
                },
                {
                    label: "缴费日期",
                    key: "transacT_DATE",
                },
                {
                    label: "支付方式",
                    key: "anotheR_TYPE",
                },
                {
                    label: "付款人姓名",
                    key: "paymenT_YGNAME",
                }
            ],
        }
    },

    filters: {
        assets_paymentRcpTNo(row) {
            if (row !== null) {
                return "已缴费";
            } else if (row === null) {
                return "未缴费";
            }
        },
        assets_isEnabled(row) {
            if (row !== null) {
                return "不显示";
            } else if (row === null) {
                return "";
            }
        },
    },

    created() {
        this.timeJudge();
        this.handleResize();
    },

    mounted() {
        window.addEventListener('resize', this.handleResize); // 添加监听器
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize); // 移除监听器
    },

    methods: {
        // 选择月份限制
        timeJudge() {
            const start = new Date(this.queueForm.beginDate);
            const end = new Date(this.queueForm.endDate);
            // 检查开始日期是否大于结束日期
            if (start > end) {
                this.showMessageA('开始月份不能大于或等于结束月份!请选择日期后在进行查询'); // 调用消息提示
            } else {
                // 计算日期差是否超过3个月
                // const diffMonths = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());
                // if (diffMonths > 3) {
                //     this.showMessageA('时间差不能超过3个月'); // 调用消息提示
                // } else {
                // 如果验证通过
                this.showMessageA('查询数据成功'); // 调用消息提示
                this.getList();
                // }
            }
        },

        // 初始化数据
        getList() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            // 初始化数据
            WaterElectricityFeeList(this.queueForm).then(res => {
                this.tableDate = res.data.list;
                this.excelDate = res.data.excelList;
                this.total = res.data.total[0].total;
            }).finally(() => {
                loading.close();
            });
        },

        // 现金缴费按钮操作
        cashPayment() {
            if (JSON.stringify(this.rowClickModel) === '{}') { // 对象为空判断
                this.showMessageA('请选择某一行后在进行操作'); // 消息内容提示
            } else {
                if (this.rowClickModel.paymenT_RCPT_NO !== null) {
                    this.showMessageA('该住户已经使用自助机缴纳过水电费!不允许更改缴费状态!'); // 消息内容提示
                } else {
                    this.$confirm('是否将住址为:' + this.rowClickModel.address + '的水电缴费数据,更新为已缴费!', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        ChangeCashPayment(this.rowClickModel).then(res => {
                            if (res.code === 200) {
                                this.showMessageA('更新缴费状态成功');
                                this.queueForm.radio = '2';
                                this.getList();
                            } else {
                                this.showMessageA('更新缴费状态失败,请联系信息科!');
                                this.queueForm.radio = '2';
                                this.getList();
                            }
                        });
                    }).catch(() => { });
                }
            }
        },

        // 点击计算算出总计费用
        calculateTotal() {
            // 确保所有费用都是数字类型
            let colwateR_COST = parseFloat(this.formDialogXgh.colwateR_COST);
            let hotwateR_COST = parseFloat(this.formDialogXgh.hotwateR_COST);
            let ammeteR_COST = parseFloat(this.formDialogXgh.ammeteR_COST);
            let maintaiN_COST = parseFloat(this.formDialogXgh.maintaiN_COST);
            // 如果转换失败，parseFloat() 会返回 NaN (Not a Number)，你可以添加一些验证逻辑来处理这种情况
            if (isNaN(colwateR_COST) || isNaN(hotwateR_COST) || isNaN(ammeteR_COST) || isNaN(maintaiN_COST)) {
                this.showMessageA('存在非数字费用，请检查输入!或者联系信息科!'); // 消息内容提示
                return;
            } else {
                // 计算总计费用
                this.formDialogXgh.collecT_COSTS = colwateR_COST + hotwateR_COST + ammeteR_COST + maintaiN_COST;
            }
        },

        // 未缴费费用修改按钮操作
        costModification() {
            if (JSON.stringify(this.rowClickModel) === '{}') { // 对象为空判断
                this.showMessageA('请选择某一行后在进行操作'); // 消息内容提示
            } else {
                if (this.rowClickModel.paymenT_RCPT_NO !== null) {
                    this.showMessageA('该住户已经使用自助机缴纳过水电费!不允许更改费用数据!'); // 消息内容提示
                } else {
                    this.$confirm('是否更改住址为:' + this.rowClickModel.address +
                        ',日期为:' + this.rowClickModel.statiS_DATE_TIME + '的水电缴费数据!', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 目前
                        this.formDialogMq.colwateR_COST = this.rowClickModel.colwateR_COST;
                        this.formDialogMq.hotwateR_COST = this.rowClickModel.hotwateR_COST;
                        this.formDialogMq.ammeteR_COST = this.rowClickModel.ammeteR_COST;
                        this.formDialogMq.maintaiN_COST = this.rowClickModel.maintaiN_COST;
                        this.formDialogMq.collecT_COSTS = this.rowClickModel.collecT_COSTS;
                        // 修改后
                        this.formDialogXgh.colwateR_COST = this.rowClickModel.colwateR_COST;
                        this.formDialogXgh.hotwateR_COST = this.rowClickModel.hotwateR_COST;
                        this.formDialogXgh.ammeteR_COST = this.rowClickModel.ammeteR_COST;
                        this.formDialogXgh.maintaiN_COST = this.rowClickModel.maintaiN_COST;
                        this.formDialogXgh.collecT_COSTS = this.rowClickModel.collecT_COSTS;
                        // where判断更新条件
                        this.formDialogXgh.building = this.rowClickModel.building;
                        this.formDialogXgh.unit = this.rowClickModel.unit;
                        this.formDialogXgh.floor = this.rowClickModel.floor;
                        this.formDialogXgh.direction = this.rowClickModel.direction;
                        this.formDialogXgh.statiS_DATE_TIME = this.rowClickModel.statiS_DATE_TIME;
                        this.dialogVisible = true;
                    }).catch(() => { });
                }
            }
        },

        // 删除离职未缴费记录按钮操作
        delPayFees() {
            if (JSON.stringify(this.rowClickModel) === '{}') { // 对象为空判断
                this.showMessageA('请选择某一行后在进行删除操作'); // 消息内容提示
            } else {
                if (this.rowClickModel.paymenT_RCPT_NO !== null) {
                    this.showMessageA('选中记录已缴费，不能删除！'); // 消息内容提示
                } else {
                    this.$confirm('是否确认删除:' + this.rowClickModel.statiS_DATE_TIME + '的记录?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    })
                        .then(() => {
                            return ChangeDelPayFees(this.rowClickModel);
                        })
                        .then(() => {
                            this.timeJudge();
                            this.showMessageA('删除数据成功！'); // 消息内容提示
                        })
                        .catch(() => { });
                }
            }
        },

        // 未缴费费用修改->弹框确定按钮操作
        saveCostModification() {
            let colwateR_COST = parseFloat(this.formDialogXgh.colwateR_COST);
            let hotwateR_COST = parseFloat(this.formDialogXgh.hotwateR_COST);
            let ammeteR_COST = parseFloat(this.formDialogXgh.ammeteR_COST);
            let maintaiN_COST = parseFloat(this.formDialogXgh.maintaiN_COST);
            if (this.formDialogXgh.collecT_COSTS !== colwateR_COST + hotwateR_COST + ammeteR_COST + maintaiN_COST) {
                this.showMessageA('明细项目相加不等于总数!'); // 消息内容提示
            } else {
                ChangeCostModification(this.formDialogXgh).then(res => {
                    if (res.code === 200) {
                        this.showMessageA('未缴费费用修改成功');
                        this.queueForm.radio = 3;
                        this.dialogVisible = false;
                        this.timeJudge();
                    } else {
                        this.showMessageA('未缴费费用修改失败,请联系信息科!');
                        this.dialogVisible = false;
                    }
                });

            }
        },

        // 未缴费处理为自助机不显示按钮操作
        noDisplay() {
            if (JSON.stringify(this.rowClickModel) === '{}') { // 对象为空判断
                this.showMessageA('请选择某一行后在进行操作'); // 消息内容提示
            } else {
                if (this.rowClickModel.paymenT_RCPT_NO !== null) {
                    this.showMessageA('该住户已经使用自助机缴纳过水电费!不允许更改显示状态!'); // 消息内容提示
                } else {
                    this.$confirm('是否将住址为:' + this.rowClickModel.address +
                        ',日期为:' + this.rowClickModel.statiS_DATE_TIME + '的水电缴费数据,更新为自助机不显示状态!', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        ChangeNoDisplay(this.rowClickModel).then(res => {
                            if (res.code === 200) {
                                this.showMessageA('更新缴费显示状态成功');
                                this.queueForm.radio = '3';
                                this.getList();
                            } else {
                                this.showMessageA('更新缴费显示状态失败,请联系信息科!');
                                this.queueForm.radio = '3';
                                this.getList();
                            }
                        });
                    }).catch(() => { });
                }
            }
        },

        // 单击某一行操作
        handleRowClick(row) {
            // 点击的这一行参数赋值
            this.rowClickModel = row;
        },

        // el-radio-group点击变化参数
        agreeChange(data) {
            this.queueForm.radio = data;
        },

        // 序号翻页递增
        indexMethod(index) {
            let nowPage = this.queueForm.pageNum; //当前第几页，根据组件取值即可
            let nowLimit = this.queueForm.pageSize; //当前每页显示几条，根据组件取值即可
            return index + 1 + (nowPage - 1) * nowLimit; // 这里可以理解成一个公式
        },

        // 默认当前时间
        formatDate(date) {
            const year = date.getFullYear()
            const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
            const day = date.getDate().toString().padStart(2, '0')
            return `${year}-${month}-${day}`
        },

        // 提示信息封装
        showMessageA(message) {
            const h = this.$createElement;
            this.$notify({
                title: '提示信息！',
                message: h('i', { style: 'color: teal' }, message)
            });
        },

        // 自定义高度变化更新高度
        handleResize() {
            this.tableHeight = window.innerHeight; // 更新高度数据
        },
    }
}
</script>
  
<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.unit_input {
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 60px;
    font-size: 12px;
    font-weight: 100px;
    margin-left: 3px;
}
</style>

