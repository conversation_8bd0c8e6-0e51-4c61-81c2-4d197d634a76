<template>
    <div :style="maxHeight">
        <el-scrollbar style="height: 98%; width: 100%; overflow-x: hidden">
            <div id='printAll'>
                <h2 style="text-align: center;" class="span1">中华人民共和国传染病报告卡</h2>
                <table width="100%" border="0" cellpadding="0" cellspacing="1">
                    <tr>
                        <td>
                            <span class="span1">卡片编号：</span>
                            <input v-model="fromCard.card_no" type="text" class="unit_input2" required />
                        </td>
                        <td>
                            <span class="span1">报卡类型：</span>
                            <span class="span">
                                <el-checkbox-group v-model="fromCard.card_type" @change="handleCheckManagementSystem">
                                    <el-checkbox label="1" class="checkBoxMargin">1,初次报告</el-checkbox>
                                    <el-checkbox label="2">2,订正报告</el-checkbox>
                                </el-checkbox-group>
                            </span>
                        </td>
                    </tr>
                </table>
                <table border="1px" width="100%" cellspacing="0px" cellpadding="6px" align="center">
                    <tr>
                        <td>
                            <div>
                                <span class="span1">姓名：</span>
                                <input v-model="fromCard.patient_name" type="text" class="unit_input" />
                                <span class="span1">(患者家长姓名：</span>
                                <input v-model="fromCard.parent_name" type="text" class="unit_input" />)
                            </div>
                            <div>
                                <span class="span1">有效证件号：</span>
                                <input v-model="fromCard.id_no" type="text" class="unit_input1" />
                                <span class="span1">性别：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.sex" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">男</el-checkbox>
                                        <el-checkbox label="2">女</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                            </div>
                            <div>
                                <span class="span1">出生日期：</span>
                                <input v-model="date_of_birth_year" type="text" class="unit_input5" />
                                <span class="span1">年</span>
                                <input v-model="date_of_birth_month" type="text" class="unit_input5" />
                                <span class="span1">月</span>
                                <input v-model="date_of_birth_day" type="text" class="unit_input5" />
                                <span class="span1">日</span>
                                <span class="span1">
                                    (如出生日期不详,
                                    实足年龄：
                                </span>
                                <input v-model="fromCard.age" type="text" class="unit_input" />
                                <span class="span1">年龄单位：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.age_unit" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">岁</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">月</el-checkbox>
                                        <el-checkbox label="3">天</el-checkbox>
                                    </el-checkbox-group>
                                </span>)
                            </div>
                            <div>
                                <span class="span1">工作单位(学校)：</span>
                                <input v-model="fromCard.unit_of_work" type="text" class="unit_input1" />
                                <span class="span1">联系电话：</span>
                                <input v-model="fromCard.phone_number" type="text" class="unit_input2" />
                            </div>
                            <div>
                                <span class="span1">病人属于：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.region" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">本县区</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">本市其他县区</el-checkbox>
                                        <el-checkbox label="3" class="checkBoxMargin">本省其他地市</el-checkbox>
                                        <el-checkbox label="4" class="checkBoxMargin">外省</el-checkbox>
                                        <el-checkbox label="5" class="checkBoxMargin">港澳台</el-checkbox>
                                        <el-checkbox label="6">外籍</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                            </div>
                            <div>
                                <span class="span1">现住址(详情)：</span>
                                <input v-model="fromCard.address_province" type="text" class="unit_input" />
                                <span class="span1">省</span>
                                <input v-model="fromCard.address_city" type="text" class="unit_input" />
                                <span class="span1">市</span>
                                <input v-model="fromCard.address_county" type="text" class="unit_input" />
                                <span class="span1">县(区)</span>
                                <input v-model="fromCard.address_town" type="text" class="unit_input" />
                                <span class="span1">乡(镇,街道)</span>
                                <input v-model="fromCard.address_village" type="text" class="unit_input" />
                                <span class="span1">村</span>
                                <input v-model="fromCard.address_number" type="text" class="unit_input" />
                                <span class="span1">(门牌号)</span>
                            </div>
                            <div>
                                <span class="span1">人群分类：</span>
                            </div>
                            <div>
                                <el-checkbox-group v-model="fromCard.occupation" @change="handleCheckManagementSystem">
                                    <el-checkbox label="1" class="checkBoxMargin">幼托儿童</el-checkbox>
                                    <el-checkbox label="2" class="checkBoxMargin">散居儿童</el-checkbox>
                                    <el-checkbox label="3" class="checkBoxMargin">学生(大中小学)</el-checkbox>
                                    <el-checkbox label="4" class="checkBoxMargin">教师</el-checkbox>
                                    <el-checkbox label="5" class="checkBoxMargin">保育员及保姆</el-checkbox>
                                    <el-checkbox label="6" class="checkBoxMargin">餐饮食品业</el-checkbox>
                                    <el-checkbox label="7" class="checkBoxMargin">商务服务</el-checkbox>
                                    <el-checkbox label="8" class="checkBoxMargin">医务人员</el-checkbox>
                                    <el-checkbox label="9" class="checkBoxMargin">工人</el-checkbox>
                                    <el-checkbox label="10" class="checkBoxMargin">民工</el-checkbox>
                                    <el-checkbox label="11" class="checkBoxMargin">农民</el-checkbox>
                                    <el-checkbox label="12" class="checkBoxMargin">牧民</el-checkbox>
                                    <el-checkbox label="13" class="checkBoxMargin">渔(船)民</el-checkbox>
                                    <el-checkbox label="14" class="checkBoxMargin">干部职员</el-checkbox>
                                    <el-checkbox label="15" class="checkBoxMargin">离退人员</el-checkbox>
                                    <el-checkbox label="16" class="checkBoxMargin">家务及待业</el-checkbox>
                                    <el-checkbox label="17">其它</el-checkbox>
                                    <span style="margin-left: -29px;">
                                        <input v-model="fromCard.occupation_remark" type="text" class="unit_input" />
                                    </span>
                                    <el-checkbox label="18" class="checkBoxMargin">不详</el-checkbox>
                                </el-checkbox-group>
                            </div>
                            <div>
                                <span class="span1">病例分类：</span>
                            </div>
                            <div>
                                <span class="span1">(1)</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.desease_type_1"
                                        @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">疑似病例</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">临床诊断病例</el-checkbox>
                                        <el-checkbox label="3" class="checkBoxMargin">确诊病例</el-checkbox>
                                        <el-checkbox label="4" class="checkBoxMargin">病原携带者</el-checkbox>
                                        <el-checkbox label="5">阳性检测</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                            </div>
                            <div>
                                <span class="span1">(2)</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.desease_type_2"
                                        @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">急性</el-checkbox>
                                        <el-checkbox label="2">慢性(乙型肝炎*,血吸虫病,丙肝)</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                            </div>
                            <div>
                                <span class="span1">发病日期：</span>
                                <input v-model="onset_date_year" type="text" class="unit_input5" />
                                <span class="span1">年</span>
                                <input v-model="onset_date_month" type="text" class="unit_input5" />
                                <span class="span1">月</span>
                                <input v-model="onset_date_day" type="text" class="unit_input5" />
                                <span class="span1">日</span>
                            </div>
                            <div>
                                <span class="span1">诊断日期：</span>
                                <input v-model="diagnosis_date_year" type="text" class="unit_input5" />
                                <span class="span1">年</span>
                                <input v-model="diagnosis_date_month" type="text" class="unit_input5" />
                                <span class="span1">月</span>
                                <input v-model="diagnosis_date_day" type="text" class="unit_input5" />
                                <span class="span1">日</span>
                                <input v-model="diagnosis_date_hour" type="text" class="unit_input5" />
                                <span class="span1">时</span>
                                <span class="span1" style="margin-left: 10px;">死亡日期：</span>
                                <input v-model="death_date_year" type="text" class="unit_input5" />
                                <span class="span1">年</span>
                                <input v-model="death_date_month" type="text" class="unit_input5" />
                                <span class="span1">月</span>
                                <input v-model="death_date_day" type="text" class="unit_input5" />
                                <span class="span1">日</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span class="span1">甲类传染病：</span>
                            <span class="span">
                                <el-checkbox-group v-model="fromCard.infection_desease_a"
                                    @change="handleCheckManagementSystem">
                                    <el-checkbox label="1" class="checkBoxMargin">鼠疫</el-checkbox>
                                    <el-checkbox label="2">霍乱</el-checkbox>
                                </el-checkbox-group>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span class="span1">乙类传染病：</span>
                            <el-checkbox-group v-model="fromCard.infection_desease_b" @change="handleCheckManagementSystem">
                                <div class="flex-container">
                                    <div class="flex-item">
                                        <el-checkbox label="1" class="checkBoxMargin">传染性非典型肺炎 、艾滋病(</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">艾滋病病人、</el-checkbox>
                                        <el-checkbox label="3" class="checkBoxMargin">HIV)、病毒性肝炎(</el-checkbox>
                                        <el-checkbox label="4" class="checkBoxMargin">甲型</el-checkbox>
                                        <el-checkbox label="5" class="checkBoxMargin">乙型</el-checkbox>
                                        <el-checkbox label="6" class="checkBoxMargin">丙型</el-checkbox>
                                        <el-checkbox label="7" class="checkBoxMargin">丁肝</el-checkbox>
                                        <el-checkbox label="8" class="checkBoxMargin">戊型</el-checkbox>
                                        <el-checkbox label="9" class="checkBoxMargin">未分型)、</el-checkbox>
                                        <el-checkbox label="10" class="checkBoxMargin">脊髓灰质炎、</el-checkbox>
                                        <el-checkbox label="11" class="checkBoxMargin">人感染高致病性禽流感、</el-checkbox>
                                        <el-checkbox label="12" class="checkBoxMargin">麻疹、</el-checkbox>
                                        <el-checkbox label="13" class="checkBoxMargin">流行性出血热、</el-checkbox>
                                        <el-checkbox label="14" class="checkBoxMargin">狂犬病、</el-checkbox>
                                        <el-checkbox label="15" class="checkBoxMargin">流行性乙型脑炎、</el-checkbox>
                                        <el-checkbox label="16" class="checkBoxMargin">登革热、炭疽、(</el-checkbox>
                                        <el-checkbox label="17" class="checkBoxMargin">肺炭疽</el-checkbox>
                                        <el-checkbox label="18" class="checkBoxMargin">皮肤炭疽</el-checkbox>
                                        <el-checkbox label="19" class="checkBoxMargin">分型)、痢疾(</el-checkbox>
                                        <el-checkbox label="20" class="checkBoxMargin">細菌性</el-checkbox>
                                        <el-checkbox label="21" class="checkBoxMargin">阿米巴性)、肺结核</el-checkbox>
                                        <el-checkbox label="46" class="checkBoxMargin">利福平耐药、</el-checkbox>
                                        <el-checkbox label="22" class="checkBoxMargin">病原学阳性</el-checkbox>
                                        <el-checkbox label="24" class="checkBoxMargin">病原学阴性</el-checkbox>
                                        <el-checkbox label="25" class="checkBoxMargin">无病原学结果)、伤寒(</el-checkbox>
                                        <el-checkbox label="26" class="checkBoxMargin">伤寒</el-checkbox>
                                        <el-checkbox label="27" class="checkBoxMargin">副伤寒)、</el-checkbox>
                                        <el-checkbox label="28" class="checkBoxMargin">流行性脑脊髓膜炎、</el-checkbox>
                                        <el-checkbox label="29" class="checkBoxMargin">百日咳、</el-checkbox>
                                        <el-checkbox label="30" class="checkBoxMargin">白喉、</el-checkbox>
                                        <el-checkbox label="31" class="checkBoxMargin">新生儿破伤风、</el-checkbox>
                                        <el-checkbox label="32" class="checkBoxMargin">猩红热、</el-checkbox>
                                        <el-checkbox label="33" class="checkBoxMargin">布鲁氏菌病、</el-checkbox>
                                        <el-checkbox label="34" class="checkBoxMargin">淋病、梅毒(</el-checkbox>
                                        <el-checkbox label="35" class="checkBoxMargin">I期</el-checkbox>
                                        <el-checkbox label="36" class="checkBoxMargin">II期</el-checkbox>
                                        <el-checkbox label="37" class="checkBoxMargin">III期</el-checkbox>
                                        <el-checkbox label="38" class="checkBoxMargin">胎传</el-checkbox>
                                        <el-checkbox label="39" class="checkBoxMargin">隐性)</el-checkbox>
                                        <el-checkbox label="40" class="checkBoxMargin">钩端螺旋体病、</el-checkbox>
                                        <el-checkbox label="41" class="checkBoxMargin">血吸虫病、疟疾(</el-checkbox>
                                        <el-checkbox label="42" class="checkBoxMargin">间日疟</el-checkbox>
                                        <el-checkbox label="43" class="checkBoxMargin">恶性疟</el-checkbox>
                                        <el-checkbox label="44" class="checkBoxMargin">未分型)</el-checkbox>
                                        <el-checkbox label="45"
                                            class="checkBoxMargin">人感染H7N9禽流感、新型冠状病毒感染(临床严重程度</el-checkbox>
                                        <el-checkbox label="47" class="checkBoxMargin">无症状感染者、</el-checkbox>
                                        <el-checkbox label="48" class="checkBoxMargin">轻型、</el-checkbox>
                                        <el-checkbox label="49" class="checkBoxMargin">中型、</el-checkbox>
                                        <el-checkbox label="50" class="checkBoxMargin">重型、</el-checkbox>
                                        <el-checkbox label="51" class="checkBoxMargin">危重型)</el-checkbox>
                                        <el-checkbox label="52">猴痘</el-checkbox>
                                    </div>
                                </div>
                            </el-checkbox-group>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span class="span1">丙类传染病：</span>
                            <el-checkbox-group v-model="fromCard.infection_desease_c" @change="handleCheckManagementSystem">
                                <el-checkbox label="1" class="checkBoxMargin">流行性感冒、</el-checkbox>
                                <el-checkbox label="2" class="checkBoxMargin">流行性腮腺炎、</el-checkbox>
                                <el-checkbox label="3" class="checkBoxMargin">风疹、</el-checkbox>
                                <el-checkbox label="4" class="checkBoxMargin">急性出血性结膜炎、</el-checkbox>
                                <el-checkbox label="5" class="checkBoxMargin">麻风病、</el-checkbox>
                                <el-checkbox label="6" class="checkBoxMargin">流行性和地方性斑疹伤寒、</el-checkbox>
                                <el-checkbox label="7" class="checkBoxMargin">黑热病、</el-checkbox>
                                <el-checkbox label="8" class="checkBoxMargin">包虫病、</el-checkbox>
                                <el-checkbox label="9" class="checkBoxMargin">丝虫病、</el-checkbox>
                                <el-checkbox label="10" class="checkBoxMargin">除霍乱、细菌性和阿米巴性痢疾、伤寒和副伤寒以外的感染性腹泻病、</el-checkbox>
                                <el-checkbox label="11">手足口病</el-checkbox>
                            </el-checkbox-group>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span class="span1">其他法定管理以及重点监测传染病</span>
                            <el-select v-model="fromCard.infection_desease_other" size="mini" placeholder=""
                                class="custom-select">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="span1">性病报告附加栏</div>
                            <div>
                                <span class="span1">婚姻状况：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.vd_ms" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">未婚</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">已婚</el-checkbox>
                                        <el-checkbox label="3" class="checkBoxMargin">离异或者丧偶</el-checkbox>
                                        <el-checkbox label="4">不详</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                            </div>
                            <div>
                                <span class="span1">文化程度：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.vd_sc" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">文盲</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">小学</el-checkbox>
                                        <el-checkbox label="3" class="checkBoxMargin">初中</el-checkbox>
                                        <el-checkbox label="4" class="checkBoxMargin">高中或中专</el-checkbox>
                                        <el-checkbox label="5" class="checkBoxMargin">大专</el-checkbox>
                                        <el-checkbox label="6" class="checkBoxMargin">大学</el-checkbox>
                                        <el-checkbox label="7">硕士及以上</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                            </div>
                            <div>
                                <!-- vd_ch, --性病报告附加栏-接触史（可多选，选项英文逗号间隔） -->
                                <span class="span1">接触史(可多选)：</span>
                                <el-checkbox-group v-model="vd_ch">
                                    <el-checkbox label="1" class="checkBoxMargin">注射病毒史</el-checkbox>
                                    <el-checkbox label="2" class="checkBoxMargin">非婚异性性接触史</el-checkbox>
                                    <el-checkbox label="3" class="checkBoxMargin">配偶/固定性伴阳性</el-checkbox>
                                    <el-checkbox label="4" class="checkBoxMargin">男男性行为史</el-checkbox>
                                    <el-checkbox label="5" class="checkBoxMargin">献血(浆)史</el-checkbox>
                                    <el-checkbox label="6" class="checkBoxMargin">输血/血制品史</el-checkbox>
                                    <el-checkbox label="7" class="checkBoxMargin">母亲阳性</el-checkbox>
                                    <el-checkbox label="8" class="checkBoxMargin">职业暴露史</el-checkbox>
                                    <el-checkbox label="9" class="checkBoxMargin">手术史</el-checkbox>
                                    <el-checkbox label="10">其他</el-checkbox>
                                    <span style="margin-left: -5px;">
                                        <input v-model="fromCard.vd_ch_other" type="text" class="unit_input" />
                                    </span>
                                    <el-checkbox label="11" class="checkBoxMargin">不详</el-checkbox>
                                </el-checkbox-group>
                            </div>
                            <div>
                                <span class="span1">感染途径：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.vd_ri" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">注射毒品</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">异性传播</el-checkbox>
                                        <el-checkbox label="3" class="checkBoxMargin">同性传播</el-checkbox>
                                        <el-checkbox label="4" class="checkBoxMargin">性接触+注射毒品</el-checkbox>
                                        <el-checkbox label="5" class="checkBoxMargin">采血（浆）</el-checkbox>
                                        <el-checkbox label="6" class="checkBoxMargin">输血/血制品</el-checkbox>
                                        <el-checkbox label="7" class="checkBoxMargin">母婴传播</el-checkbox>
                                        <el-checkbox label="8" class="checkBoxMargin">职业暴露</el-checkbox>
                                        <el-checkbox label="9" class="checkBoxMargin">其他</el-checkbox>
                                        <span>
                                            <input v-model="fromCard.vd_ri_other" type="text" class="unit_input" />
                                        </span>
                                        <el-checkbox label="10">不详</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                            </div>
                            <div>
                                <span class="span1">样本来源：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.vd_ss" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">术前检测</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">受血（制品）前检测</el-checkbox>
                                        <el-checkbox label="3" class="checkBoxMargin">性病门诊</el-checkbox>
                                        <el-checkbox label="4" class="checkBoxMargin">其他就诊者检测</el-checkbox>
                                        <el-checkbox label="5" class="checkBoxMargin">婚前检测</el-checkbox>
                                        <el-checkbox label="6" class="checkBoxMargin">孕产期检查</el-checkbox>
                                        <el-checkbox label="7" class="checkBoxMargin">其他</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                                <span>
                                    <input v-model="fromCard.vd_ss_other" type="text" class="unit_input" />
                                </span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="span1">乙肝病例附加栏</div>
                            <div>
                                <span class="span1">HbsAg阳性时间：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.hb_pt" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">>6个月</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">6个月内由阴性转为阳性</el-checkbox>
                                        <el-checkbox label="3">既往未检测或结果不详</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                                <span class="span1" style="margin-left: 20px;">本次ALT：</span>
                                <input v-model="fromCard.hb_pt_alt" type="text" class="unit_input" />
                                <span class="span1">U/L</span>
                            </div>
                            <div>
                                <span class="span1">首次出现乙肝症状和体征时间：</span>
                                <input v-model="hb_date_year" type="text" class="unit_input5" />
                                <span class="span1">年</span>
                                <input v-model="hb_date_month" type="text" class="unit_input5" />
                                <span class="span1">月</span>
                                <span style="margin-left: 20px;" class="span1">抗-HBc igM1∶100检测结果：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.hb_igm" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">阳性</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">阴性</el-checkbox>
                                        <el-checkbox label="3" class="checkBoxMargin">未测</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                            </div>
                            <div>
                                <span class="span1">肝穿检测结果：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.hb_lp" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">急性病变</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">慢性病变</el-checkbox>
                                        <el-checkbox label="3">未测</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                                <span class="span1">恢复期血清HbsAg阴转,抗HBs阳转：</span>
                                <span class="span">
                                    <el-checkbox-group v-model="fromCard.hb_cp" @change="handleCheckManagementSystem">
                                        <el-checkbox label="1" class="checkBoxMargin">是</el-checkbox>
                                        <el-checkbox label="2" class="checkBoxMargin">否</el-checkbox>
                                        <el-checkbox label="3">未测</el-checkbox>
                                    </el-checkbox-group>
                                </span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>
                                <span class="span1">订正病名：</span>
                                <input v-model="fromCard.desease_name" type="text" class="unit_input6" />
                                <span class="span1">退卡原因：</span>
                                <input v-model="fromCard.invalid_reason" type="text" class="unit_input6" />
                                <span class="span1">报告单位：</span>
                                <input v-model="fromCard.hospital_name" type="text" class="unit_input7" />
                            </div>
                            <div>
                                <span class="span1">联系电话：</span>
                                <input v-model="fromCard.hospital_tel" type="text" class="unit_input2" />
                                <span class="span1">填卡医生：</span>
                                <input v-model="fromCard.doctor_name" type="text" class="unit_input2"
                                    style="display:none" />
                                <img :src="fromCard.doctor_nameqm" alt="" style="width: 7%;height: 25px;" />
                                <span class="span1">填卡日期：</span>
                                <input v-model="create_date_year" type="text" class="unit_input5" />
                                <span class="span1">年</span>
                                <input v-model="create_date_month" type="text" class="unit_input5" />
                                <span class="span1">月</span>
                                <input v-model="create_date_day" type="text" class="unit_input5" />
                                <span class="span1">日</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span class="span1">备注：</span>
                            <input v-model="fromCard.remarks" type="text" class="unit_input1" />
                        </td>
                    </tr>
                </table>
            </div>
        </el-scrollbar>
        <!-- 按钮 -->
        <div>
            <el-form :inline="true" :model="queueForm" class="demo-form-inline">
                <el-form-item>
                    <input v-model="fromCard.doctor_name" type="text" class="unit_input3" />
                    <el-input v-model="queueForm.patientId" placeholder="请输入患者id" clearable size="mini"
                        style="width: 120px;" />
                </el-form-item>
                <el-form-item>
                    <div class="element-button">
                        <el-button type="success" size="mini" v-print="print">打印</el-button>
                        <el-button type="success" size="mini" @click="getChangeState">更改状态</el-button>
                        <el-button type="success" size="mini" @click="getApproved">审核通过</el-button>
                        <el-button type="success" size="mini" @click="getRejectModification">驳回修改</el-button>
                        <el-button type="success" size="mini" @click="saveRejectModification">保存</el-button>
                        <el-button type="success" size="mini" @click="getDescribe">填卡说明</el-button>
                        <el-button type="success" size="mini" @click="getPulmonaryTuberculosis">肺结核转诊单</el-button>
                    </div>
                </el-form-item>
            </el-form>
        </div>
        <!-- 填卡说明弹出框 -->
        <el-dialog :visible.sync="dialogDescribe" :title="titleDescribe" width="60%">
            <div style="background: #ffff002e;">
                <div style="text-align: center;font-size:19px;font-weight: bold">
                    《中华人民共和国传染病报告卡》填卡说明
                </div>
                <div>
                    <span class="fontSize">卡片编码:</span>
                    由报告单位自行编制填写。
                </div>
                <div>
                    <span class="fontSize">姓名:</span>
                    填写患者或献血员的名字，姓名应该和身份证上的姓名一致。
                </div>
                <div>
                    <span class="fontSize">家长姓名:</span>
                    14岁及以下的患儿要求填写患者家长姓名。
                </div>
                <div>
                    <span class="fontSize">有效证件号:</span>
                    必须填写有效证件号，包括居民身份证号、护照、军官证、居民健康卡
                </div>
                <div>
                    社会保障卡、新农合医疗卡。尚未获得身份识别号码的人员用特定编码标识。
                </div>
                <div>
                    <span class="fontSize">性别:</span>
                    在相应的性别前打。
                </div>
                <div>
                    <span class="fontSize">出生日期:</span>
                    出生日期与年龄栏只要选择一栏填写即可，不必同时填报出生日期和年龄。
                </div>
                <div>
                    <span class="fontSize">实足年龄:</span>
                    对出生日期不详的用户填写年龄。
                </div>
                <div>
                    <span class="fontSize">年龄单位:</span>
                    对于新生儿和只有月龄的儿童，注意选择年龄单位为天或月。
                </div>
                <div>
                    <span class="fontSize">工作单位(学校):</span>
                    填写患者的工作单位。学生、幼托儿童须详细填写所在学校及班级名称。
                </div>
                <div>
                    <span class="fontSize">联系电话:</span>
                    填写患者的联系方式。
                </div>
                <div>
                    <span class="fontSize">病例属于:</span>
                    在相应的类别前打，。用于标识病人现住地址与就诊医院所在地区的关系
                </div>
                <div>
                    <span class="fontSize">现住地址:</span>
                    至少须详细填写到乡镇(街道)。现住址的填写，原则是指病人发病时的居
                </div>
                <div>
                    住地，不是户籍所在地址。如病人不能提供本人现住地址，则填写报告单位地址。
                </div>
                <div>
                    <span class="fontSize">职业:</span>
                    在相应的职业名前打~。
                </div>
                <div>
                    <span class="fontSize">病例分类:</span>
                    在相应的类别前打√。
                </div>
                <div>
                    <span class="fontSize">发病日期:</span>
                    本次发病日期;病原携带者填初检日期或就诊时间;采供血机构报告填写献血者献血日期。
                </div>
                <div>
                    <span class="fontSize">诊断日期:</span>
                    本次诊断日期，需填写至小时;采供血机构填写确认实验日期。
                </div>
                <div>
                    <span class="fontSize">死亡日期:</span>
                    病例的死亡时间。
                </div>
                <div>
                    <span class="fontSize">疾病名称:</span>
                    在作出诊断的病名前打√。其中利福平耐药结核病指检测发现的对利福平耐
                </div>
                <div>
                    药的患者，包含利福平单耐药、耐多药和其他利福平耐药等。
                </div>
                <div>
                    <span class="fontSize">其他法定管理以及重点监测传染病:</span>
                    填写纳入报告管理的其他传染病病种名称。其中结
                </div>
                <div>
                    核性胸膜炎归入肺结核分类统计，不再报告到“其他法定管理以及重点监测传染病”中。
                </div>
                <div>
                    <span class="fontSize">订正病名:</span>
                    订正报告填写订正前的病名
                </div>
                <div>
                    <span class="fontSize">退卡原因:</span>
                    填写卡片填报不合格的原因。
                </div>
                <div>
                    <span class="fontSize">报告单位:</span>
                    填写报告传染病的单位。
                </div>
                <div>
                    <span class="fontSize">填卡医生:</span>
                    填写传染病报告卡的医生姓名:
                </div>
                <div>
                    <span class="fontSize">填卡日期:</span>
                    填写本卡日期。
                </div>
                <div>
                    <span class="fontSize">备注:</span>
                    用户可填写文字信息，如最终确诊非法定报告的传染病的病名等。
                </div>
                <div>
                    <span class="fontSize">注:</span>
                    报告卡带“*”部份为必填项目。
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    DetailReportCardReview,
    ChangeStateButton,
    ApprovedButton,
    RejectModificationButton,
    PulmonaryTuberculosisButton,
    JudgeCountReportCardReview,
    SaveReportCardReview,
    UpdateReportCardReview
} from "@/api/singlePage/reportCardReview";
export default {
    name: 'dialogOne',
    // 继承父组件弹框中的高度
    props: ['rowData', 'maxHeight'],
    data() {
        return {
            dialogDescribe: false, // 填卡说明弹框
            titleDescribe: '填卡说明',
            vd_ch: [], // 性病报告附加栏-接触史(可多选)->单独进行处理
            queueForm: {
                ls_type: "", //按钮代表的类型1:保存;2:审核通过;3:驳回修改;4:打印
            },
            fromCard: {
                card_type: [], // 报卡类别
                sex: [], // 性别
                age_unit: [], // 年龄
                region: [], // 病人属于
                occupation: [], // 人群分类
                desease_type_1: [], // 病例分类1
                desease_type_2: [], // 病例分类2
                infection_desease_a: [], // 甲类传染病
                infection_desease_b: [], // 乙类传染病
                infection_desease_c: [], // 丙类传染病
                vd_ms: [], // 性病报告附加栏-婚姻状况
                vd_sc: [], // 性病报告附加栏-文化程度
                vd_ri: [], // 性病报告附加栏-感染途径
                vd_ss: [], // 性病报告附加栏-样本来源
                vd_ch: [], // 性病报告附加栏-接触史(可多选)
                hb_pt: [], // 乙肝病例附加栏-HbsAg阳性时间
                hb_igm: [], // 抗-HBc 1gM1∶100检测结果
                hb_lp: [], // 肝穿检测结果
                hb_cp: [], // 恢复期血清HBsAg阴转
                date_of_birth: "", // 出生日期
                onset_date: "", // 发病日期
                diagnosis_date: "", // 诊断日期
                death_date: "", // 死亡日期
                create_date: "", // 填卡日期
            },
            print: {
                id: 'printAll',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {
                }, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {
                }, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeEntryIframe() {
                    const cells = document.querySelectorAll('.cell');
                    [].slice.call(cells).forEach((item) => {
                        // 为了让表格中的内容自动换行，不需要的话可以删掉
                        item.style.whiteSpace = 'pre-wrap'
                    })
                },
                openCallback() {
                }, // 调用打印之后的回调事件
                closeCallback() {
                }, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: ''
            },
            options: [
                {
                    label: '生殖道沙眼衣原体感染',
                    value: '生殖道沙眼衣原体感染'
                },
                {
                    label: '生殖器疱疹',
                    value: '生殖器疱疹'
                },
                {
                    label: '尖锐湿疣',
                    value: '尖锐湿疣'
                },
                {
                    label: '水痘',
                    value: '水痘'
                },
                {
                    label: 'AFP',
                    value: 'AFP'
                },
            ]
        }
    },

    computed: {
        // 出生日期
        datePartsA() {
            if (this.fromCard.date_of_birth) {
                // 分割年月日和时分秒
                const [datePartx, timePart] = this.fromCard.date_of_birth.split(' ');
                // 分割年月日
                return datePartx.split('-');
            }
            return []; // 如果诊断日期为空，返回空数组
        },
        // 读取年份
        date_of_birth_year: {
            get() {
                const parts = this.datePartsA;
                return parts.length > 0 ? parts[0] : null; // 如果parts存在，返回年份，否则返回null
            },
            set(value) {
                this.setDateOfBirth('year', value);
            }
        },
        // 读取月份
        date_of_birth_month: {
            get() {
                const parts = this.datePartsA;
                return parts.length > 1 ? parts[1] : null; // 如果parts存在，返回月份，否则返回null
            },
            set(value) {
                this.setDateOfBirth('month', value);
            }
        },
        // 读取日期
        date_of_birth_day: {
            get() {
                const parts = this.datePartsA;
                return parts.length > 2 ? parts[2] : null; // 如果parts存在，返回日期，否则返回null
            },
            set(value) {
                this.setDateOfBirth('day', value);
            }
        },

        // 发病日期
        datePartsB() {
            if (this.fromCard.onset_date) {
                // 分割年月日和时分秒
                const [datePartx, timePart] = this.fromCard.onset_date.split(' ');
                // 分割年月日
                return datePartx.split('-');
            }
            return []; // 如果诊断日期为空，返回空数组
        },
        // 读取年份
        onset_date_year: {
            get() {
                const parts = this.datePartsB;
                return parts.length > 0 ? parts[0] : null; // 如果parts存在，返回年份，否则返回null
            },
            set(value) {
                this.setOnsetDate('year', value);
            }
        },
        // 读取月份
        onset_date_month: {
            get() {
                const parts = this.datePartsB;
                return parts.length > 1 ? parts[1] : null; // 如果parts存在，返回月份，否则返回null
            },
            set(value) {
                this.setOnsetDate('month', value);
            }
        },
        // 读取日期
        onset_date_day: {
            get() {
                const parts = this.datePartsB;
                return parts.length > 2 ? parts[2] : null; // 如果parts存在，返回日期，否则返回null
            },
            set(value) {
                this.setOnsetDate('day', value);
            }
        },

        // 诊断日期
        datePartsC() {
            if (this.fromCard.diagnosis_date) {
                // 分割年月日和时分秒
                const [datePartx, timePart] = this.fromCard.diagnosis_date.split(' ');
                // 分割年月日
                const dateParts = datePartx.split('-');
                // 分割时分秒
                const timeParts = timePart.split(':');
                return { dateParts, timeParts };
            }
            return { dateParts: [], timeParts: [] }; // 如果诊断日期为空，返回空对象
        },
        // 读取年份
        diagnosis_date_year: {
            get() {
                const { dateParts } = this.datePartsC;
                return dateParts.length > 0 ? dateParts[0] : null; // 如果dateParts存在，返回年份，否则返回null
            },
            set(value) {
                this.setDiagnosisDate('year', value);
            }
        },
        // 读取月份
        diagnosis_date_month: {
            get() {
                const { dateParts } = this.datePartsC;
                return dateParts.length > 1 ? dateParts[1] : null; // 如果dateParts存在，返回年份，否则返回null
            },
            set(value) {
                this.setDiagnosisDate('month', value);
            }
        },
        // 读取日期
        diagnosis_date_day: {
            get() {
                const { dateParts } = this.datePartsC;
                return dateParts.length > 2 ? dateParts[2] : null; // 如果dateParts存在，返回年份，否则返回null
            },
            set(value) {
                this.setDiagnosisDate('day', value);
            }
        },
        // 读取小时
        diagnosis_date_hour: {
            get() {
                const { timeParts } = this.datePartsC;
                return timeParts.length > 1 ? timeParts[0] : null; // 如果timeParts存在，返回小时，否则返回null
            },
            set(value) {
                this.setDiagnosisDate('hour', value);
            }
        },

        // 死亡日期
        datePartsD() {
            if (this.fromCard.death_date) {
                // 分割年月日和时分秒
                const [datePartx, timePart] = this.fromCard.death_date.split(' ');
                // 分割年月日
                return datePartx.split('-');
            }
            return []; // 如果诊断日期为空，返回空数组
        },
        // 读取年份
        death_date_year: {
            get() {
                const parts = this.datePartsD;
                return parts.length > 0 ? parts[0] : null; // 如果parts存在，返回年份，否则返回null
            },
            set(value) {
                this.setDeathDate('year', value);
            }
        },
        // 读取月份
        death_date_month: {
            get() {
                const parts = this.datePartsD;
                return parts.length > 1 ? parts[1] : null; // 如果parts存在，返回月份，否则返回null
            },
            set(value) {
                this.setDatePart('month', value);
            }
        },
        // 读取日期
        death_date_day: {
            get() {
                const parts = this.datePartsD;
                return parts.length > 2 ? parts[2] : null; // 如果parts存在，返回日期，否则返回null
            },
            set(value) {
                this.setDatePart('day', value);
            }
        },

        // 填卡日期
        datePartsE() {
            if (this.fromCard.create_date) {
                // 分割年月日和时分秒
                const [datePartx, timePart] = this.fromCard.create_date.split(' ');
                // 分割年月日
                return datePartx.split('-');
            }
            return []; // 如果诊断日期为空，返回空数组
        },
        // 读取年份
        create_date_year: {
            get() {
                const parts = this.datePartsE;
                return parts.length > 0 ? parts[0] : null; // 如果parts存在，返回年份，否则返回null
            },
            set(value) {
                this.setCreateDate('year', value);
            }
        },
        // 读取月份
        create_date_month: {
            get() {
                const parts = this.datePartsE;
                return parts.length > 1 ? parts[1] : null; // 如果parts存在，返回月份，否则返回null
            },
            set(value) {
                this.setCreateDate('month', value);
            }
        },
        // 读取日期
        create_date_day: {
            get() {
                const parts = this.datePartsE;
                return parts.length > 2 ? parts[2] : null; // 如果parts存在，返回日期，否则返回null
            },
            set(value) {
                this.setCreateDate('day', value);
            }
        },

        // hb_date_year首次出现乙肝症状和体征时间
        datePartsF() {
            if (this.fromCard.hb_date) {
                // 分割年月日和时分秒
                const [datePartx, timePart] = this.fromCard.hb_date.split(' ');
                // 分割年月日
                return datePartx.split('-');
            }
            return []; // 如果诊断日期为空，返回空数组
        },
        // 读取年份
        hb_date_year: {
            get() {
                const parts = this.datePartsF;
                return parts.length > 0 ? parts[0] : null; // 如果parts存在，返回年份，否则返回null
            },
            set(value) {
                this.setHbDate('year', value);
            }
        },
        // 读取月份
        hb_date_month: {
            get() {
                const parts = this.datePartsF;
                return parts.length > 1 ? parts[1] : null; // 如果parts存在，返回月份，否则返回null
            },
            set(value) {
                this.setHbDate('month', value);
            }
        },
    },

    mounted() { },

    created() {
        this.queueForm.patientId = this.rowData.patienT_ID;
        this.queueForm.serialNo = this.rowData.seriaL_NO;
        this.queueForm.emp_no = this.$route.query && this.$route.query.emp_no;
        this.getDetailList();
    },

    methods: {
        // 回显
        getDetailList() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            this.queueForm.emp_no = this.rowData.emP_NO;
            DetailReportCardReview(this.queueForm).then(res => {
                if (res.data.detailReportCardReviewRetVueModels.length > 0) {
                    this.fromCard = res.data.detailReportCardReviewRetVueModels[0];
                    if (this.fromCard.vd_ch !== null) {
                        this.vd_ch = this.fromCard.vd_ch; // 可多选数据回显
                    }
                } else {
                    this.$message({
                        showClose: true,
                        message: '请联系信息科!!!',
                        type: 'warning'
                    });
                }
            }).finally(() => {
                loading.close();
            });
        },

        // 保存按钮操作(这个接口是进行限制条件判断的)
        saveRejectModification() {
            this.queueForm.ls_type = '1'; //按钮代表的类型1代表保存按钮标识
            if (this.queueForm.ls_type === '1' && this.rowData.status === '2') {
                this.showMessageA('该报卡已经通过审核,不允许再次保存!'); // 调用消息提示
            } else {
                if (this.rowData.status === '3') {
                    this.showMessageA('该报卡目前处于驳回修改状态,请及时通知填写医师修改报卡!'); // 调用消息提示
                } else {
                    // 进行rule限制->判断下进行限制的字段是否填入或者填入的格式是否正确
                    this.ruleJudgeAndSave();
                }
            }
        },

        // 前端rule限制判断条件
        ruleJudgeAndSave() {
            if (this.fromCard.card_no === null) {
                this.showMessageA('红色字体为必填项!'); // 调用消息提示
            } else {
                // 进行保存操作
                this.saveFinal(); // 调用后端保存接口
            }
        },

        // 调用保存/修改接口(这个接口是进行调用后端保存接口的)
        saveFinal() {
            this.fromCard.patientId = this.rowData.patienT_ID;
            this.fromCard.serialNo = this.rowData.seriaL_NO;
            this.fromCard.deptCode = this.rowData.depT_CODE;
            this.fromCard.emp_no = this.rowData.emP_NO;
            JudgeCountReportCardReview(this.fromCard).then((res) => {
                // lI_COUNT证明数据库中已经有该条数据,后续进行修改就行,否则进行新增
                if (res.data.listSaveCount[0].lI_COUNT === 1) {
                    // 调用修改接口
                    this.fromCard.vd_ch = this.vd_ch;
                    UpdateReportCardReview(this.fromCard).then(res => {
                        if (res.code === 200) {
                            this.showMessageA('修改数据成功'); // 调用消息提示
                            // this.$emit('success-listener', true); // 给父组件传参,是的弹框关闭调用查询接口
                        } else {
                            this.showMessageA('修改数据失败,清联系信息科!'); // 调用消息提示
                        }
                    })
                } else {
                    // 调用保存接口
                    this.fromCard.vd_ch = this.vd_ch;
                    SaveReportCardReview(this.fromCard).then(res => {
                        if (res.code === 200) {
                            this.showMessageA('保存数据成功'); // 调用消息提示
                            // this.$emit('success-listener', true); // 给父组件传参,是的弹框关闭调用查询接口
                        } else {
                            this.showMessageA('已保存数据失败,清联系信息科!'); // 调用消息提示
                        }
                    })
                }
            });
        },

        // 更改状态按钮操作
        getChangeState() {
            this.queueForm.patientId = this.rowData.patienT_ID;
            this.queueForm.serialNo = this.rowData.seriaL_NO;
            ChangeStateButton(this.queueForm).then(res => {
                if (res.code === 200) {
                    const h = this.$createElement;
                    this.$notify({
                        title: '成功',
                        message: h('i', { style: 'color: teal' }, '更改状态成功')
                    });
                    // 传递给父组件一个参数:true
                    this.$emit('success-listener', true);
                } else {
                    this.$message({
                        showClose: true,
                        message: '请联系信息科,请勿重复操作!!!',
                        type: 'warning'
                    });
                }
            });
        },

        // 审核通过按钮操作
        getApproved() {
            this.queueForm.ls_type = '2'; //2是审核通过按钮标识
            this.queueForm.patientId = this.rowData.patienT_ID;
            this.queueForm.serialNo = this.rowData.seriaL_NO;
            this.queueForm.card_no = this.fromCard.card_no;
            this.queueForm.emp_no = this.$route.query && this.$route.query.emp_no;
            if (this.queueForm.ls_type == '2' & this.rowData.status == '2') {
                this.showMessageA('该报卡已经通过审核,不允许再次设置通过审核!'); // 调用消息提示
            } else {
                if (this.fromCard.card_no === null) {
                    this.showMessageA('审核通过失败!请填写卡片编号!'); // 调用消息提示
                } else {
                    if (this.rowData.status === '2') {
                        this.showMessageA('审核通过失败!已经审核通过!'); // 调用消息提示
                    } else {
                        ApprovedButton(this.queueForm).then(res => {
                            if (res.code === 200) {
                                this.showMessageA('审核成功!'); // 调用消息提示
                                // 传递给父组件一个参数:true
                                this.$emit('success-listener', true);
                            } else {
                                this.showMessageA('请联系信息科,请勿重复操作!!!'); // 调用消息提示
                            }
                        });
                    }
                }
            }
        },

        // 驳回修改按钮操作
        getRejectModification() {
            this.queueForm.ls_type = '3'; //按钮代表的类型3代表驳回修改标识
            this.queueForm.patientId = this.rowData.patienT_ID;
            this.queueForm.serialNo = this.rowData.seriaL_NO;
            this.queueForm.invalid_reason = this.fromCard.invalid_reason;
            if (this.queueForm.ls_type === '3' && this.rowData.status === '2') {
                this.showMessageA('该报卡已经通过审核,不允许驳回修改!'); // 调用消息提示
            } else {
                if (this.fromCard.invalid_reason === null) {
                    this.showMessageA('驳回修改失败!请填写退卡原因!'); // 调用消息提示
                } else {
                    if (this.rowData.status === '3') {
                        this.showMessageA('驳回修改失败!已经驳回修改!'); // 调用消息提示
                    } else {
                        RejectModificationButton(this.queueForm).then(res => {
                            if (res.code === 200) {
                                this.showMessageA('驳回修改成功!'); // 调用消息提示
                                // 传递给父组件一个参数:true
                                this.$emit('success-listener', true);
                            } else {
                                this.showMessageA('请联系信息科,请勿重复操作!'); // 调用消息提示
                            }
                        });
                    }
                }
            }
        },

        // 填卡说明按钮操作
        getDescribe() {
            this.dialogDescribe = true;
        },

        // 肺结核转诊单按钮操作
        getPulmonaryTuberculosis() {
            PulmonaryTuberculosisButton().then(res => {
                this.queueForm.url = res.data.list[0].datA_NAME;
                let newUrl = this.queueForm.url + this.queueForm.patientId;
                window.open(newUrl, '_blank');
            });
        },

        // 规定CheckBox只能选择一个
        handleCheckManagementSystem(value) {
            if (this.fromCard.card_type.length > 1) {// 报卡类别
                this.fromCard.card_type.splice(0, 1)
            }
            if (this.fromCard.sex.length > 1) {// 性别
                this.fromCard.sex.splice(0, 1)
            }
            if (this.fromCard.age_unit.length > 1) {// 年龄
                this.fromCard.age_unit.splice(0, 1)
            }
            if (this.fromCard.region.length > 1) {// 病人属于
                this.fromCard.region.splice(0, 1)
            }
            if (this.fromCard.occupation.length > 1) {// 人群分类
                this.fromCard.occupation.splice(0, 1)
            }
            if (this.fromCard.desease_type_1.length > 1) {// 病例分类1
                this.fromCard.desease_type_1.splice(0, 1)
            }
            if (this.fromCard.desease_type_2.length > 1) {// 病例分类2
                this.fromCard.desease_type_2.splice(0, 1)
            }
            if (this.fromCard.infection_desease_a.length > 1) {// 甲类传染病
                this.fromCard.infection_desease_a.splice(0, 1)
            }
            if (this.fromCard.infection_desease_b.length > 1) {// 乙类传染病
                this.fromCard.infection_desease_b.splice(0, 1)
            }
            if (this.fromCard.infection_desease_c.length > 1) {// 丙类传染病
                this.fromCard.infection_desease_c.splice(0, 1)
            }
            if (this.fromCard.vd_ms.length > 1) {// 性病报告附加栏-婚姻状况
                this.fromCard.vd_ms.splice(0, 1)
            }
            if (this.fromCard.vd_sc.length > 1) {// 性病报告附加栏-文化程度
                this.fromCard.vd_sc.splice(0, 1)
            }
            if (this.fromCard.vd_ri.length > 1) {// 性病报告附加栏-感染途径
                this.fromCard.vd_ri.splice(0, 1)
            }
            if (this.fromCard.vd_ss.length > 1) {// 性病报告附加栏-样本来源
                this.fromCard.vd_ss.splice(0, 1)
            }
            if (this.fromCard.hb_pt.length > 1) {// 乙肝病例附加栏-HbsAg阳性时间
                this.fromCard.hb_pt.splice(0, 1)
            }
            if (this.fromCard.hb_igm.length > 1) {// 抗-HBc 1gM1∶100检测结果
                this.fromCard.hb_igm.splice(0, 1)
            }
            if (this.fromCard.hb_lp.length > 1) {// 肝穿检测结果
                this.fromCard.hb_lp.splice(0, 1)
            }
            if (this.fromCard.hb_cp.length > 1) {// 恢复期血清HBsAg阴转
                this.fromCard.hb_cp.splice(0, 1)
            }
        },

        // 提示信息封装
        showMessageA(message) {
            const h = this.$createElement;
            this.$notify({
                title: '提示信息！',
                message: h('i', { style: 'color: teal' }, message)
            });
        },

        // 设置出生日期的年月日部分
        setDateOfBirth(part, value) {
            const parts = this.datePartsA;
            if (parts.length) {
                // 根据part更新相应的部分
                switch (part) {
                    case 'year':
                        parts[0] = value;
                        break;
                    case 'month':
                        parts[1] = value;
                        break;
                    case 'day':
                        parts[2] = value;
                        break;
                    default:
                        console.error('Invalid part provided');
                        return;
                }
                // 重新组合日期字符串
                const updatedDate = parts.join('-');
                // 更新fromCard.date_of_birth
                this.fromCard.date_of_birth = `${updatedDate} ${parts.length > 3 ? parts.slice(3).join(':') : ''}`;
            }
        },

        // 设置发病日期的年月日部分
        setOnsetDate(part, value) {
            const parts = this.datePartsB;
            if (parts.length) {
                // 根据part更新相应的部分
                switch (part) {
                    case 'year':
                        parts[0] = value;
                        break;
                    case 'month':
                        parts[1] = value;
                        break;
                    case 'day':
                        parts[2] = value;
                        break;
                    default:
                        console.error('Invalid part provided');
                        return;
                }
                // 重新组合日期字符串
                const updatedDate = parts.join('-');
                // 更新fromCard.onset_date
                this.fromCard.onset_date = `${updatedDate} ${parts.length > 3 ? parts.slice(3).join(':') : ''}`;
            }
        },

        // 设置诊断日期的年月日部分
        setDiagnosisDate(part, value) {
            // 注意这里需要调用 datePartsC() 来获取计算属性的值
            const { dateParts, timeParts } = this.datePartsC;
            let parts = dateParts.concat(timeParts); // 将日期和时间的部分合并
            if (parts.length) {
                // 根据part更新相应的部分
                switch (part) {
                    case 'year':
                        parts[0] = value;
                        break;
                    case 'month':
                        parts[1] = value;
                        break;
                    case 'day':
                        parts[2] = value;
                        break;
                    case 'hour':
                        // 确保更新了正确的时间部分
                        parts[3] = value;
                        break;
                    case 'minute':
                        parts[4] = value;
                        break;
                    case 'second':
                        parts[5] = value;
                        break;
                    default:
                        console.error('Invalid part provided');
                        return;
                }
                // 重新组合日期和时间字符串
                const updatedDate = parts.slice(0, 3).join('-'); // 日期部分
                const updatedTime = parts.slice(3).join(':'); // 时间部分
                // 更新fromCard.diagnosis_date
                this.fromCard.diagnosis_date = `${updatedDate} ${updatedTime}`;
            }
        },

        // 设置死亡日期的年月日部分
        setDeathDate(part, value) {
            const parts = this.datePartsD;
            if (parts.length) {
                // 根据part更新相应的部分
                switch (part) {
                    case 'year':
                        parts[0] = value;
                        break;
                    case 'month':
                        parts[1] = value;
                        break;
                    case 'day':
                        parts[2] = value;
                        break;
                    default:
                        console.error('Invalid part provided');
                        return;
                }
                // 重新组合日期字符串
                const updatedDate = parts.join('-');
                // 更新fromCard.death_date
                this.fromCard.death_date = `${updatedDate} ${parts.length > 3 ? parts.slice(3).join(':') : ''}`;
            }
        },

        // 设置填卡日期的年月日部分
        setCreateDate(part, value) {
            const parts = this.datePartsE;
            if (parts.length) {
                // 根据part更新相应的部分
                switch (part) {
                    case 'year':
                        parts[0] = value;
                        break;
                    case 'month':
                        parts[1] = value;
                        break;
                    case 'day':
                        parts[2] = value;
                        break;
                    default:
                        console.error('Invalid part provided');
                        return;
                }
                // 重新组合日期字符串
                const updatedDate = parts.join('-');
                // 更新fromCard.date_of_birth
                this.fromCard.create_date = `${updatedDate} ${parts.length > 3 ? parts.slice(3).join(':') : ''}`;
            }
        },

        // 设置首次出现乙肝症状和体征时间的年月部分
        setHbDate(part, value) {
            const parts = this.datePartsF;
            if (parts.length) {
                // 根据part更新相应的部分
                switch (part) {
                    case 'year':
                        parts[0] = value;
                        break;
                    case 'month':
                        parts[1] = value;
                        break;
                    case 'day':
                        parts[2] = value;
                        break;
                    default:
                        console.error('Invalid part provided');
                        return;
                }
                // 重新组合日期字符串
                const hbDate = parts.join('-');
                // 更新fromCard.hb_date
                this.fromCard.hb_date = `${hbDate} ${parts.length > 3 ? parts.slice(3).join(':') : ''}`;
            }
        },
    }
}
</script>

<style scoped lang="scss">
.unit_input {
    border: none;
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 80px;
    font-size: 15px;
    font-weight: 100px;
    margin-left: 3px;
}

.unit_input1 {
    border: none;
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 300px;
    font-size: 15px;
    font-weight: 100px;
    margin-left: 3px;
}

.unit_input2 {
    border: none;
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 200px;
    font-size: 15px;
    font-weight: 100px;
    margin-left: 3px;
}

.unit_input3 {
    border: none;
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 60px;
    font-size: 15px;
    font-weight: 100px;
    margin-left: 3px;
}

.unit_input5 {
    border: none;
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 39px;
    font-size: 15px;
    font-weight: 100px;
    margin-left: 3px;
}

.unit_input6 {
    border: none;
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 110px;
    font-size: 15px;
    font-weight: 100px;
    margin-left: 3px;
}

.unit_input7 {
    border: none;
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 230px;
    font-size: 15px;
    font-weight: 100px;
    margin-left: 3px;
}

.flex-container {
    display: flex;
    flex-wrap: wrap;

    .flex-item {
        width: 100%;
    }
}

.checkBoxMargin {
    margin: 0px 0;
}

.span {
    display: inline-block;
}

.span1 {
    color: rgb(0, 0, 0);
}

.spanColor {
    color: red;
}

//字体颜色
::v-deep .el-checkbox__label {
    color: rgb(8, 0, 0);
}

//选中字体颜色
::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
    color: rgb(8, 0, 0);
}

//选中对勾框背景色边框色
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.myRedCheckBox .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    border-color: rgb(0, 0, 0);
    background-color: rgb(2, 0, 0);
}

::v-deep.custom-select .el-input__icon {
    display: none;
}
</style>

<style>
/* check没有选中复选框的颜色设置  */
.el-checkbox__inner {
    border: 1px solid #303133;
}
</style>