import request from "@/utils/request";

// 用户初始化信息
export function GetUserHoneInfo(empNo) {
  return request({
    url: "/QueueItem/GetUserHoneInfo?empNo=" + empNo,
    method: "get",
  });
}

//获取项目字典
export function GetItemTreeList(unitId,windowId) {
  return request({
    url: "/QueueItem/GetItemTreeList?unitId=" + unitId + '&windowId=' + windowId,
    method: "get",
  });
}

//获取窗口信息
export function GetQueueTreeList() {
  return request({
    url: "/QueueItem/GetQueueTreeList",
    method: "get",
  });
}

//获取已配置信息
export function GetConfigConsultingRoomItem(unitId,windowId) {
  return request({
    url: "/QueueItem/GetConfigConsultingRoomItem?unitId=" + unitId + '&windowId=' + windowId,
    method: "get",
  });
}

//配置字典
export function ConfigConsultingRoomItem(data) {
  return request({
    url: "/QueueItem/ConfigConsultingRoomItem",
    method: "post",
    data:data,
  });
}

//取消配置字典
export function CancelConfigConsultingRoomItem(data) {
  return request({
    url: "/QueueItem/CancelConfigConsultingRoomItem",
    method: "post",
    data:data,
  });
}
//时间字典获取
export function GetQueueTimeTree(unitId,windowId,date) {
  return request({
    url: "/QueueItem/GetQueueTimeTree?unitId=" + unitId + '&windowId=' + windowId + '&date=' + date,
    method: "get",
  });
}

//时间字典获取
export function ConfigQueueTime(data) {
  return request({
    url: "/QueueItem/ConfigQueueTime",
    method: "post",
    data:data,
  });
}
