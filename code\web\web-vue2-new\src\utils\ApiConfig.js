const env = {
  api_url: "http://**************:8009/api/",
  rabbit_url: "ws://**********:15674/ws",
  mappings: [
    {
      ip: "localhost",
      url: "http://localhost:8088/api/",
    },
    {
      ip: "***********",
      url: "http://localhost:8088/api/",
    },
    {
      ip: "**************",
      url: "http://localhost:8088/api/",
    },
    {
      ip: "*********",
      url: "http://*********:10001/api/",
    },
    {
      ip: "************",
      url: "http://************:10001/api/",
    },
    {
      ip: "**********",
      url: "http://**********:10001/api/",
    },
    {
      ip: "************",
      url: "http://************:10001/api/",
    }, {
      ip: "**********",
      url: "http://**********:10001/api/",
    },
    {
      ip: "************",
      url: "http://************:10001/api/",
    },
    {
      ip: "*********",
      url: "http://*********:10001/api/",
    },
    {
      ip: "************",
      url: "http://************:10001/api/",
    },
    {
      ip: "*********",
      url: "http://*********:8076/api/",
    },
    {
      ip: "************9",
      url: "http://************9:8076/api/",
    },
    {
      ip: "http://mobile.honlivhp.com",
      url: "http://**************:8009/api/",
    },
  ],
  baseMappings: [
    {
      ip: "localhost",
      url: "http://localhost:8088/",
    },
    {
      ip: "*********",
      url: "http://*********:10001/",
    },
    {
      ip: "************",
      url: "http://************:10001/",
    },
    {
      ip: "**********",
      url: "http://**********:10001/",
    },
    {
      ip: "************",
      url: "http://************:10001/",
    }, {
      ip: "**********",
      url: "http://**********:10001/",
    },
    {
      ip: "************",
      url: "http://************:10001/",
    },
    {
      ip: "*********",
      url: "http://*********:10001/",
    },
    {
      ip: "************",
      url: "http://************:10001/",
    },
    {
      ip: "*********",
      url: "http://*********:8076/",
    },
    {
      ip: "************9",
      url: "http://************9:8076/",
    },
    {
      ip: "http://mobile.honlivhp.com",
      url: "http://**************:8009/",
    },
  ],
  rabbitMqUrls: [
    {
      ip: "localhost",
      url: "ws://**********:15674/ws",
    },
    {
      ip: "*********",
      url: "ws://**********:15674/ws",
    },
    {
      ip: "************",
      url: "ws://*************:15674/ws",
    },
  ],
  reportUploadUrls: [
    {
      ip: "localhost",
      url: "http://*********:8082/",
    },
    {
      ip: "***********",
      url: "http://*********:8082/",
    },
    {
      ip: "**************",
      url: "http://************9:8082/",
    },
    {
      ip: "*********",
      url: "http://*********:8082/",
    },
    {
      ip: "************",
      url: "http://************9:8082/",
    },
    {
      ip: "**********",
      url: "http://*********:8082/",
    },
    {
      ip: "************",
      url: "http://************9:8082/",
    }, {
      ip: "**********",
      url: "http://*********:8082/",
    },
    {
      ip: "************",
      url: "http://************9:8082/",
    },
    {
      ip: "*********",
      url: "http://*********:8082/",
    },
    {
      ip: "************",
      url: "http://************9:8082/",
    },
    {
      ip: "*********",
      url: "http://*********:8082/",
    },
    {
      ip: "************9",
      url: "http://************9:8082/",
    }
  ],
  mappingsFile: [
    {
      ip: "localhost",
      url: "http://localhost:8088/",
    },
    {
      ip: "*********",
      url: "http://*********:8076/",
    }
  ],
  get_api_url() {
    let ip = window.location.hostname;
    let mapping = this.mappings.find((t) => {
      return t.ip === ip;
    });

    if (mapping !== undefined) {
      return mapping.url;
    }
    return this.api_url;
  },
  get_file_url() {
    let ip = window.location.hostname;
    let mapping = this.mappingsFile.find((t) => {
      return t.ip === ip;
    });

    if (mapping !== undefined) {
      return mapping.url;
    }
    return this.api_url;
  },
  get_rabbit_url() {
    let ip = window.location.hostname;
    let mapurl = this.rabbitMqUrls.find((t) => {
      return t.ip === ip;
    });
    if (mapurl !== undefined) {
      return mapurl.url;
    }
    return this.rabbit_url;
  },
  get_base_url() {
    let ip = window.location.hostname;
    let mapping = this.baseMappings.find((t) => {
      return t.ip === ip;
    });

    if (mapping !== undefined) {
      return mapping.url;
    }
    return mapping.url;
  },
  get_base_report_upload_url() {
    let ip = window.location.hostname;
    let mapping = this.reportUploadUrls.find((t) => {
      return t.ip === ip;
    });

    if (mapping !== undefined) {
      return mapping.url;
    }
    return mapping.url;
  },
};
export default env;
