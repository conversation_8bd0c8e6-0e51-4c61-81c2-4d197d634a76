<template>
  <div class="single-master">
    <div class="single-title">孕产数据上报窗口</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="开始时间:">
              <el-date-picker v-model="queueForm.beginDate" type="date" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker v-model="queueForm.endDate" type="date" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="ID号:">
              <el-input v-model="queueForm.patientId" placeholder="请输入患者id" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <div class="element-button">
                <el-button type="primary" icon="el-icon-search" @click="maternalDataReporting">查询</el-button>
                <el-button type="primary" icon="el-icon-download" @click="exportData">导出</el-button>
              </div>
            </el-form-item>
            <el-form-item>
              <div class="element-radio" style="border: 1px solid #d0d0d0;border-radius: 10px;">
                <div style="padding: 0 15px;display: flex;">
                  <div>
                    <el-radio-group v-model="queueForm.status">
                      <el-radio :label="0">未报</el-radio>
                      <el-radio :label="1">已报</el-radio>
                    </el-radio-group>
                  </div>
                  <div style="color: red;padding: 0 10px;">总人次：{{ sum }}</div>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="element-table">
          <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 150" highlight-current-row
            @cell-dblclick="doubleSelectionChange">
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column prop="REPORT_STATUS" align="center" label="上报状态" width="80"></el-table-column>
            <el-table-column prop="VISIT_DATE" align="center" label="就诊日期" width="100"></el-table-column>
            <el-table-column prop="PATIENT_ID" align="center" label="病人ID" width="90"></el-table-column>
            <el-table-column prop="NAME" align="center" label="姓名" width="95"></el-table-column>
            <el-table-column prop="SEX" align="center" label="性别" width="75"></el-table-column>
            <el-table-column prop="ID_NO" align="center" label="身份证号" width="180"></el-table-column>
            <el-table-column prop="MAILING_ADDRESS" align="center" label="家庭地址" width="250"></el-table-column>
            <el-table-column prop="REPORT_TIME" align="center" label="登记时间" width="180"></el-table-column>
          </el-table>
        </div>
        <el-dialog :visible.sync="extraFunctionStatus" :title="title" width="85%">
          <extra-function :row-data="rowData" :max-height="'height:' + (tableHeight - 250) + 'px'"
            :key="extraFunctionKey"></extra-function>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { maternalDataReportingInquire, exportMaternalDataReporting } from '@/api/singlePage/maternalDataReportingInquire'
import { excelDownloadXLSX } from "@/utils/BlobUtils"
import ExtraFunction from './components/extraFunction.vue'
export default {
  name: 'inquire',
  props: [],
  components: { ExtraFunction },
  data() {
    return {
      tableDate: [],
      sum: 0,
      queueForm: {
        beginDate: this.formatDate(new Date()),
        endDate: this.formatDate(new Date()),
        // beginDate: '2024-05-01',
        // endDate: '2024-05-01',
        patientId: '',
        status: ''
      },
      tableHeight: undefined,
      rowData: {},
      extraFunctionStatus: false,
      title: '患者详情信息',
      extraFunctionKey: 0,
    }
  },
  created() {
    this.maternalDataReporting();
    this.handleResize();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize); // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize); // 移除监听器
  },
  methods: {
    exportData() {
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力导出中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      exportMaternalDataReporting(this.queueForm).then(res => {
        let fileName = "孕产妇数据上报统计";
        excelDownloadXLSX(res, fileName)
      }).finally(() => {
        loading.close();
      })
    },
    doubleSelectionChange(row) {
      this.rowData = row;
      ++this.extraFunctionKey;
      this.extraFunctionStatus = true;
    },
    handleResize() {
      this.tableHeight = window.innerHeight; // 更新高度数据
    },
    maternalDataReporting() {
      this.tableDate = []
      maternalDataReportingInquire(this.queueForm).then(res => {
        if (res.code === 200) {
          this.tableDate = res.data
          this.sum = this.tableDate.length;
        }
      })
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
</style>
