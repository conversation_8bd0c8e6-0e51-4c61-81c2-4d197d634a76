<template>
  <div class="single-master my_style">
    <div class="single-title">药品使用明细</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="开始时间:">
              <el-date-picker
                v-model="queueForm.beginDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker
                v-model="queueForm.endDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <div class="element-button">
                <el-button type="primary" icon="el-icon-search" @click="getDrugUsageDetailsList">查询</el-button>
                <el-button type="primary" icon="el-icon-download" @click="exportDrugUsageDetailsList">导出</el-button>
              </div>
            </el-form-item>
            <div>
              <el-tabs v-model="queueForm.activeValue" type="card" @tab-click="handleClick">
                <el-tab-pane label="按药品查询" name="1">
                  <el-select v-model="queueForm.drugCode" class="input-my" :filter-method="remoteMethodTwo" filterable
                             placeholder="请输入关键字进行检索" clearable  style="margin-left: -2px;">
                    <el-option
                      v-for="(item,index) in drugNew"
                      :key="index"
                      :label="item.LABEL"
                      :value="item.VALUE"
                    >
                    </el-option>
                  </el-select>
                </el-tab-pane>
                <el-tab-pane label="按医生查询" name="2"><doctor-table></doctor-table></el-tab-pane>
              </el-tabs>
            </div>
          </el-form>
        </div>
        <div v-show="drugStatus">
          <drug-table :table-height="tableHeight" :key :queue-form="queueForm" ref="drugTableRefs"></drug-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * 药品使用明细
 * /singlePage/drugUsageDetails/index
 */
import { getThisDateMonthBeginDefault, getThisDateDefault } from '@/utils/DateUtils'
import {getDrugStatementItemDict} from '@/api/singlePage/drugStatement';
import DrugTable from './module/drugTable.vue'
import DoctorTable from './module/doctorTable.vue'

export default {
  name: 'index',
  props: [],
  components: { DoctorTable, DrugTable },
  data() {
    return {
      drugStatus: true,
      doctorStatus: false,
      tableData: [],
      options: [],
      drug: [{ VALUE:'123', CODE: '123',LABEL: '11'}],
      drugNew: [],
      key: 0,
      queueForm: {
        beginDate: getThisDateMonthBeginDefault(),
        endDate: getThisDateDefault(),
        activeValue: '1',
        drugCode: '',
        drugType: '1',
      },
      tableHeight: undefined,
    }
  },
  created() {
    this.handleResize()
    this.getDrugStatementItem();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    remoteMethodTwo(query){
      this.queueForm.drugCode = query;
      if (query !== "") {
        this.drugNew = this.drug.filter((item) => {
          // 这里是用的value选项筛选，默认是label
          if (item.CODE.toUpperCase().indexOf(query.toUpperCase()) > -1 ||
            item.LABEL.toLowerCase().indexOf(query.toLowerCase()) > -1){
            return true;
          }
        });
      } else {
        this.drugNew = [];
      }
    },
    handleClick(){
      ++this.key
      if (this.queueForm.activeValue === '1'){
        this.drugStatus = true;
        this.doctorStatus = false;
      }else if (this.queueForm.activeValue === '2'){
        this.drugStatus = false;
        this.doctorStatus = true;
      }
    },
    exportDrugUsageDetailsList(){
      if (this.queueForm.activeValue === '1'){
        this.$refs.drugTableRefs.exportTableData();
      }
    },
    getDrugUsageDetailsList(){
      if (this.queueForm.activeValue === '1'){
        this.$refs.drugTableRefs.getTableDate();
      }
    },
    getDrugStatementItem(){
      getDrugStatementItemDict().then(res => {
        if (res.code === 200){
          this.drug = res.data.drug;
        }
      })
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
.my_style{
  ::v-deep.el-tabs__item {
    padding: 0 40px;
    height: 30px;
    line-height: 30px;
  }
  ::v-deep.el-tabs__header {
    padding: 0;
    position: relative;
    margin: 0 0 1px;
  }
  .input-my{

    ::v-deep.el-input--medium .el-input__inner {
      width: 300px!important;
      margin-left: 5px;
      height: 35px;
      line-height: 36px;
      margin-bottom: 5px;
    }
  }
}
</style>
