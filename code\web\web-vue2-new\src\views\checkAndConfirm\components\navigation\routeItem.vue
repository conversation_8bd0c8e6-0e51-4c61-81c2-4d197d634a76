<template>
  <div class="tabs-home">
    <el-tabs v-model="activeName" type="card" @tab-click="tabsClick" v-for="(item,index) in activeItem" :key="index"
             :class="{tabsButtonOne: activeName === item.value}">
      <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: 'routeItem',
  props: [],
  components: {},
  data() {
    return {
      activeName: 'examConfirm',
      activeItem: [{
        label: '检查确认',
        value: 'examConfirm',
      },
      //   {
      //   label: '预约中心',
      //   value: 'reservationCenter',
      // },
      //   {
      //   label: '四维预约',
      //   value: 'perinatalHealthCare',
      // },
        {
        label: '配置中心',
        value: 'configurationCenter',
      },
      //   {
      //   label: '系统配置',
      //   value: 'systemConfig',
      // },
      ],
    }
  },
  created() {
    this.tabsClick();
  },
  mounted() {
  },
  methods: {
    tabsAssignment(data){
      this.activeName = data;
    },
    tabsClick() {
      this.$emit("tabs-click", this.activeName)
    },
  }
}
</script>

<style scoped lang="scss">
.tabs-home {
  display: flex;

  ::v-deep.el-tabs__header {
    padding: 0;
    margin: 0;
  }

  ::v-deep.el-tabs--card > .el-tabs__header .el-tabs__nav {
    border-radius: 0;
  }

  ::v-deep.el-tabs__item {
    padding: 0 20px;
    font-size: 16px;
    height: 50px;
    color: #303133;
    line-height: 50px;
  }

  .tabsButtonOne {
    ::v-deep.el-tabs__nav {
      background-color: #FFFFFF !important;
      color: black !important;
    }

    ::v-deep.el-tabs__item {
      color: black !important;
    }
  }

  ::v-deep.el-tabs__nav {
    :hover {
      background-color: #FFFFFF;
      color: black;
      transform: scale(1.1);
    }
  }
  @media screen and (max-height: 650px) {
    ::v-deep.el-tabs__item {
      padding: 0 10px;
      font-size: 14px;
      height: 30px;
      line-height: 30px;
    }
  }
}
</style>
