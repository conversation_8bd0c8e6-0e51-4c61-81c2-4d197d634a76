<template>
  <div>
    <el-dialog
      :visible.sync="status"
      :title="title"
      width="85%"
    >
      <div class="refund-form">
        <el-form ref="form" :model="queueForm" :inline="true">
          <el-form-item label="查询时间:" label-width="85px">
            <div class="form-flex">
              <el-date-picker v-model="queueForm.beginDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
              <div class="date-f">-</div>
              <el-date-picker v-model="queueForm.endDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="dataInitialize">提取数据</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="button-home">
        <div class="tree-left">
          <div class="dict-title">检查字典选取</div>
          <div>
            <el-tree :data="itemDict"  @node-click="handleNodeClick"></el-tree>
          </div>
        </div>
        <div class="table-right">
          <div class="dict-title">数据汇总量</div>
          <div class="right-table" v-if="queueForm.name && tableData.length > 0">
<!--            600-->
            <el-table :data="tableData" style="width: 100%" border height="600" v-loading="loading">
              <el-table-column prop="name" align="center" label="类别" ></el-table-column>
              <el-table-column prop="amount" align="center" label="数量"></el-table-column>
            </el-table>
          </div>
          <div class="right-text" v-else-if="!queueForm.name">
            <el-empty description="请先点击左侧字典查询想要查询的数据"></el-empty>
          </div>
          <div class="right-text" v-else-if="tableData.length === 0">
            <el-empty description="暂无统计数据"></el-empty>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getThisDateDefault} from '@/utils/DateUtils'
import {GetExamTreeDataDict,GetExamWorkloadData} from '@/api/checkAndConfirm/workloadSummarizing'
/**
 * 工作量汇总
 */
export default {
  name: 'workloadSummarizingIndex',
  props: [],
  components: {},
  data() {
    return {
      status: false,
      title: '工作量汇总',
      queueForm: {
        beginDate: '',
        endDate: '',
        name: '',
      },
      loading: false,
      tableData: [],
      itemDict: [],
    }
  },
  created() {
    this.queueForm.beginDate = getThisDateDefault();
    this.queueForm.endDate = getThisDateDefault();
  },
  mounted() {
  },
  methods: {
    init(status){
      if (status){
        this.status = status;
        this.getDataDictConfig();
      }
    },
    dataInitialize(){
      this.tableData = [];
      this.loading = true;
      console.log(this.queueForm)
      GetExamWorkloadData(this.queueForm).then(res => {
        if (res.code === 200){
          this.tableData = res.data;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
    getDataDictConfig(){
      GetExamTreeDataDict().then(res => {
        this.itemDict = res.data;
      })
    },
    handleNodeClick(data){
      this.queueForm.name = data.value;
      this.dataInitialize();
    },
  }
}
</script>

<style scoped lang="scss">
.refund-form{
  margin-top: 2px;
  margin-left: 10px;
  .form-flex {
    display: flex;
  }

  .date-f {
    padding: 0 5px;
  }
  ::v-deep.el-date-editor.el-input, .el-date-editor.el-input__inner {
    width: 135px;
  }
  ::v-deep.el-form-item {
    margin-bottom: 0;
  }
}
.button-home{
  min-height: 100px;
  display: flex;
  margin-top: 10px;
  border: 1px solid #3A71A8;
  .tree-left{
    border-right: 1px solid #3A71A8;
    width: 30%;
  }
  .table-right{
    width: 70%;
  }
  .dict-title{
    height: 30px;
    border: 1px solid #00a19b;
    background-color: #185F7D;
    color: #FFFFFF;
    font-size: 20px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }
}
</style>
