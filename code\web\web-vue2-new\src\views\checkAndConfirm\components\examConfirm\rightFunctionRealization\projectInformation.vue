<template>
  <div class="project-home">
    <el-dialog
      title="检查项目修改"
      :visible.sync="projectStatus"
      width="75%"
    >
      <div class="dialog-master">
        <!--        基础信息-->
        <div class="basic-information-master">
          <div class="basic-item">
            <div class="basic-item">
              <div class="basic-text">姓名：</div>
              <div class="basic-input">
                <el-input v-model="projectBasicData.name" disabled></el-input>
              </div>
            </div>
            <div class="basic-item">
              <div class="basic-text">申请序号：</div>
              <div class="basic-input">
                <el-input v-model="projectBasicData.examNo" disabled></el-input>
              </div>
            </div>
          </div>
          <div class="basic-item">
            <div class="basic-item">
              <div class="basic-text">性别：</div>
              <div class="basic-input">
                <el-input v-model="projectBasicData.sex" disabled></el-input>
              </div>
            </div>
            <div class="basic-item">
              <div class="basic-text">预约时间：</div>
              <div class="basic-input">
                <el-input v-model="projectBasicData.date" disabled></el-input>
              </div>
            </div>
          </div>
        </div>
        <!--        项目列表-->
        <div class="project-table-master">
          <div class="table-master">
            <el-table :data="projectData" style="width: 100%" border max-height="180px" show-summary
                      @row-click="projectTableClick" highlight-current-row>
              <el-table-column type="index" align="center" label="序号" width="60"></el-table-column>
              <el-table-column prop="examItem" align="center" label="检查项目"></el-table-column>
              <el-table-column prop="charges" align="center" label="应收费用" width="80"></el-table-column>
              <el-table-column prop="costs" align="center" label="实收费用" width="80"></el-table-column>
            </el-table>
          </div>
          <div class="button-master">
            <el-button type="success" @click="projectInsert">增加</el-button>
            <el-button type="danger" @click="projectDelete">删除</el-button>
          </div>
        </div>
        <!--        项目详情-->
        <div class="item-table-master">
          <el-table :data="projectDataRow.items" style="width: 100%" border max-height="350px" show-summary
                    @row-click="handleRowDblClick" highlight-current-row
          >
            <el-table-column type="index" align="center" width="60"></el-table-column>
            <el-table-column align="center" label="类别">
              <template slot-scope="scope">
                <el-select v-model="scope.row.itemClass">
                  <el-option
                    v-for="item in projectExamClassItems"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="itemName" align="center" label="项目名称"></el-table-column>
            <el-table-column prop="itemSpec" align="center" label="项目规格" width="80" :show-overflow-tooltip="true"
            ></el-table-column>
            <el-table-column prop="amount" align="center" label="数量" width="80">
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.amountInputStatus"
                  v-model="scope.row.amount"
                  ref="amountRefs"
                  @blur="amountInputBlur(scope.row)"
                >
                </el-input>
                <span v-else>{{ scope.row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="units" align="center" label="单位" width="50"></el-table-column>
            <el-table-column prop="unitPrice" align="center" label="单价" width="80"></el-table-column>
            <el-table-column prop="charges" align="center" label="应收费用" width="80"></el-table-column>
            <el-table-column prop="costs" align="center" label="实收费用" width="80">
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.costsInputStatus"
                  v-model="scope.row.costs"
                  placeholder="请输入地址"
                  ref="costsRefs"
                  @blur="costsInputBlur(scope.row)"
                >
                </el-input>
                <span v-else>{{ scope.row.costs }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="bottom-button-master">
          <div class="button-item">
            <el-button type="primary" @click="itemInsert">新增</el-button>
            <el-button type="danger" @click="itemDelete">删除</el-button>
            <el-button @click="saveProject">保存</el-button>
            <el-button type="info" @click="cancel">取消</el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <div class="dialog-master">
      <el-dialog
        title="检查项目选取"
        :visible.sync="examItemDialog"
        width="50%">
        <div>
          <el-input v-model="examItemSelect" placeholder="请输入关键字、拼音等进行筛选" @input="examItemInputFilter"></el-input>
        </div>
        <div>
          <div class="project-table-master">
            <div class="table-master" style="width: 90%">
              <el-table :data="examItemDictNew" style="width: 100%" border max-height="480px" show-summary
                        @row-click="examItemTableClick" highlight-current-row
              >
                <el-table-column type="index" align="center" label="序号" width="50"></el-table-column>
                <el-table-column prop="iteM_CODE" align="center" label="项目代码" width="180"></el-table-column>
                <el-table-column prop="iteM_NAME" align="center" label="项目名称"></el-table-column>
              </el-table>
            </div>
            <div class="button-master">
              <el-button type="success" @click="examItemConfirm">确定</el-button>
              <el-button type="danger" @click="examItemClose">关闭</el-button>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
    <div class="dialog-master">
      <el-dialog
        title="计价项目选取"
        :visible.sync="valuationItemDialog"
        width="60%">
        <div>
          <el-input v-model="valuationItemSelect" placeholder="请输入关键字、拼音等进行筛选" @input="valuationItemInputFilter"></el-input>
        </div>
        <div>
          <div class="project-table-master">
            <div class="table-master" style="width: 90%">
              <el-table :data="valuationItemDictNew" style="width: 100%" border max-height="480px" show-summary
                        @row-click="valuationItemTableClick" highlight-current-row
              >
                <el-table-column type="index" align="center" label="序号" width="50"></el-table-column>
                <el-table-column prop="iteM_CODE" align="center" label="项目代码" width="180"></el-table-column>
                <el-table-column prop="iteM_NAME" align="center" label="项目名称"></el-table-column>
              </el-table>
            </div>
            <div class="button-master">
              <el-button type="success" @click="valuationItemConfirm">确定</el-button>
              <el-button type="danger" @click="valuationItemClose">关闭</el-button>
            </div>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { GetItemInformation, DeleteExamProject, DeleteItemProject } from '@/api/checkAndConfirm/checkConfirm';
import { GetExamItemDict, GetValuationItemDict, GetValuationItemParticulars, SaveValuationItem } from '@/api/checkAndConfirm/checkCommon';
export default {
  name: 'projectInformation',
  props: [],
  components: {},
  data() {
    return {
      projectStatus: false,
      projectData: [],
      projectBasicData: {
        name: '',
        examNo: '',
        sex: '',
        date: ''
      },
      projectExamClassItems: [{
        value: 'D',
        label: '检查'
      }, {
        value: 'A',
        label: '西药'
      }, {
        value: 'E',
        label: '治疗'
      }, {
        value: 'I',
        label: '材料'
      }, {
        value: 'Z',
        label: '其他'
      }],
      projectDataRow: {},
      projectDataRowIndex: undefined,
      itemRowIndex: undefined,
      itemInsertNewTable: {
        itemClass: 'D',
      },
      itemRowData: {},
      examItemDict:[],
      examItemDictNew: [],
      examItemDialog: false,
      examItemSelect: '',
      examItemRow: {},
      valuationItemDict: [],
      valuationItemDictNew: [],
      valuationItemDialog: false,
      valuationItemSelect: '',
      valuationItemRow:{}
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    costsInputBlur(row){
      row.costsInputStatus=false
      this.projectResetImputedPrice();
    },
    amountInputBlur(row){
      let amount = parseFloat(row.amount);
      let unitPrice = parseFloat(row.unitPrice)
      let sum = amount * unitPrice;
      console.log(amount,unitPrice,sum)
      row.charges = sum;
      row.costs = sum;
      this.projectResetImputedPrice();
      row.amountInputStatus=false
    },
    saveProject(){
      let rowTable = this.$store.getters.rowTable;
      let projectData = this.projectData;
      let dataArray = [];
      if (projectData.length > 0){
        let status = true;
        projectData.filter(project => {
          let data = {
            patient_id: rowTable.patientId,
            visit_id: rowTable.visitId,
            exam_no: rowTable.examNo,
            exam_item_no: project.examItemNo,
            exam_item_code: project.examItemCode,
            exam_item: project.examItem,
            costs: project.costs,
            charges: project.charges,
            bill_items: [],
          }
          let itemArray = [];
          if (project.items.length === 0){
            status = false;
            this.$message.error("项目计价不能为空,请选择当前项目对应计价项目!!!")
            return;
          }
          project.items.filter(item => {
            if (item.itemCode){
              itemArray.push({
                charge_item_no: item.chargeItemNo,
                item_class: item.itemClass,
                item_code: item.itemCode,
                item_name: item.itemName,
                item_spec: item.itemSpec,
                amount: item.amount,
                units: item.units,
                costs: item.costs,
                ordered_by: item.orderedBy,
                performed_by: item.performedBy,
                charges: item.charges,
                operator_no: item.operatorNo,
                verified_indicator: item.verifiedIndicator,
              })
            }
          })
          data.bill_items = itemArray;
          dataArray.push(data);
        })
        if (status){
          SaveValuationItem(dataArray).then(res => {
            if (res.code === 200){
              this.$message.success("项目计价修改成功!!!,页面左上角手动刷新或F5刷新后列表参数会显示正常值")
              this.projectStatus = false;
            }
          })
        }
      }else{
        this.$message.success("项目计价修改成功!!!,页面左上角手动刷新或F5刷新后列表参数会显示正常值")
        this.projectStatus = false;
      }

    },
    /**
     * 取消事件
     */
    cancel() {
      this.projectStatus = false
      this.examItemDialog = false
      this.projectData = []
      this.projectBasicData = {
        name: '',
        examNo: '',
        sex: '',
        date: ''
      }
      this.projectDataRow = {}
      this.itemRowIndex = undefined
      this.examItemRow = {};
      this.projectDataRowIndex = undefined
      this.examItemSelect = ''
    },
    /**
     * 计价删除
     */
    itemDelete() {
      if (this.projectDataRow) {
        let a = []
        this.projectDataRow.items.forEach((t, index) => {
          if (index !== this.itemRowIndex) {
            a.push(t)
          }else{
            if (t.examNo){
              DeleteItemProject({
                exam_no: t.examNo,
                exam_item_no: t.examItemNo,
                charge_item_no: t.chargeItemNo,
              }).then((res) =>{
                this.$message.success(res.message);
              })
            }
          }
        })
        this.projectDataRow.items = a
        this.itemRowIndex = undefined
        this.projectResetImputedPrice();
      }
    },
    /**
     * 计价新增
     */
    itemInsert() {
      this.projectDataRow.items.push(this.itemInsertNewTable)
    },
    /**
     * 计价点击
     * @param row row
     */
    itemClick(row) {
      if (!row.itemDialogStatus) {
        this.itemRowData = row;
        this.getValuationItemDict(row.itemClass);
      }
    },
    /**
     * 计价行内点击事件
     * @param row row
     */
    handleRowDblClick(row, column, event) {
      this.itemRowIndex = this.projectDataRow.items.indexOf(row)
      if (column.property === 'amount') {
        row.amountInputStatus = true
        // 下面的代码是为了在编辑时自动聚焦输入框
        setTimeout(() => {
          this.$refs.amountRefs.focus()
        }, 200)
      } else if (column.property === 'costs') {
        row.costsInputStatus = true
        // 下面的代码是为了在编辑时自动聚焦输入框
        setTimeout(() => {
          this.$refs.costsRefs.focus()
        }, 200)
      } else {
        this.itemClick(row)
      }
    },
    /**
     * 检查项目删除
     */
    projectDelete() {
      let data = this.projectDataRow;
      if (data.examNo){
        DeleteExamProject({
          exam_no: data.examNo,
          exam_item_no: data.examItemNo,
        }).then(res => {
          this.projectData = this.projectData.filter(t => t !== data)
          this.projectDataRow = {}
          this.projectDataRowIndex = undefined;
        })
      }else{
        this.projectData = this.projectData.filter(t => t !== data)
        this.projectDataRow = {}
        this.projectDataRowIndex = undefined;
      }

    },
    /**
     * 检查项目新增
     */
    projectInsert() {
      this.projectData.push({})
    },
    /**
     * 检查项目行内点击事件
     * @param row
     */
    projectTableClick(row) {
      this.projectDataRow = row
      this.projectDataRowIndex = this.projectData.indexOf(row);
      if (!row.examStatus) {
        this.examItemDictNew = this.examItemDict;
        this.examItemDialog = true;
      }
    },
    /**
     * 检查项目数据监听
     * @param status
     */
    projectMonitor(status) {
      if (status) {
        let data = this.$store.getters.rowTable
        if (data) {
          let disabled = data.disabled
          let patientSource = data.patientSource
          let examNo = data.examNo
          if (!disabled) {
            if (patientSource) {
              if (patientSource === '2') {
                if (examNo) {
                  GetItemInformation(examNo).then(res => {
                    this.getExamItemDict();
                    this.projectData = res.data
                    this.projectDataRow = res.data[0]
                    this.projectDataRowIndex = 0;
                    this.projectBasicInformation(data)
                    this.projectStatus = status
                  })
                }
              }else{
                this.$msgbox.alert(
                  '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
                  '只有住院患者可以使用此功能!!!' + '</div>',
                  '系统提示',
                  {
                    confirmButtonText: '确定',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                  }).then(() => {
                  this.$emit('path-skip', 'reservationCenter')
                })
              }
            }
          }
        }
      }
    },
    /**
     * 检查信息初始化
     * @param data
     */
    projectBasicInformation(data) {
      this.projectBasicData = {
        name: data.name,
        examNo: data.examNo,
        sex: data.sex,
        date: data.reqDateTime.split(' ')[1]
      }
    },
    /**
     * 项目字典选取关闭按钮
     */
    examItemClose() {
      this.examItemDialog = false;
      this.examItemDictNew = [];
      this.examItemSelect = '';
    },
    /**
     * 项目字典确定选取按钮
     */
    examItemConfirm() {
      let exam = this.examItemRow;
      let data = {
        examItem: exam.iteM_NAME,
        examItemCode: exam.iteM_CODE,
        examStatus: false,
        charges: '0',
        costs: '',
        examItemNo: '',
        examNo: '',
        items: [{
          amountInputStatus: false,
          costsInputStatus: false,
          itemDialogStatus: false,
          itemClass: 'D',
        },],
      };
      this.projectDataRow = data;
      this.projectData.splice(this.projectDataRowIndex, 1, data);
      this.examItemClose();
    },
    /**
     * 项目字典行内点击选取事件
     * @param row row
     */
    examItemTableClick(row) {
      this.examItemRow = row;
    },
    /**
     * 项目字典舒服信息过滤
     * @param data
     */
    examItemInputFilter(data) {
      this.examItemDictNew = this.examItemDict.filter(t => t.iteM_NAME.includes(data) || t.inpuT_CODE.includes(data.toUpperCase()))
    },
    /**
     * 获取项目字典信息
     */
    getExamItemDict() {
      GetExamItemDict().then(res => {
        this.examItemDict = res.data;
      })
    },
    /**
     * 计价项目关闭按钮
     */
    valuationItemClose() {
      this.valuationItemDialog = false;
      this.valuationItemDictNew = [];
      this.valuationItemSelect = '';
    },
    /**
     * 计价项目确定按钮
     */
    valuationItemConfirm() {
      let rowTable = this.$store.getters.rowTable;
      let valuation = this.valuationItemRow;
      let itemData = this.itemRowData;
      let d = {
        item_class: valuation.iteM_CLASS,
        item_code: valuation.iteM_CODE,
        amount: "1",
        charge_type: rowTable.chargeType,
      }
      let data = {};
      GetValuationItemParticulars(d).then(res => {
        let resData = res.data.data;
        data = {
          itemClass: itemData.itemClass,
          itemName: valuation.iteM_NAME,
          itemCode: valuation.iteM_CODE,
          amountInputStatus: false,
          costsInputStatus: false,
          itemDialogStatus: false,
          amount: '1',
          itemSpec: resData.item_spec,
          units: resData.item_units,
          costs: parseFloat(resData.costs),
          charges: parseFloat(resData.charges),
          examNo: rowTable.examNo,
          visitId: rowTable.visitId,
          performedBy: rowTable.performedBy,
          orderedBy: this.$store.getters.empNo,
          chargeItemNo: '0',
          operatorNo: '0',
          verifiedIndicator: '0',
          unitPrice: parseFloat(resData.costs),
        }
        this.projectDataRow.items.splice(this.itemRowIndex, 1, data);
        this.valuationItemClose();
        this.projectResetImputedPrice();
      })
    },
    /**
     * 计价项目行内点击选取
     * @param row
     */
    valuationItemTableClick(row) {
      this.valuationItemRow = row;
    },
    valuationItemInputFilter(data) {
      this.valuationItemDictNew = this.valuationItemDict.filter(t => t.iteM_NAME.includes(data) || t.inpuT_CODE.includes(data.toUpperCase()))
    },
    /**
     * 计价项目获取
     * @param type
     */
    getValuationItemDict(type) {
      const loading = this.$loading({
        lock: true,
        text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.valuationItemDict = [];
      this.valuationItemDictNew = [];
      GetValuationItemDict(type).then(res => {
        this.valuationItemDialog = true;
        this.valuationItemDict = res.data;
        this.valuationItemDictNew = res.data;
        loading.close();
      })
    },
    /**
     * 项目重新计价
     */
    projectResetImputedPrice() {
      this.projectData.filter((project, index) => {
        if (index === this.projectDataRowIndex) {
          let costs = project.items.reduce((sum, value) => sum + parseFloat(value.costs), 0);
          let charges = project.items.reduce((sum, value) => sum + parseFloat(value.charges), 0);
          project.costs = costs.toString();
          project.charges = charges.toString();
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">
.project-home {

  .dialog-master {
    //padding: 10px;

    .basic-information-master {
      display: flex;
      flex-direction: column;
      align-items: center;

      .basic-item {
        display: flex;
        margin-left: 10px;
        margin-bottom: 3px;

        .basic-input {
          width: 120px;

          ::v-deep.el-input.is-disabled .el-input__inner {
            background-color: #ffffff;
            border-color: #dfe4ed;
            color: #000;
            cursor: not-allowed;
          }
        }

        .basic-text {
          display: flex;
          align-items: center;
        }
      }
    }

    .project-table-master {
      margin-top: 20px;
      display: flex;

      .table-master {
        border: 1px solid #00a19b;
        width: 70%;

      }

      .button-master {
        width: 15%;
        display: flex;
        flex-direction: column;
        align-content: flex-start;
        justify-content: space-around;

        ::v-deep.el-button--medium {
          margin-left: 10px;
        }
      }
    }

    .item-table-master {
      margin-top: 10px;
      border: 1px solid #00a19b;

    }

    .bottom-button-master {
      margin-top: 10px;
      display: flex;
      justify-content: center;

      .button-item {
        display: flex;
        justify-content: space-evenly;
        width: 50%;
      }
    }

    ::v-deep.el-table--medium .el-table__cell {
      padding: 1px 0;
    }

    ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
      word-break: break-word;
      background-color: #f8f8f9;
      color: #515a6e;
      height: 30px;
      font-size: 13px;
    }

    ::v-deep.el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
      background-color: #1890FF;
      color: #FFFFFF;
    }
  }

}

</style>
