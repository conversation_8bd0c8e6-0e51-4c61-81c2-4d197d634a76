<template>
  <div class="user-home">
    <div class="user-avatar">
      <!--        <el-avatar :src="manImages" v-if="user.sex === '男'" size="small"></el-avatar>-->
      <!--        <el-avatar :src="woManImages" v-else-if="user.sex === '女'" size="small"></el-avatar>-->
      <el-avatar :src="manImages"></el-avatar>
    </div>
    <div class="user-message">
      <div class="user-text">{{user.name}}</div>
      <div class="user-text">{{user.empNo}}</div>
    </div>
    <div>
      <el-button icon="el-icon-caret-bottom" type="text"></el-button>
    </div>
  </div>
</template>

<script>
import man from "@/assets/icons/svg/doctor-1.png";
// import woMan from "@/assets/icons/svg/woman.png";
export default {
  name: 'userMessage',
  props: [],
  components: {},
  data() {
    return {
      manImages: man,
      // woManImages: woMan,
      user: this.$store.getters,
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {}
}
</script>

<style scoped lang="scss">
.user-home {
  display: flex;

  .user-avatar {
    margin-top: 4px;
  }

  .user-message {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .My-el-avatar {
    margin-left: 1%;
    margin-outside: 1%;
    margin-top: 0.1%;
    float: left;
  }

  ::v-deep.el-button--medium {
    padding: 0;
    font-size: 14px;
    border-radius: 4px;
  }
  .user-text{
    font-size: 16px;
  }
  @media screen and (max-height: 650px) {
    ::v-deep.el-avatar {
      width: 23px;
      height: 23px;
      line-height: 23px;
    }
    .user-text{
      font-size: 12px;
    }
  }
}
</style>
