<template>
  <div class="my-module-dialog">
    <el-dialog
      title="配置中心"
      :visible.sync="dialogStatus"
      width="60%">
<!--      <div :style="'height:' + (tableHeight - 180) + 'px'">-->
<!--        <el-transfer-->
<!--          filterable-->
<!--          :filter-method="filterMethod"-->
<!--          filter-placeholder="请输入城市拼音"-->
<!--          v-model="value"-->
<!--          :data="data">-->
<!--        </el-transfer>-->
<!--      </div>-->
    </el-dialog>
  </div>
</template>

<script>
/**
 * 配置中心
 */
export default {
  name: 'configCenter',
  props: ['tableHeight'],
  components: {},
  data() {
    return {
      dialogStatus: false,
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    configCenterCreated(status){
      this.dialogStatus = status;
    },
  }
}
</script>

<style scoped lang="scss">
</style>
