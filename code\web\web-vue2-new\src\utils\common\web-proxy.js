function WebProxy () {
  this.config = {
    host: '127.0.0.1',
    port: 8655,
    api: function (api) {
      return 'http://' + this.host + ':' + this.port + '/api/' + api
    }
  }
  this.extend = (moudles) => {
    for (var moudle in moudles) {
      moudles[moudle].config = this.config
      this[moudle] = moudles[moudle]
    }
  }
}

var Client = function () {
  this.run = (callback) => {
    var api = this.config.api('client/run')
    ajax.get(api, (result) => {
      window.wp.run = true
      if (callback) callback()
    })
  }
  this.version = (callback) => {
    var api = this.config.api('client/version')
    ajax.get(api, (result) => {
      if (result.success) {
        window.wp.version = result.data
        if (callback) callback()
      }
    })
  }
}

var Print = function () {
  this.getPrinter = (callback) => {
    var api = this.config.api('printer/get')
    ajax.get(api, (result) => {
      if (callback) callback(result)
    })
  }
  // 退费条码
  this.refundPrint = (model, callback) => {
    var api = this.config.api('ExamPrint/RefundPrint')
    ajax.post(api, model, (result) => {
      if (callback) callback(result)
    })
  }
  // 检验条码
  this.printTest = (model, callback) => {
    var api = this.config.api('test/print')
    ajax.post(api, model, (result) => {
      if (callback) callback(result)
    })
  }
  // 检查确认
  this.printExamConfirm = (model, callback) => {
    var api = this.config.api('ExamPrint/ConfirmPrint')
    ajax.post(api, model, (result) => {
      if (callback) callback(result)
    })
  }
  // 检验确认
  this.printTestConfirm = (model, callback) => {
    var api = this.config.api('test/PrintConfirm')
    ajax.post(api, model, (result) => {
      if (callback) callback(result)
    })
  }
  // 住院腕带
  this.printWristband = (model, callback) => {
    var api = this.config.api('wristband/print')
    ajax.post(api, model, (result) => {
      if (callback) callback(result)
    })
  }
  // 陪护腕带
  this.printPHWristband = (model, callback) => {
    var api = this.config.api('wristband/ph/print')
    ajax.post(api, model, (result) => {
      if (callback) callback(result)
    })
  }
  this.printPatienBedLabel = (model, callback) => {
    var api = this.config.api('print/PrintPatienBedLabel')
    ajax.post(api, model, (result) => {
      if (callback) callback(result)
    })
  }
  // 打印文件
  this.printFile = (model, callback) => {
    var api = this.config.api('print/PrintFile')
    ajax.post(api, model, (result) => {
      if (callback) callback(result)
    })
  }
}

var Card = function () {
  this.readSfz = (callback) => {
    var api = this.config.api('card/sfz/read')
    ajax.get(api, (result) => {
      if (callback) callback(result)
    })
  }
  // 读宏信通卡
  this.readCard = (callback) => {
    var api = this.config.api('card/ReadCard')
    ajax.get(api, (result) => {
      if (callback) callback(result)
    })
  }
}

//* *********************************************汉王签字板插件********************************************
var PenSign = function () {
  this.connect = (callback) => {
    // 打开连接
    var api = this.config.api('api/SignBoard/hw/connect')
    ajax.get(api, (result) => {
      if (callback) callback(result)
    })
  }
  this.close = (callback) => {
    // 关闭连接
    var api = this.config.api('api/SignBoard/hw/close')
    ajax.get(api, (result) => {
      if (callback) callback(result)
    })
  }
  this.clear = (callback) => {
    // 清空画板
    var api = this.config.api('api/SignBoard/hw/clear')
    ajax.get(api, (result) => {
      if (callback) callback(result)
    })
  }
  this.getSign = (callback) => {
    // 获取签字信息
    var api = this.config.api('api/SignBoard/hw/GetSign')
    ajax.get(api, (result) => {
      if (callback) callback(result)
    })
  }
}

var wp = new WebProxy()
wp.extend({
  client: new Client(),
  print: new Print(),
  card: new Card(),
  penSign: new PenSign()
})

// window.wp = wp;

function beforeReturn (option) {
  if (option.xhr.readyState == 4) {
    var result = {}
    if (option.xhr.status == 200) {
      result = JSON.parse(option.xhr.responseText)
      result.success = result.IsSuccess
      delete result.IsSuccess
      result.data = result.Data
      delete result.Data
      result.message = result.Message
      delete result.Message
    } else {
      result.success = false
      if (option.xhr.responseText.length > 0) {
        result.message = option.xhr.responseText
      }
    }
    option.callback(result)
  }
}
var ajax = {
  get: function (url, fn) {
    var xhr = new XMLHttpRequest()
    xhr.open('GET', url, true)
    // xhr.setRequestHeader('If-Modified-Since', '0');
    xhr.onreadystatechange = function () {
      beforeReturn({
        xhr: xhr,
        callback: fn
      })
    }

    if (xhr) {
      xhr.send()
    } else {
      fn.call(this, xhr.responseText)
    }
  },
  post: function (url, data, fn) {
    var xhr = new XMLHttpRequest()
    xhr.open('POST', url, true)
    // xhr.setRequestHeader('If-Modified-Since', '0');
    xhr.onreadystatechange = function () {
      beforeReturn({
        xhr: xhr,
        callback: fn
      })
    }
    xhr.send(JSON.stringify(data))
  }
}

export default wp
