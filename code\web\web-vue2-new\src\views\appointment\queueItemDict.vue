<template>
  <div class="home" :style="myStyle.home.minHeight">
    <div class="home-index">
      <div class="home-left">
        <div class="left-top" :style="myStyle.left.top"></div>
        <div class="left-user-title" :style="myStyle.left.user.title">
          用户基础信息
        </div>
        <div class="left-user" :style="myStyle.left.user.height">
          <div class="user-info" :style="myStyle.left.user.info">
            <div class="user-img">
              <el-avatar :src="user.img" :size="myStyle.left.user.img"></el-avatar>
              <!--              <el-avatar :src="manImg"  :size="myStyle.left.user.img"></el-avatar>-->
              <!--              <el-avatar :src="woManImg" v-else-if="myData.user.sex === '女'" :size="myStyle.left.user.img"></el-avatar>-->
              <!--              <el-avatar :src="manImg" v-else :size="myStyle.left.user.img"></el-avatar>-->
            </div>
            <div class="user-msg">
              <div class="user-msg-1" :style="myStyle.left.user.size">
                <span>姓名：{{ user.name }}</span>
                <span>性别：{{ user.sex }}</span>
              </div>
              <div class="user-msg-1" :style="myStyle.left.user.size">
                <span>职业：{{ user.job }}</span>
                <span>职称：{{ user.title }}</span>
              </div>
              <div class="user-msg-2" :style="myStyle.left.user.size">
                <span>部门：{{ user.deptName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="home-right">
        <div class="right-top" :style="myStyle.right.top">
          <span>
            预约中心字典配置项
          </span>
        </div>
        <div class="right-text" :style="myStyle.right.text">
          请勿修改其他科室的数据!!!
        </div>
        <div class="right-class" :style="myStyle.right.height.height1">
          <div class="right-left">
            <div class="left-tree">
              <div class="tree-text">窗口/诊室</div>
              <el-scrollbar style="overflow-x: hidden" :style="myStyle.right.height.height2">
                <el-tree
                  :data="queueTree" :props="defaultProps"
                  :show-checkbox="false" :highlight-current="true" :expand-on-click-node="false"
                  node-key="value" :default-expanded-keys="idArr" :indent="22">
                  <template #default="{ node, data }">
                    <span v-if="!node.isLeaf" style="display: flex; align-items: center">
                      <el-icon class="el-icon-folder-opened" v-if="node.expanded" style="margin: 0 6px 0 2px"
                               size="20"></el-icon>
                      <el-icon class="el-icon-folder" v-else style="margin: 0 6px 0 2px" size="20"></el-icon>
                      <small>{{ node.label }}</small>
                    </span>
                    <span v-else style="display: flex; align-items: center">
                      <el-icon class="el-icon-tickets" style="margin: 0 6px 0 2px" size="20"></el-icon>
                      <small @click="handleNodeClick(node, data)">{{ node.label }}</small>
                      <span @click="dialogButton(node,data)" style="margin-left: 20%"><el-button
                        type="text">人数配置</el-button></span>
                    </span>
                  </template>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
          <div class="right-right">
            <div class="right-tree">
              <div class="tree-text">项目信息</div>
              <el-scrollbar style="overflow-x: hidden" :style="myStyle.right.height.height2">
                <el-input
                  placeholder="输入关键字进行过滤"
                  v-model="treeSearch">
                </el-input>
                <el-tree
                  :data="itemTree" :props="defaultProps"
                  show-checkbox
                  ref="tree"
                  :highlight-current="true" :expand-on-click-node="false" :filter-node-method="filterNode"
                  node-key="index" :default-expanded-keys="idArr" :indent="22">
                  <template #default="{ node, data }">
                    <span v-if="!node.isLeaf" style="display: flex; align-items: center">
                      <small>{{ node.label }}</small>
                    </span>
                    <span v-else style="display: flex; align-items: center">
                      <small>{{ node.label }}</small>
                        <span @click="saveDates(node,data)" v-if="!data.ors" style="margin-left: 20%"><el-button
                          type="text">新增</el-button></span>
                        <span @click="cancelDates(node,data)" v-if="data.ors" style="margin-left: 20%;"><el-button
                          style="color: red" type="text">取消</el-button></span>
                    </span>
                  </template>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="home-dialog">
      <el-dialog
        :title="doalogTitle"
        :visible.sync="dialogBug"
        width="70%">
        <div style="display: flex;justify-content: center;">
          <el-date-picker
          v-model="saveTimeData.queueDate"
          type="date"
          @change="getTimeCharge"
          value-format="yyyy-MM-dd"
          placeholder="选择日期">
        </el-date-picker>
        </div>
        <el-scrollbar :style="myStyle.right.dialog.height">
        <div>凌晨：</div>
        <div class="dialog-item" >
          <div class="item-son" :style="myStyle.right.dialog.width" v-for="item in timeTree.lc" :key="item.index">
            <div class="son">
              <div class="son1 sonFlex">{{item.label}}</div>
              <div class="son2 sonFlex"> <el-input-number v-model="item.no" controls-position="right" :min="0" :max="20"></el-input-number>
              </div>
            </div>
          </div>
        </div>

        <div>早晨：</div>
        <div class="dialog-item" >
          <div class="item-son" :style="myStyle.right.dialog.width" v-for="item in timeTree.zc" :key="item.index">
            <div class="son">
              <div class="son1 sonFlex">{{item.label}}</div>
              <div class="son2 sonFlex"> <el-input-number v-model="item.no" controls-position="right" :min="0" :max="20"></el-input-number></div>            </div>
          </div>
        </div>

        <div>上午：</div>
        <div class="dialog-item" >
          <div class="item-son" :style="myStyle.right.dialog.width" v-for="item in timeTree.sw" :key="item.index">
            <div class="son">
              <div class="son1 sonFlex">{{item.label}}</div>
              <div class="son2 sonFlex">
                <el-input-number v-model="item.no" controls-position="right" :min="0" :max="20"></el-input-number>
              </div>            </div>
          </div>
        </div>

        <div>中午：</div>
        <div class="dialog-item" >
          <div class="item-son" :style="myStyle.right.dialog.width" v-for="item in timeTree.zw" :key="item.index">
            <div class="son">
              <div class="son1 sonFlex">{{item.label}}</div>
              <div class="son2 sonFlex"> <el-input-number v-model="item.no" controls-position="right" :min="0" :max="20"></el-input-number></div>            </div>
          </div>
        </div>

        <div>下午：</div>
        <div class="dialog-item" >
          <div class="item-son" :style="myStyle.right.dialog.width" v-for="item in timeTree.xw" :key="item.index">
            <div class="son">
              <div class="son1 sonFlex">{{item.label}}</div>
              <div class="son2 sonFlex"> <el-input-number v-model="item.no" controls-position="right" :min="0" :max="20"></el-input-number></div>            </div>
          </div>
        </div>

        <div>傍晚：</div>
        <div class="dialog-item" >
          <div class="item-son" :style="myStyle.right.dialog.width" v-for="item in timeTree.pw" :key="item.index">
            <div class="son">
              <div class="son1 sonFlex">{{item.label}}</div>
              <div class="son2 sonFlex"> <el-input-number v-model="item.no" controls-position="right" :min="0" :max="20"></el-input-number></div>
            </div>
          </div>
        </div>

        <div>晚上：</div>
        <div class="dialog-item" >
          <div class="item-son" :style="myStyle.right.dialog.width" v-for="item in timeTree.ws" :key="item.index">
            <div class="son">
              <div class="son1 sonFlex">{{item.label}}</div>
              <div class="son2 sonFlex"> <el-input-number v-model="item.no" controls-position="right" :min="0" :max="20"></el-input-number></div>            </div>
          </div>
        </div>
        </el-scrollbar>
        <div class="dialog-button">
          <el-button round class="my-button" @click="ConfigQueueTime" type="primary">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  GetUserHoneInfo,
  GetItemTreeList,
  GetQueueTreeList,
  GetConfigConsultingRoomItem,
  ConfigConsultingRoomItem,
  CancelConfigConsultingRoomItem,
  GetQueueTimeTree,
  ConfigQueueTime,
} from "@/api/appointment/queueItem"
import man from "@/assets/icons/svg/man.png";
import woMan from "@/assets/icons/svg/woman.png";

export default {
  name: "queueItemDict",
  data() {
    return {
      manImg: man,
      woManImg: woMan,
      myStyle: {
        home:{
          minHeight:"height: 942px;",
        },
        left:{
          top: "height: 50px;",
          user:{
            height: 'height:150px;',
            info:"height: 150px;",
            title:"margin-top: 17px;height: 35px;font-size: 24px;",
            img:80,
            size:"font-size: 14px;",
            exam: "height: 50px;font-size: 14px;"
          },
        },
        right:{
          top:"height: 50px;font-size: 28px;",
          text:"margin-top: 19px;height: 36px;font-size: 22px;",
          height: {
            height1: 'height: ' + 958 / 1.13 + 'px;',
            height2: 'height: ' + 958 / 1.25 + 'px;',
          },
          dialog:{
            width: '',
            height: ''
          }
        }
      },
      user: {},
      itemTree: [],
      queueTree: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      idArr: [],
      treeSearch: undefined,
      myItemTree: [],
      saveDate: {},
      empNo: '',
      dialogBug: false,
      dialogData: {},
      timeTree: [],
      saveTimeData:{},
      doalogTitle: '',
    }
  },
  methods: {
    ConfigQueueTime(){
      this.saveTimeData.time = this.timeTree;
      const loading = this.$loading({
        lock: true,
        text: '系统正在努力处理中,请勿关闭本页面!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      ConfigQueueTime(this.saveTimeData).then(res => {
        if (res.code === 200){
          this.$message.success(res.message)
          this.dialogBug = false;
        }
      }).finally(r => {
        loading.close();
      })
    },
    //时间字典获取
    getTimeTree(unitId, windowId,date) {
      const loading = this.$loading({
        lock: true,
        text: "数据加载中,请稍后!!!(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      GetQueueTimeTree(unitId, windowId,date).then(res => {
        this.timeTree = res.data;
      }).finally(t => {
        loading.close();
      })
    },
    getTimeCharge(){
      let data = this.saveTimeData;
      this.getTimeTree(data.unitId, data.windowId,data.queueDate)
    },
    //配置时间按钮
    dialogButton(node, data) {
      this.saveTimeData = {};
      this.doalogTitle = "诊室人数配置（当前选择：" + node.parent.data.label + "  >>>  " + data.label + "）"
      let queueDate = this.getTodayDateString();
      this.saveTimeData = {
        unitId: node.parent.data.value,
        unitName: node.parent.data.label,
        windowId: data.value,
        windowName: data.label,
        empNo: this.empNo,
        queueDate: queueDate
      };
      this.getTimeTree(node.parent.data.value, data.value,queueDate);
      this.dialogBug = true;
    },
    //取消字典
    cancelDates(node, data) {
      let c = {
        empNo: this.empNo,
        unitId: this.saveDate.unitId,
        windowId: this.saveDate.windowId,
        tree: {
          value: data.value,
        }
      }
      CancelConfigConsultingRoomItem(c).then(res => {
        if (res.code === 200) {
          this.$message.error("您已取消取消配置")
          let o = data.ors
          data.ors = !o;
          this.getConfigConsultingRoomItem(this.saveDate.unitId, this.saveDate.windowId);
        }
      })
    },
    //新增
    saveDates(node, data) {
      this.saveDate.EmpNo = this.empNo;
      let c = {
        label: data.label,
        value: data.value,
      }
      this.saveDate.tree = c;
      ConfigConsultingRoomItem(this.saveDate).then(res => {
        if (res.code === 200) {
          this.$message.success("添加成功")
          let o = data.ors
          data.ors = !o;
          this.getConfigConsultingRoomItem(this.saveDate.unitId, this.saveDate.windowId);
        }
      })
    },
    //获取已配置信息  进行回显
    getConfigConsultingRoomItem(unitId, windowId) {
      GetConfigConsultingRoomItem(unitId, windowId).then(res => {
        if (res.data.length > 0 && this.$refs.tree) {
          this.$refs.tree.setCheckedKeys(res.data);
        }else{
          // 当没有数据时，清空选中项
          this.$refs.tree.setCheckedKeys([]);
        }
      })
    },
    //关键字过滤
    filterNode(value, data) {
      if (!value) return true;
      return data && data.label && data.label.indexOf(value) !== -1;
    },
    //节点点击
    handleNodeClick(node, data) {
      let unitIds = node.parent.data.value;
      let windowIds = data.value
      if (unitIds && windowIds) {
        this.saveDate = {
          unitId: unitIds,
          unitName: node.parent.data.label,
          windowId: windowIds,
          windowName: data.label,
          windowNo: data.no,
        }
        console.log(this.saveDate)
        this.getItemTreeList(unitIds, windowIds);
        this.getConfigConsultingRoomItem(unitIds, windowIds);
      }

    },
    //获取用户信息
    getUserHoneInfo(empNo) {
      GetUserHoneInfo(empNo).then(res => {
        this.user = res.data;
      })
    },
    //获取右侧字典信息
    getItemTreeList(unitIds, windowIds) {
      this.itemTree = [];
      const loading = this.$loading({
        lock: true,
        text: '数据加载中,请稍后!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      GetItemTreeList(unitIds, windowIds).then(res => {
        this.itemTree = res.data;
      }).finally(r => {
        loading.close();
      })
    },
    //获取左侧字典信息
    getQueueTreeList() {
      GetQueueTreeList().then(res => {
        this.queueTree = res.data;
      })
    },
    getTodayDateString() {
      const today = new Date();
      const year = today.getFullYear();
      let month = today.getMonth() + 1; // 月份是从0开始的
      let day = today.getDate();
      // 月份和日期小于10时前面补0
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;
      // 返回格式为 YYYY-MM-DD 的字符串
      return `${year}-${month}-${day}`;
    }
  },
  created() {
    let empNo = this.$route.query && this.$route.query.empNo;
    this.empNo = empNo;
    this.getUserHoneInfo(empNo);
    this.getQueueTreeList();

  },
  mounted() {
    this.$nextTick(() => {
      const bodyStyle = document.body.style, // 获取body节点样式
        htmlStyle = document.getElementsByTagName("html")[0].style, // 获取html节点样式
        docEl = document.documentElement,
        docBody = document.body,
        winWidth = docEl.clientWidth || docBody.clientWidth, // 获取浏览器的宽度
        WinHeight = docEl.clientHeight || docBody.clientHeight; // 获取浏览器的高
      this.bodyHeight = "height:" + WinHeight + "px";
      bodyStyle.minWidth = "1014px";
      bodyStyle.minHeight = "768px";
      htmlStyle.minHeight = "768px";
      htmlStyle.minWidth = "1014px";
      if (winWidth <= 1200) {
        this.myStyle = {
          home:{
            minHeight:"height: 753px;",
          },
          left:{
            top: "height: 40px;background-size: 100% auto;",
            user:{
              height: 'height:130px;',
              info:"height: 100px;",
              title:"margin-top: 10px;height: 25px;font-size: 18px;",
              img:45,
              size:"font-size: 12px;",
              exam: "height: 29px;font-size: 12px;"
            },
          },
          right:{
            top:"height: 40px;font-size: 24px;",
            text:"height: 26px;margin-top: 10px;",
            height: {
              height1: 'height: ' + 758 / 1.18 + 'px;',
              height2: 'height: ' + 758 / 1.23 + 'px;',
            },
            dialog:{
              width: 'width: 32%;',
              height: 'height: ' + 758 / 1.5 + 'px;',
            }
          }
        }
      } else if (winWidth <= 1400) {
        this.myStyle = {
          home:{
            minHeight:"height: 1010px;",
          },
          left:{
            top: "height: 40px;background-size: 100% auto;",
            user:{
              height: 'height:200px;',
              info:"height: 150px;",
              title:"margin-top: 27px;height: 35px;font-size: 22px;",
              img:55,
              size:"font-size: 14px;",
              exam: "height: 50px;font-size: 14px;"
            },
          },
          right:{
            top:"height: 50px;font-size: 26px;",
            text:"height: 41px;margin-top: 32px; font-size: 25px",
            height: {
              height1: 'height: ' + 1010 / 1 + 'px;',
              height2: 'height: ' + 1010 / 1.15 + 'px;',
            },
            dialog:{
              width: '',
              height: 'height: ' + 1010 / 1.5 + 'px;'
            }
          }
        }
      } else {
        this.myStyle ={
          home:{
            minHeight:"height: 942px;",
          },
          left:{
            top: "height: 50px;",
            user:{
              height: 'height:150px;',
              info:"height: 150px;",
              title:"margin-top: 17px;height: 35px;font-size: 24px;",
              img:80,
              size:"font-size: 14px;",
              exam: "height: 50px;font-size: 14px;"
            },
          },
          right:{
            top:"height: 50px;font-size: 28px;",
            text:"margin-top: 19px;height: 36px;font-size: 22px;",
            height: {
              height1: 'height: ' + 958 / 1.13 + 'px;',
              height2: 'height: ' + 958 / 1.25 + 'px;',
            },
            dialog:{
              width: '',
              height: 'height: ' + 958 / 1.5 + 'px;'
            }
          }
        }
      }
    });
  },
  watch: {
    treeSearch(val) {
      if (this.$refs.tree) {
        this.$refs.tree.filter(val);
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.home {
  width: 99%;
  margin: 0 auto;
  margin-top: 2px;
  padding-right: 5px;

  .home-index {
    margin-top: 2px;
    height: 100%;
    border: 1px solid #A7C8FE;
    box-shadow: 5px 5px 5px #A7C8FE;
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
  }
}

.home-left {
  width: 23%;
  border-right: 1px dashed #A7C8FE;

  .left-top {
    height: 100%;
    background: url("../../assets/logo/honlivhpOne.png") 35% no-repeat;
  }

  .left-user-title {
    background: #185F7D;
    text-align: center;
    color: #FFFFFF;
  }

  .left-user {
    background: rgb(#b4bccc, 0.1);

    .user-info {
      display: flex;
      border-bottom: 1px solid #b4bccc;

      .user-img {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        width: 20%;
        height: 100%;
        border-right: 1px solid #b4bccc;

        ::v-deep.el-avatar > img {
          width: 100%;
        }
      }

      .user-msg {
        width: 80%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;

        .user-msg-1 {
          height: 33.3%;
          display: flex;
          align-items: center;
          justify-content: space-around;

          span {
            width: 45%;
          }
        }

        .user-msg-2 {
          margin-left: 3%;
        }
      }
    }
  }
}

.home-right {
  width: 77%;
  display: flex;
  flex-direction: column;

  .right-top {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    background: #185F7D;
    color: #FFFFFF;
    border-radius: 0px 10px 0px 0px;

    span {
      margin-left: 1%;
    }
  }

  .right-text {
    margin-top: 17px;
    height: 35px;
    border: 1px solid #20B2AA;
    color: #FFFFFF;
    background: red;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 20px;
    text-indent: 1.5em;
    letter-spacing: 0.1em;
  }

  .tree-text {
    border: 1px solid #20B2AA;
    background-color: #20B2AA;
    color: #ffffff;
    text-align: center;
    font-size: 20px;
  }

  .right-class {
    margin-top: 20px;
    display: flex;
    height: 700px;
    justify-content: space-between;

    .right-left {
      border-bottom: 1px solid #20B2AA;
      border-right: 1px solid #20B2AA;
      width: 35%;

      .left-tree {
        height: 100%;

        ::v-deep.el-tree {
          /* ---- ---- ---- ---- ^（节点对齐）---- ---- ---- ---- */
          .el-tree-node {
            /* / 已展开的父节点 */
            /* ^ 叶子节点 */
            i.el-tree-node__expand-icon.is-leaf {
              &::before {
                display: none;
              }
            }

            /* / 叶子节点 */
            /* ^ 复选框 */
            .el-checkbox {
              margin: 0 7px 0 2px;

              .el-checkbox__inner {
                width: 14px;
                height: 14px;
                border-radius: 2px;
                border: 1px solid #bbb;
              }

              .el-checkbox__input.is-checked .el-checkbox__inner,
              .el-checkbox__input.is-indeterminate .el-checkbox__inner {
                border: 1px solid #5e7ce0;
              }
            }

            /* / 复选框 */
            .el-tree-node__content {
              small {
                font-size: 13px;
              }
            }
          }

          /* ---- ---- ---- ---- /（节点对齐）---- ---- ---- ---- */
          /* ---- ---- ---- ---- ^（文字高亮）---- ---- ---- ---- */
          .el-tree-node.is-current {
            .el-tree-node__content {
              small {
                color: #5e7ce0;
              }
            }

            .el-tree-node__children {
              small {
                color: unset;
              }
            }
          }

          /* ---- ---- ---- ---- /（文字高亮）---- ---- ---- ---- */
          /* ---- ---- ---- ---- ^（新增辅助线）---- ---- ---- ---- */
          /* ^ 树节点 */
          .el-tree-node {
            position: relative;
            width: auto;
            // width: max-content; // 显示文字宽度
            padding-left: 10px;

            &::before {
              width: 1px;
              height: 100%;
              content: "";
              position: absolute;
              top: -38px;
              bottom: 0;
              left: 0;
              right: auto;
              border-width: 1px;
              border-left: 1px solid #b8b9bb;
            }

            &::after {
              width: 16px;
              height: 13px;
              content: "";
              position: absolute;
              z-index: 0;
              left: 0;
              right: auto;
              top: 12px;
              bottom: auto;
              border-width: 1px;
              border-top: 1px solid #b8b9bb;
            }

            .el-tree-node__content {
              position: relative;
              z-index: 1;
              color: #000;
              padding-left: 0 !important;
              /* ^ 复选框 */
              .el-checkbox {
                margin: 0 10px 0 5.5px;
              }

              /* / 复选框 */
            }

            .el-tree-node__children {
              padding-left: 12px;
            }

            &:last-child::before {
              height: 50px;
            }
          }

          /* / 树节点 */
          /* ^ 第一层节点 */
          > .el-tree-node {
            padding-left: 0;

            &::before {
              border-left: none;
            }

            &::after {
              border-top: none;
            }
          }

          /* / 第一层节点 */
          /* ^ 叶子节点 */
          i.el-tree-node__expand-icon.is-leaf {
            display: none;
          }

          /* / 叶子节点 */
          /* ^ 设置子节点左外边距 */
          .el-tree-node__content:has(.is-leaf) {
            // color: #00ffff;
            margin-left: 0px !important;
          }

          /* / 设置子节点左外边距 */
          /* ---- ---- ---- ---- /（新增辅助线）---- ---- ---- ---- */
        }
      }
    }

    .right-right {
      width: 70%;
      border-bottom: 1px solid #20B2AA;

      .right-tree {
        height: 100%;

        ::v-deep.el-checkbox__input.is-indeterminate .el-checkbox__inner {
          background-color: #409eff !important;
          border-color: #409eff !important;
        }

        ::v-deep.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
          background-color: #409eff !important;
          border-color: #409eff !important;
        }

        ::v-deep.el-checkbox__input.is-disabled .el-checkbox__inner {
          background-color: #FFFFFF;
          border-color: #DCDFE6;
          cursor: not-allowed;
        }
      }

    }
  }
}

.home-dialog {

  .dialog-item{
    margin-top: 10px;
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    .item-son{
      border-radius: 10px;
      width: 24%;
      height: 50px;
      border: 1px solid #20B2AA;
      margin-right: 1%;
      margin-top: 2px;
    }
    .son{
      display: flex;
      height: 49px;
      .son1{
        width: 54%;
        border-right: 1px solid #20B2AA;
        border-bottom: 1px solid #20B2AA;
        background: floralwhite;
        border-radius: 10px 0 0 10px;
      }
      .son2{
        width: 43%;
        ::v-deep.el-input-number.is-controls-right[class*=medium] [class*=increase], .el-input-number.is-controls-right[class*=medium] [class*=decrease] {
          line-height: 23px;
        }

        .el-input-number--medium .el-input-number__increase, .el-input-number--medium .el-input-number__decrease {
          width: 30px;
          font-size: 14px;
        }

        ::v-deep.el-input--medium .el-input__inner{
          height: 49px;
        }

        ::v-deep.el-input__inner{
          border: none;
          border-bottom: 1px solid #20B2AA;
        }
      }
      .sonFlex{
        display: flex;
        align-items: center;
        justify-content: space-around;
      }
    }
  }

  .dialog-button{
    margin-top: 15px;
    display: flex;
    justify-content: space-around;

    .my-button{
      width: 30%;
    }

  }
}
</style>
