<template>
  <div class="element-table">
    <el-table v-loading="loading" :data="tableData" style="width: 100%" border :height="tableHeight">
      <el-table-column type="index" width="50" align="center"></el-table-column>
      <el-table-column prop="DRUG_NAME" align="center" label="药品名称" width="200"></el-table-column>
      <el-table-column prop="DRUG_SPEC" align="center" label="规格" width="90"></el-table-column>
      <el-table-column prop="PACKAGE_SPEC" align="center" label="包装规格" width="90"></el-table-column>
      <el-table-column prop="QUANTITY" align="center" label="数量" width="70"></el-table-column>
      <el-table-column prop="PRICE" align="center" label="进价" width="90"></el-table-column>
      <el-table-column prop="JJJE" align="center" label="进价金额" width="90"></el-table-column>
      <el-table-column prop="PURCHASE_PRICE" align="center" label="零价" width="90"></el-table-column>
      <el-table-column prop="LJJE" align="center" label="零价金额" width="90">
      </el-table-column>
      <el-table-column align="center" label="差价金额" width="90">
        <template slot-scope="scope">
          <div>{{(scope.row.LJJE - scope.row.JJJE).toFixed(2)}}</div>
        </template>
      </el-table-column>
      <el-table-column prop="FIRM_ID" align="center" label="厂家" width="120"></el-table-column>
      <el-table-column prop="SUPPLIER" align="center" label="供应商" width="180"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import {getDrugStatementTable} from '@/api/singlePage/drugStatement'
/**
 * 按药品名称汇总
 */
export default {
  name: 'drugNameSummarizing',
  props: ['tableHeight','queryForm'],
  components: {},
  data() {
    return {
      loading: false,
      tableData: [],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    getTable(){
      this.loading = true;
      getDrugStatementTable(this.queryForm).then(res => {
        if (res.code === 200){
          this.tableData = res.data;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../../assets/styles/singlePage";
</style>
