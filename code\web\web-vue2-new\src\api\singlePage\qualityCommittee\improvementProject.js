// 质量改进项目模块
import request from '@/utils/request'

export function GetHisDeptDict() {
    return request({
        url: '/ImprovementProject/GetHisDeptDict',
        method: 'post'
    })
}

export function ImprovementProjectFile(data) {
    return request({
        url: '/ImprovementProject/ImprovementProjectFile',
        method: 'post',
        data: data
    })
}

export function GetImprovementProjectFile(data) {
    return request({
        url: '/ImprovementProject/GetImprovementProjectFile',
        method: 'post',
        data: data
    })
}

export function ImprovementProjectFileDel(fileid) {
    return request({
        url: '/ImprovementProject/ImprovementProjectFileDel?fileid=' + fileid,
        method: 'get'
    })
}