<template>
  <div></div>
</template>

<script>
/**
 * 现场报道功能
 */
import {PatientReport} from "@/api/checkAndConfirm/checkAppointment"
export default {
  name: 'liveReport',
  props: [],
  components: {},
  data() {
    return {}
  },
  created() {
  },
  mounted() {
  },
  methods: {
    report(data) {
      if (!data) {
        this.$msgbox.alert(
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' + '请先点击想要报道的项目' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        ).then(() => {
          this.patientMessageButton = true
        })
        return;
      }
      PatientReport(data.examNo, this.$store.getters.empNo).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message);
        }
      });
    },
  }
}
</script>

<style scoped lang="scss">

</style>
