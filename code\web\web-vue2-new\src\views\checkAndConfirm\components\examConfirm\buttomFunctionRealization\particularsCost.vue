<template>
  <div class="cost-home">
    <div class="cost-master">
      <div class="cost-title">预交金额:</div>
      <div class="cost-item">{{conts.prepayments}}</div>
    </div>
    <div class="cost-master" style="margin-left: 5px;">
      <div class="cost-title">累计费用:</div>
      <div class="cost-item">{{conts.totalCharges}}</div>
    </div>
  </div>
</template>

<script>
/**
 * 费用详情
 */
import {GetPatientMoneyCorrelationMessage} from "@/api/checkAndConfirm/checkConfirm"
import logo from '../../../../../layout/components/Sidebar/Logo.vue'
export default {
  name: 'particularsCost',
  props: [],
  components: {},
  data() {
    return {
      conts: {
        prepayments: '',
        totalCharges: '',
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    getMoney(data) {
      GetPatientMoneyCorrelationMessage(data).then(res => {
        if (res.data) {
          this.conts = res.data;
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">
.cost-home {
  display: flex;

  .cost-master {
    display: flex;

    .cost-title {
      font-size: 16px;
    }

    .cost-item {
      text-align: center;
      font-size: 14px;
      border-bottom: 1px solid #000000;
      width: 130px;
    }
  }
}
</style>
