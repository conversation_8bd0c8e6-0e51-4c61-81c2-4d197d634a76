<template>
    <div class="single-master">
        <div class="single-title">新生儿报表</div>
        <div class="single-element">
            <div class="element-master">
                <div class="element-form">
                    <el-form :inline="true" :model="queueForm" class="demo-form-inline">
                        <el-form-item label="日期选择:">
                            <el-date-picker v-model="queueForm.beginDate" type="date" value-format="yyyy-MM-dd"
                                format="yyyy-MM-dd" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            到
                        </el-form-item>
                        <el-form-item>
                            <el-date-picker v-model="queueForm.endDate" type="date" value-format="yyyy-MM-dd"
                                format="yyyy-MM-dd" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="病人ID:">
                            <el-input v-model="queueForm.patientId" placeholder="请输入患者id" clearable></el-input>
                        </el-form-item>
                        <el-form-item>
                            <div class="element-button">
                                <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="element-table">
                    <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 180" highlight-current-row
                        @cell-dblclick="doubleSelectionChange">
                        <el-table-column type="index" :index="indexMethod" align="center" width="40" />
                        <el-table-column prop="patienT_ID" align="center" label="病人ID" />
                        <el-table-column prop="name" align="center" label="临时姓名" />
                        <el-table-column prop="sex" align="center" label="性别" />
                        <el-table-column prop="datE_OF_BIRTH" align="center" label="出生时间" />
                        <el-table-column prop="birtH_CERTIFICATION_NO" align="center" label="出生证号" />
                        <el-table-column prop="healtH_STATUS" align="center" label="健康状况" />
                        <el-table-column prop="namE_OF_MOTHER" align="center" label="母亲姓名" />
                        <el-table-column prop="admissioN_DATE_TIME" align="center" label="母亲住院时间" />
                        <el-table-column prop="mailinG_ADDRESS" align="center" label="家庭住址" />
                    </el-table>
                    <!-- 分页 -->
                    <div>
                        <pagination v-show="total > 0" :limit.sync="queueForm.pageSize" :page.sync="queueForm.pageNum"
                            :total="total" @pagination="getList" />
                    </div>
                </div>
                <!-- DialogOne弹框 -->
                <el-dialog :visible.sync="dialogOneOpen" :title="title" width="90%">
                    <dialog-one @success-listener="listenerMethod" :row-data="rowData"
                        :max-height="'height:' + (tableHeight - 250) + 'px'" :key="extraFunctionKey" />
                </el-dialog>
            </div>
        </div>
    </div>
</template>
  
<script>
import {
    GetNewbornList
} from "@/api/singlePage/newbornStatistics";
import DialogOne from './components/dialogOne.vue' // 组件引用
export default {
    name: 'reportCardReview',
    props: [],
    components: {
        DialogOne
    },
    data() {
        return {
            queueForm: {
                pageNum: 1,
                pageSize: 20,
                beginDate: this.formatDate(new Date()),
                endDate: this.formatDate(new Date()),
            },
            tableDate: [],
            total: 0,
            tableHeight: undefined,
            extraFunctionKey: 0,
            dialogOneOpen: false,
            title: '新生儿明细',
            rowData: {},
        }
    },

    created() {
        this.getList();
        this.handleResize();
    },

    mounted() {
        window.addEventListener('resize', this.handleResize); // 添加监听器
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize); // 移除监听器
    },

    methods: {
        // 初始化数据
        getList() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            // 初始化数据
            GetNewbornList(this.queueForm).then(res => {
                this.tableDate = res.data.list;
                this.total = res.data.total[0].total;
            }).finally(() => {
                loading.close();
            });
        },

        // 双击某一行操作
        doubleSelectionChange(row) {
            this.rowData = row;
            ++this.extraFunctionKey;
            this.dialogOneOpen = true;
        },

        // 监听弹框事件,如果收到子组件传递的参数,则调用该方法
        listenerMethod(data) {
            this.dialogOneOpen = !data;
            this.getList();
        },

        // 序号翻页递增
        indexMethod(index) {
            let nowPage = this.queueForm.pageNum; //当前第几页，根据组件取值即可
            let nowLimit = this.queueForm.pageSize; //当前每页显示几条，根据组件取值即可
            return index + 1 + (nowPage - 1) * nowLimit; // 这里可以理解成一个公式
        },

        // 默认当前时间
        formatDate(date) {
            const year = date.getFullYear()
            const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
            const day = date.getDate().toString().padStart(2, '0')
            return `${year}-${month}-${day}`
        },

        // 自定义高度变化更新高度
        handleResize() {
            this.tableHeight = window.innerHeight; // 更新高度数据
        },
    }
}
</script>
  
<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
</style>
  