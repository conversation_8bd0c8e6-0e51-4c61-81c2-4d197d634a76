import request from "@/utils/request";

export function GetReservationCenterInfo(examNo,empNO) {
  return request({
    url: "/ReservationCenter/GetReservationCenterInfo?examNo=" + examNo + '&empNo=' +empNO,
    method: "get",
  });
}

export function GetReservationCenterInfoTwo(data) {
  return request({
    url: "/ReservationCenter/GetReservationCenterInfoTwo",
    method: "post",
    data: data,
  });
}

export function GetUnitOrWindowTree(data) {
  return request({
    url: "/ReservationCenter/GetUnitOrWindowTree",
    method: "post",
    data: data,
  });
}

export function GetUnitAndWindowTree(data) {
  return request({
    url: "/ReservationCenter/GetUnitAndWindowTree",
    method: "post",
    data:data,
  });
}

export function GetWindowTimeMessage(data) {
  return request({
    url: "/ReservationCenter/GetWindowTimeMessage",
    method: "post",
    data:data,
  });
}

export function GetCallQueueList(data) {
  return request({
    url: "/ReservationCenter/GetCallQueueList",
    method: "post",
    data:data,
  });
}

export function GetCallQueueMessage(data) {
  return request({
    url: "/ReservationCenter/GetCallQueueMessage",
    method: "post",
    data:data,
  });
}

export function DeleteCallQueueByQueueId(data) {
  return request({
    url: "/ReservationCenter/DeleteCallQueueByQueueId",
    method: "put",
    data:data,
  });
}

export function CallTransfer(data) {
  return request({
    url: "/ReservationCenter/CallTransfer",
    method: "put",
    data:data,
  });
}

export function RecoverCallQueue(data) {
  return request({
    url: "/ReservationCenter/RecoverCallQueue",
    method: "put",
    data:data,
  });
}

export function UpdateMark(data) {
  return request({
    url: "/ReservationCenter/UpdateMark",
    method: "put",
    data:data,
  });
}

export function GetAppointmentTimeList(data) {
  return request({
    url: "/ReservationCenter/GetAppointmentTimeList",
    method: "post",
    data: data,
  });
}

export function TimeAppointment(data) {
  return request({
    url: "/ReservationCenter/TimeAppointment",
    method: "post",
    data: data,
  });
}

export function GetTimeDispose(data) {
  return request({
    url: "/ReservationCenter/GetTimeDispose?date=" + data,
    method: "get",
  });
}

export function GetAppointmentListFormUnitTree(data) {
  return request({
    url: "/ReservationCenter/GetAppointmentListFormUnitTree?deptCode=" + data,
    method: "get",
  });
}
export function GetAppointmentListFormWindowTree(data) {
  return request({
    url: "/ReservationCenter/GetAppointmentListFormWindowTree?unitId=" + data,
    method: "get",
  });
}

export function GetAppointmentList(data) {
  return request({
    url: "/ReservationCenter/GetAppointmentList",
    method: "post",
    data: data,
  });
}

//叫号登记相关
export function GetManualInputFormUnitTree(data) {
  return request({
    url: "/ReservationCenter/GetManualInputFormUnitTree?deptCode=" + data,
    method: "get",
  });
}
export function GetManualInputFormFormWindowTree(data) {
  return request({
    url: "/ReservationCenter/GetManualInputFormFormWindowTree?unitId=" + data,
    method: "get",
  });
}
export function GetManualInputFormPatient(data) {
  return request({
    url: "/ReservationCenter/GetManualInputFormPatient?patientId=" + data,
    method: "get",
  });
}
export function SaveManualInputForm(data) {
  return request({
    url: "/ReservationCenter/SaveManualInputForm",
    method: "post",
    data: data,
  });
}
//住院通知相关
export function GetBeHospitalizedInformMessage(data) {
  return request({
    url: "/ReservationCenter/GetBeHospitalizedInformMessage",
    method: "post",
    data:data,
  });
}
export function GetBeHospitalizedInformTemplate(data) {
  return request({
    url: "/ReservationCenter/GetBeHospitalizedInformTemplate",
    method: "get",
  });
}
export function SendBeHospitalizedInform(data) {
  return request({
    url: "/ReservationCenter/SendBeHospitalizedInform",
    method: "post",
    data:data,
  });
}
