import request from "@/utils/request2";

export function getJournalismPageList(data) {
  return request({
    url: "Journalism/pageList/company",
    method: "post",
    data: data,
  });
}

export function getJournalismPageListOuterNet(data) {
  return request({
    url: "Journalism/pageList/outerNet",
    method: "post",
    data: data,
  });
}

export function newsFileDetecting(id,type) {
  return request({
    url: "Journalism/detecting/" + id + "/" + type,
    method: "post",
  });
}

export function oaSyuchronizationById(id) {
  return request({
    url: "Journalism/syuchronization/" + id,
    method: "post",
  });
}

export function oaHpoa1005SyuchronizationById(id) {
  return request({
    url: "Journalism/Hpoa1005/syuchronization/" + id,
    method: "post",
  });
}

export function getNewHtmlThenBase64(id,type) {
  return request({
    url: "Journalism/syuchronization/html/" + id + "/" + type,
    method: "post",
  });
}
