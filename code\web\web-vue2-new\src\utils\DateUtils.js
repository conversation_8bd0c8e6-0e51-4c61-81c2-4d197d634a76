

const thisDate = new Date();
const year = thisDate.getFullYear()
const month = (thisDate.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
const day = thisDate.getDate().toString().padStart(2, '0')
const hours = thisDate.getHours().toString().padStart(2, '0');
const minutes = thisDate.getMinutes().toString().padStart(2, '0');
const seconds = thisDate.getSeconds().toString().padStart(2, '0');
/**
 * 获取当前日期 默认 yyyy-MM-dd
 * @returns {string}
 */
export function getThisDateDefault(){
  return `${year}-${month}-${day}`
}

export function getThisDateMonthBeginDefault(){
  return `${year}-${month}-01`
}

export function getThisDateYearBeginDefault(){
  return `${year}-01-01`
}

/**
 * 获取当前日期
 * @param formatSymbol 格式化符号
 * @returns {string}
 */
export function getThisDateDefined(formatSymbol){
  return `${year}${formatSymbol}${month}${formatSymbol}${day}`
}


/**
 * 获取当前日期时间 默认 yyyy-MM-dd HH:mm:ss
 * @returns {string}
 */
export function getThisDateTimeDefault(){
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * 获取当前日期时间 自定义 + HH:mm:ss
 * @param formatDateSymbol 格式化符号
 * @returns {string}
 */
export function getThisDateTimeDefinedDate(formatDateSymbol){
  return this.getThisDateDefined(formatDateSymbol) + ` ${hours}:${minutes}:${seconds}`
}
