import request from '@/utils/request'

// 初始化数据
export function WaterElectricityFeeList(data) {
    return request({
        url: '/WaterElectricityFee/WaterElectricityFeeList',
        method: 'post',
        data: data
    })
}

// 现金缴费按钮操作
export function ChangeCashPayment(data) {
    return request({
        url: '/WaterElectricityFee/ChangeCashPayment',
        method: 'post',
        data: data
    })
}

// 未缴费费用修改按钮操作
export function ChangeCostModification(data) {
    return request({
        url: '/WaterElectricityFee/ChangeCostModification',
        method: 'post',
        data: data
    })
}

// 未缴费处理为自助机不显示按钮操作
export function ChangeNoDisplay(data) {
    return request({
        url: '/WaterElectricityFee/ChangeNoDisplay',
        method: 'post',
        data: data
    })
}

// 未缴费处理为自助机不显示按钮操作
export function ChangeDelPayFees(data) {
    return request({
        url: '/WaterElectricityFee/ChangeDelPayFees',
        method: 'post',
        data: data
    })
}