<template>
  <div>
    <el-dialog title="打印预览" top="2vh" :visible.sync="dialogVisible" width="85%" :show-close="true"
               :close-on-press-escape="true" :close-on-click-modal="true"
    >
      <print-components @page-size="totalSizeClick"
                        :print-data="printDatas"
                        :key="printKey"
                        :print-data-list="printDatas.printDataList"
                        :table-title="printDatas.tableTitle"
                        :page-html="printDatas.pageHtml"
                        :total-page="printDatas.totalPage"
                        :total-size="printDatas.totalSize"
                        :merge-data="printDatas.mergeData"
                        :is-merge="printDatas.isMerge"
                        :title-html="printDatas.titleHtml"
                        :bottom-html="printDatas.bottomHtml"
                        :merge-size="printDatas.mergeSize">
      </print-components>
    </el-dialog>
  </div>
</template>

<script>
import { getDrugStatementTable } from '@/api/singlePage/drugStatement'
import PrintComponents from '../../../../components/print/printComponents.vue'

/**
 * 药品历史价格查询
 */
export default {
  name: 'drugHistoryPring',
  props: [],
  components: { PrintComponents },
  data() {
    return {
      printDatas: {
        printDataList: [],//打印数据
        tableTitle: [{
          key: 'DOCUMENT_NO',
          title: '入库单号',
          width: 'width: 15%'
        }, {
          key: 'DRUG_NAME',
          title: '药品名称',
          width: 'width: 20%'
        }, {
          key: 'DRUG_SPEC',
          title: '规格',
          width: 'width: 5%'
        }, {
          key: 'PACKAGE_SPEC',
          title: '包装规格',
          width: 'width: 5%'
        }, {
          key: 'PACKAGE_UNITS',
          title: '包装单位',
          width: 'width: 5%'
        }, {
          key: 'UNITS',
          title: '单位',
          width: 'width: 5%'
        }, {
          key: 'PRICE',
          title: '价格',
          width: 'width: 5%'
        }, {
          key: 'PURCHASE_PRICE',
          title: '进价',
          width: 'width: 5%'
        }, {
          key: 'QUANTITY',
          title: '数量',
          width: 'width: 5%'
        }, {
          key: 'SUPPLIER',
          title: '供货商',
          width: 'width: 20%'
        }, {
          key: 'DISCOUNT',
          title: '折扣',
          width: 'width: 5%'
        }, {
          key: 'RETAIL_PRICE',
          title: '零售价',
          width: 'width: 5%'
        }, {
          key: 'FIRM_ID',
          title: '厂家标识',
          width: 'width: 5%'
        }, {
          key: 'IMPORT_DATE',
          title: '入库日期',
          width: '6vw'
        }],//标题数据
        pageHtml: '',//是否page同行
        totalPage: 0,//总行数
        totalSize: 13,//打印页每页显示条数
        mergeData: [],//合并数据
        isMerge: true,//是否合并
        titleHtml: '药品历史价格',//每页title文字（需要带样式）
        bottomHtml: '',//每页底部文字（需要带样式）
        mergeSize: 7,
      },
      printKey: 0,
      dialogVisible: false,
      jg:[],
      jj:[],
      sl:[],
      zk:[],
      lsj: [],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    totalSizeClick(data) {
      ++this.printKey;
      this.printDatas.totalSize = data
      this.chengeRows(data);
    },
    printData(data) {
      getDrugStatementTable(data).then(res => {
        if (res.code === 200) {
          this.tableData = res.data
          this.chengeRows(this.printDatas.totalSize)
          this.dialogVisible = true;
        }
      })
    },
    chengeRows(val) {
      let tableData = this.sliceArr(this.tableData, val) //按照行数将表格数据切割为二维数组
      this.printDatas.printDataList = tableData
      this.printDatas.totalPage = tableData.length //这里拿到的就是总页面数
      tableData.forEach((item, index) => {
        let jg = 0
        let jj = 0
        let sl = 0
        let zk = 0
        let lsj = 0
        item.forEach((i, j) => {
          jg += i.PRICE //这个就是要计算的列的参数名合计，并且这里是按照每页计算的
          jj += i.PURCHASE_PRICE
          sl += i.QUANTITY
          zk += i.DISCOUNT
          lsj += i.RETAIL_PRICE
        })
        this.$set(this.jg, index, jg.toFixed(2))
        this.$set(this.jj, index, jj.toFixed(2))
        this.$set(this.sl, index, sl.toFixed(2))
        this.$set(this.zk, index, zk.toFixed(2))
        this.$set(this.lsj, index, lsj.toFixed(2))
        this.$set(this.printDatas.printDataList, index, item)
      })
      this.printDatas.mergeData = [this.jg, this.jj, this.sl,[], this.zk, this.lsj,[],[]]
    },
    sliceArr(array, size) {
      if (array.length === 0) {
        return []
      }
      const numOfChunks = Math.ceil(array.length / size)
      const chunks = new Array(numOfChunks)
      for (let i = 0, j = 0; i < numOfChunks; i++) {
        chunks[i] = array.slice(j, j + size)
        j += size
      }
      return chunks
    }
  }
}
</script>

<style scoped lang="scss">

</style>
