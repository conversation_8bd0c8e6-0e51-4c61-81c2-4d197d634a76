<template>
  <div class="element-table">
    <el-table v-loading="loading" :data="tableDate" style="width: 100%" border :height="tableHeight" :show-summary="true">
      <el-table-column type="index" width="40" align="center"></el-table-column>
      <el-table-column prop="SUPPLIER" align="center" label="公司名称" width="300"></el-table-column>
      <el-table-column prop="西药库" align="center" label="西药库" width="110"></el-table-column>
      <el-table-column prop="中成药库" align="center" label="中药库" width="110"></el-table-column>
      <el-table-column prop="草药库" align="center" label="草药库" width="110"></el-table-column>
      <el-table-column prop="试剂库" align="center" label="试剂库" width="110"></el-table-column>
      <el-table-column prop="合计" align="center" label="合计" width="110"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import {getDrugStatementTable} from '@/api/singlePage/drugStatement'
/**
 * 按医药公司供货查询
 */
export default {
  name: 'supplierCondition',
  props: ['tableHeight', 'queryForm'],
  components: {},
  data() {
    return {
      tableDate: [],
      loading: false,
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    getTable(){
      this.loading = true;
      getDrugStatementTable(this.queryForm).then(res => {
        if (res.code === 200){
          this.tableDate = res.data;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../../assets/styles/singlePage";
</style>
