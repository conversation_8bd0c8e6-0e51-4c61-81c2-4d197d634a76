<template>
  <div>
    <el-dialog
      :visible.sync="imageRecordStatus"
      :title="title"
      width="85%"
    >
      <div class="tabs-home">
        <el-tabs v-model="activeName" type="card" @tab-click="tabsClick" v-for="(item,index) in activeItem" :key="index"
                 :class="{tabsButtonOne: activeName === item.value}">
          <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="record-master">
        <div v-if="activeName === 'outp'">
          <outp-record :key="activeKey" :queue-form="queueForm"></outp-record>
        </div>
        <div v-else-if="activeName === 'in'">
          <in-record :key="activeKey" :queue-form="queueForm"></in-record>
        </div>
        <div v-else-if="activeName === 'statistics'">
          <statistics-record :key="activeKey" :queue-form="queueForm"></statistics-record>
        </div>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import OutpRecord from './iamgeRecordItem/outpRecord.vue'
import InRecord from './iamgeRecordItem/inRecord.vue'
import StatisticsRecord from './iamgeRecordItem/statisticsRecord.vue'

export default {
  name: 'imageBillingRecord',
  props: [],
  components: { StatisticsRecord, InRecord, OutpRecord },
  data() {
    return {
      activeName: 'outp',
      activeItem: [{
        label: '门诊',
        value: 'outp'
      },
        {
          label: '住院',
          value: 'in'
        }, {
          label: '统计',
          value: 'statistics'
        }],
      imageRecordStatus: false,
      activeKey: 0,
      title: '患者开单记录查询',
      patientId: '',
      queueForm:{
        patientId: '',
        beginDate: '',
        endDate: '',
      },
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    billingRecordVerify(status) {
      let queueForm = this.$store.getters.patient;
      this.queueForm = {
        beginDate: queueForm.beginDate,
        endDate: queueForm.endDate,
      }
      if (queueForm.patientId){
        this.queueForm.patientId = queueForm.patientId;
      }else{
        this.queueForm.patientId = this.$store.getters.rowTable.patientId;
      }
      this.imageRecordStatus = status
    },
    tabsClick(){
      ++this.activeKey;
    },
  }
}
</script>

<style scoped lang="scss">
.tabs-home {
  display: flex;
  margin-left: 5px;
  ::v-deep.el-tabs__header {
    padding: 0;
    margin: 0;
  }

  ::v-deep.el-tabs--card > .el-tabs__header .el-tabs__nav {
    border-radius: 0;
  }

  ::v-deep.el-tabs__item {
    padding: 0 20px;
    font-size: 16px;
    height: 35px;
    color: #303133;
    line-height: 35px;
  }

  .tabsButtonOne {
    ::v-deep.el-tabs__nav {
      background-color: #FFFFFF !important;
      color: black !important;
    }

    ::v-deep.el-tabs__item {
      color: black !important;
    }
  }

  ::v-deep.el-tabs__nav {
    :hover {
      background-color: #FFFFFF;
      color: black;
      transform: scale(1.1);
    }
  }
}
.record-master{
  min-height: 400px;
}
</style>
<style scoped>
/deep/.el-dialog:not(.is-fullscreen) {
  margin-top: 0.5vh !important;
}
/deep/.el-dialog__body {
  padding: 5px 10px;
}
</style>
