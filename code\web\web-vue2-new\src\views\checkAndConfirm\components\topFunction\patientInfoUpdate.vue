<template>
  <div>
    <el-dialog
      :visible.sync="updateStatus"
      title="患者信息调整"
      width="85%"
    >
      <el-descriptions class="margin-top" title="带边框列表" :column="2" border>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-user"></i>
            患者ID
          </template>
          {{patientData.patientId}}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-user"></i>
            用户名
          </template>
          {{patientData.name}}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <i class="el-icon-mobile-phone"></i>
            手机号
          </template>
          <el-input style="width: 180px;" v-model="patientData.phone" placeholder="请输入内容"></el-input>
        </el-descriptions-item>
      </el-descriptions>
      <div style="display: flex;justify-content: center;align-items: center">
        <el-button type="primary" @click="checkPhone">修改/确定</el-button>
        <el-button type="danger" @click="updateStatus = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {GetPatientUpdateInfoData,UpdatePatientInfo} from '@/api/checkAndConfirm/patientInfoUpdate'
export default {
  name: 'patientInfoUpdate',
  props: [],
  components: {},
  data() {
    return {
      updateStatus: false,
      patientData:{},
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    checkPhone() {
      if (!this.patientData.phone) {
        this.errorMsg = '请输入手机号';
        return;
      }
      const loading = this.$loading({
        lock: true,
        text: '正在校验数据中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      UpdatePatientInfo(this.patientData).then(res => {
        if (res.code === 200){
          this.$message.success("修改成功")
          this.updateStatus = false;
        }
      }).finally(() => {
        loading.close();
      })
    },
    init(data){
      if (data){
        let table = this.$store.getters.rowTable;
        const loading = this.$loading({
          lock: true,
          text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
          spinner: 'el-icon-coffee-cup',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        this.patientData = {};
        GetPatientUpdateInfoData(table.examNo).then(res => {
          if (res.code === 200){
            this.patientData = res.data;
            console.log(this.patientData);
            this.updateStatus = true;
          }
        }).finally(() => {
          loading.close();
        })
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>
