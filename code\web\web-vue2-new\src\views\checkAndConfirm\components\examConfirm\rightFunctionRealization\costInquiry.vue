<template>
  <div class="cost-inquiry-home">
    <el-dialog
      title="检查项目明细"
      :visible.sync="costInquiryStatus"
      width="80%"
    >
      <div class="dialog-master">
        <!--        项目详情-->
        <div class="item-table-master">
          <el-table :data="costInquiryData" style="width: 100%" border max-height="350px" show-summary>
            <el-table-column type="index" align="center" width="60"></el-table-column>
            <el-table-column align="center" label="类别">
              <template slot-scope="scope">
                <el-select v-model="scope.row.itemclass">
                  <el-option
                    v-for="item in examClassItems"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="itemname" align="center" label="项目名称"></el-table-column>
            <el-table-column prop="itemspec" align="center" label="项目规格" width="80" :show-overflow-tooltip="true"
            ></el-table-column>
            <el-table-column prop="amount" align="center" label="数量" width="80">
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.amountInputStatus"
                  v-model="scope.row.amount"
                  placeholder="请输入地址"
                  ref="amountRefs"
                  @blur="scope.row.amountInputStatus=false"
                >
                </el-input>
                <span v-else>{{ scope.row.amount }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="units" align="center" label="单位" width="50"></el-table-column>
            <el-table-column prop="charges" align="center" label="单价" width="80"></el-table-column>
            <el-table-column prop="charges" align="center" label="应收费用" width="80"></el-table-column>
            <el-table-column prop="costs" align="center" label="实收费用" width="80">
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.costsInputStatus"
                  v-model="scope.row.costs"
                  placeholder="请输入地址"
                  ref="costsRefs"
                  @blur="scope.row.costsInputStatus=false"
                >
                </el-input>
                <span v-else>{{ scope.row.costs }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="bottom-button-master">
          <div class="button-item">
            <el-button type="info" @click="cancel">关闭</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetCostInquiry } from '@/api/checkAndConfirm/checkConfirm';
export default {
  name: 'costInquiry',
  props: [],
  components: {},
  data() {
    return {
      costInquiryStatus: false,
      costInquiryData:[],
      examClassItems: [{
        value: 'D',
        label: '检查'
      }, {
        value: 'A',
        label: '西药'
      }, {
        value: 'E',
        label: '治疗'
      }, {
        value: 'I',
        label: '材料'
      }, {
        value: 'Z',
        label: '其他'
      }],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    cancel(){
      this.costInquiryStatus = false;
      this.costInquiryData = [];
    },
    openDialog(status){
      if (status){
        let data = this.$store.getters.rowTable;
        if (data){
          GetCostInquiry(data.examNo).then(res => {
            this.costInquiryData = res.data;
            this.costInquiryStatus = status;
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.cost-inquiry-home{

  .dialog-master{

    ::v-deep.el-table--medium .el-table__cell {
      padding: 1px 0;
    }

    ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
      word-break: break-word;
      background-color: #f8f8f9;
      color: #515a6e;
      height: 30px;
      font-size: 13px;
    }

    ::v-deep.el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
      background-color: #1890FF;
      color: #FFFFFF;
    }

    .bottom-button-master{
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }

}
</style>
