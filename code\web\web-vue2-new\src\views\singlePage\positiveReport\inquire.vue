<template>
  <div class="single-master">
    <div class="single-title">阳性上报</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="开始时间:">
              <el-date-picker
                v-model="queueForm.beginDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker
                v-model="queueForm.endDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <div class="element-button">
                <el-button type="primary" icon="el-icon-search" @click="getPositiveReport">查询</el-button>
                <el-button type="primary" icon="el-icon-download" @click="exportData">导出</el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="element-table">
          <el-table :data="tableDate" :span-method="objectSpanMethod" style="width: 100%" border :height="tableHeight - 150">
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column prop="RECEIVEDATE" align="center" label="报告日期" width="120"></el-table-column>
            <el-table-column prop="PATID" align="center" label="病人ID" width="80">
              <template slot-scope="scope">
                <div class="text-click" @click="openResult(scope.row)">{{scope.row.PATID}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="PATNAME" align="center" label="姓名" width="80"></el-table-column>
            <el-table-column prop="AGE" align="center" label="年龄" width="60"></el-table-column>
            <el-table-column prop="PNAME" align="center" label="项目组合" width="250"></el-table-column>
            <el-table-column prop="CNAME" align="center" label="项目名称" width="250"></el-table-column>
            <el-table-column prop="REPORTDESC" align="center" label="结果" width="120"></el-table-column>
            <el-table-column prop="DOCTOR" align="center" label="开单医生" width="80"></el-table-column>
            <el-table-column prop="DEPTNAME" align="center" label="开单科室" width="120"></el-table-column>
          </el-table>
        </div>
        <el-dialog
          :visible.sync="resultStatus"
          :title="title"
          width="85%"
        >
          <report-result :row-data="resultRowData" :max-height="tableHeight" :key="resultKey"></report-result>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import {getPositiveReportList,exportPositiveReport} from "@/api/singlePage/positiveReport"
import {excelDownloadXLSX} from "@/utils/BlobUtils"
import ReportResult from './module/reportResult.vue'
export default {
  name: 'inquires',
  props: [],
  components: { ReportResult },
  data() {
    return {
      tableDate: [],
      queueForm: {
        beginDate: this.formatDate(new Date()),
        endDate: this.formatDate(new Date()),
        patientId: '',
        status: ''
      },
      tableHeight: undefined,
      mergeArr: ["RECEIVEDATE","PATID","PATNAME","AGE","PNAME","DOCTOR","DEPTNAME"],
      mergeObj: {},
      title: '患者阳性报告结果',
      resultStatus: false,
      resultKey: 0,
      resultRowData:{},
    }
  },
  created() {
    this.handleResize();
    this.getPositiveReport();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize); // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize); // 移除监听器
  },
  methods: {
    openResult(row){
      this.resultRowData = row;
      ++this.resultKey;
      this.resultStatus = true;
    },
    getSpanArr(data) {
      // 循环需要合并的单元格数据
      this.mergeArr.forEach((key, index1) => {
        let count = 0; // 用来记录需要合并行的起始位置
        this.mergeObj[key] = []; // 记录每一列的合并信息
        data.forEach((item, index) => {
          // index == 0表示数据为第一行，直接 push 一个 1
          if (index === 0) {
            this.mergeObj[key].push(1);
          } else {
            // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并 并push 一个 0 作为占位
            if (item[key] === data[index - 1][key]) {
              this.mergeObj[key][count] += 1;
              this.mergeObj[key].push(0);
            } else {
              // 如果当前行和上一行其值不相等
              count = index; // 记录当前位置
              this.mergeObj[key].push(1); // 重新push 一个 1
            }
          }
        });
      });
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 判断列的属性
      if (this.mergeArr.indexOf(column.property) !== -1) {
        // 判断其值是不是为0
        if (this.mergeObj[column.property][rowIndex]) {
          return [this.mergeObj[column.property][rowIndex], 1];
        } else {
          // 如果为0则为需要合并的行
          return [0, 0];
        }
      }
    },
    getPositiveReport(){
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      getPositiveReportList(this.queueForm).then(res => {
        if (res.code === 200){
          this.tableDate = res.data;
          this.getSpanArr(res.data);
        }
      }).finally(() => {
        loading.close();
      })
    },
    exportData(){
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力导出中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      exportPositiveReport(this.queueForm).then(res => {
        let fileName = "阳性上报统计";
        excelDownloadXLSX(res,fileName)
      }).finally(() => {
        loading.close();
      })
    },
    handleResize() {
      this.tableHeight = window.innerHeight; // 更新高度数据
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
.text-click{
  color: #00afff;
}

:hover.text-click{
  cursor: pointer;
  border-bottom: 1px solid #00afff;
}
</style>
