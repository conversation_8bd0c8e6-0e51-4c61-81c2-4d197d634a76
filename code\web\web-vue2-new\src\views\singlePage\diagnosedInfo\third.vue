<template>
  <div class="single-master">
  
    <div class="element-master">
  
      <div class="my-table">
        <el-table :data="thirdList" style="width: 100%" border height="200px" highlight-current-row
          @row-click="handleTableClick">
          <el-table-column prop="tesT_NO" align="center" label="检验号">
          </el-table-column>
          <el-table-column prop="iteM_NAME" align="center" label="检验名称" />
          <el-table-column prop="specimen" align="center" label="标本" width="60px" :show-overflow-tooltip="true" />
          <el-table-column prop="requesteD_DATE_TIME" align="center" label="申请时间" :show-overflow-tooltip="true" />
          <el-table-column prop="orderinG_PROVIDER" align="center" label="申请者" width="60px">
            <template slot-scope="scope">
              {{ scope.row.provider==null? scope.row.orderinG_PROVIDER :scope.row.provider }}
            </template>
          </el-table-column>
          <el-table-column prop="tesT_CAUSE" align="center" label="申请科室" :show-overflow-tooltip="true" width="60px">
            <template slot-scope="scope">
              {{ scope.row.dept==null? scope.row.tesT_CAUSE :scope.row.dept }}
            </template>
          </el-table-column>
          <el-table-column prop="verifieD_BY" align="center" label="审核者" />
          <el-table-column prop="resultS_RPT_DATE_TIME" align="center" label="报告时间" :show-overflow-tooltip="true">
          </el-table-column>
        </el-table>
        <el-divider></el-divider>
        <el-table :data="tableDateResult" style="width: 100%" border height="200px" highlight-current-row
          :cell-style="rowClassName">
          <el-table-column prop="resultS_RPT_DATE_TIME" align="center" label="报告时间" :show-overflow-tooltip="true" />
          <el-table-column prop="reporT_ITEM_NAME" align="center" label="报告项目名称">
          </el-table-column>
          <el-table-column prop="result" align="center" label="结果" :show-overflow-tooltip="true" width="100px" />
          <el-table-column prop="abnormaL_INDICATOR" label="正常标志" align="center" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span
                :style="{color:scope.row.abnormaL_INDICATOR === 'H'? 'red': scope.row.abnormaL_INDICATOR === 'L'? 'red': scope.row.abnormaL_INDICATOR === 'LL'? 'red': scope.row.abnormaL_INDICATOR === 'HH'? 'red': scope.row.abnormaL_INDICATOR === '阳'? 'red': '',}">
                {{ scope.row.abnormaL_INDICATOR }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="units" align="center" label="单位" width="60px" :show-overflow-tooltip="true" />
          <el-table-column prop="prinT_CONTEXT" align="center" label="参考值" :show-overflow-tooltip="true" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>

import {
  GetAssayResultList
} from "@/api/singlePage/diagnosedInfo";
export default {
  name: 'parasiteIndex',
  props:
  {
    thirdList: Array,
  },

  data() {
    return {
      currentTabIndex: '0',
      activeName: 'first',
      queueForm: {
        pageNum: 1,
        pageSize: 10,
        beginDate: this.formatDate(new Date()),
        endDate: this.formatDate(new Date()),
        // beginDate: '2024-01-01',
        // endDate: '2024-01-30'
      },
      baseInfo: [],
      tableDate: [],
      total: 0,
      tableDateTestNo: [],
      tableDateDocName: [],
      titleTestNo: "检验详情信息",
      titleDocName: "医生详情信息",
      testNoDialog: false,
      docNameDialog: false,
      tableDateResult: [],
    }
  },

  created() {
    //this.getList();
    this.handleResize();
  },

  mounted() {
    window.addEventListener('resize', this.handleResize); // 添加监听器
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize); // 移除监听器
  },

  methods: {
    handleTableClick(row) {
      let param = {
        testNo: row.tesT_NO
      }
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力导出中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      GetAssayResultList(param).then(res => {
        this.tableDateResult = res.data.baseDown;
      }).finally(() => {
        loading.close();
      });
    },
    rowClassName(row) {



      if (row.row.result != null) {


        if (row.row.result.includes('阳')) {
          return 'color: red;'

        }
      }

    },







    // 默认当前时间
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 自定义高度变化更新高度
    handleResize() {
      this.tableHeight = window.innerHeight; // 更新高度数据
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.text-click {
  color: #00afff;
}

:hover.text-click {
  cursor: pointer;
  border-bottom: 1px solid #00afff;
}

.my-table {
  ::v-deep.el-table--medium .el-table__cell {
    padding: 0;
  }

  ::v-deep.el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    word-break: break-word;
    background-color: #f8f8f9;
    color: #515a6e;
    height: 30px;
    font-size: 14px;
  }

  ::v-deep.el-table th.el-table__cell>.cell {
    padding: 0;
  }

  ::v-deep.el-table--border .el-table__cell:first-child .cell {
    padding: 0;
  }

  ::v-deep.el-button+.el-button {
    margin-left: 2px;
  }

  ::v-deep.el-table .cell {
    padding: 1px;
  }

  /* ---el-table滚动条公共样式--- */
  // 滚动条的宽度
  ::v-deep.el-table__body-wrapper::-webkit-scrollbar {
    width: 10px; // 横向滚动条
    height: 10px; // 纵向滚动条 必写
  }

  // 滚动条的滑块
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }
}
</style>