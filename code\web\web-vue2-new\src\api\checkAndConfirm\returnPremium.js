import request from "@/utils/request";


export function ReturnPremiumParameterInit(data) {
  return request({
    url: "/ReturnPremium/ReturnPremiumParameterInit",
    method: "get",
    params: data,
  });
}


export function InHospitalReturnPremium(data) {
  return request({
    url: "/ReturnPremium/InHospitalReturnPremium",
    method: "post",
    data: data,
  });
}

export function ReturnPremiumOutpTableClickData(examNo,examItemNo) {
  return request({
    url: "/ReturnPremium/ReturnPremiumOutpTableClickData?examNo=" + examNo + "&examItemNo=" + examItemNo,
    method: "get",
  });
}

export function OutpHospitalReturnPremium(data) {
  return request({
    url: "/ReturnPremium/OutpHospitalReturnPremium",
    method: "post",
    data: data,
  });
}

export function CancelCallQueueMessage(examNo,patientId) {
  return request({
    url: "/ReturnPremium/CancelCallQueueMessage?examNo=" + examNo + "&patientId=" + patientId,
    method: "get",
  });
}
