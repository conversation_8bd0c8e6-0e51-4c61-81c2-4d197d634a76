import request from "@/utils/request";

export function GetReservationCenterInfo(patientId,empNO,deptCode) {
  return request({
    url: "/CheckAppointment/GetReservationCenterInfo?patientId=" + patientId + '&empNo=' +empNO + '&deptCode=' + deptCode,
    method: "get",
  });
}

export function GetReservationCenterInfoTwo(data) {
  return request({
    url: "/CheckAppointment/GetReservationCenterInfoTwo",
    method: "post",
    data: data,
  });
}

export function GetUnitOrWindowTree(data) {
  return request({
    url: "/CheckAppointment/GetUnitOrWindowTree",
    method: "post",
    data: data,
  });
}

export function GetUnitAndWindowTree(data) {
  return request({
    url: "/CheckAppointment/GetUnitAndWindowTree",
    method: "post",
    data:data,
  });
}

export function GetWindowTimeMessage(data) {
  return request({
    url: "/CheckAppointment/GetWindowTimeMessage",
    method: "post",
    data:data,
  });
}

export function GetCallQueueList(data) {
  return request({
    url: "/CheckAppointment/GetCallQueueList",
    method: "post",
    data:data,
  });
}

export function GetCallQueueMessage(data) {
  return request({
    url: "/CheckAppointment/GetCallQueueMessage",
    method: "post",
    data:data,
  });
}

export function DeleteCallQueueByQueueId(data) {
  return request({
    url: "/CheckAppointment/DeleteCallQueueByQueueId",
    method: "put",
    data:data,
  });
}

export function CallTransfer(data) {
  return request({
    url: "/CheckAppointment/CallTransfer",
    method: "put",
    data:data,
  });
}

export function RecoverCallQueue(data) {
  return request({
    url: "/CheckAppointment/RecoverCallQueue",
    method: "put",
    data:data,
  });
}

export function UpdateMark(data) {
  return request({
    url: "/CheckAppointment/UpdateMark",
    method: "put",
    data:data,
  });
}

export function GetAppointmentTimeList(data) {
  return request({
    url: "/CheckAppointment/GetAppointmentTimeList",
    method: "post",
    data: data,
  });
}

export function TimeAppointment(data) {
  return request({
    url: "/CheckAppointment/TimeAppointment",
    method: "post",
    data: data,
  });
}

export function GetTimeDispose(data) {
  return request({
    url: "/CheckAppointment/GetTimeDispose?date=" + data,
    method: "get",
  });
}

export function GetAppointmentListFormUnitTree(data) {
  return request({
    url: "/CheckAppointment/GetAppointmentListFormUnitTree?deptCode=" + data,
    method: "get",
  });
}
export function GetAppointmentListFormWindowTree(data) {
  return request({
    url: "/CheckAppointment/GetAppointmentListFormWindowTree?unitId=" + data,
    method: "get",
  });
}

export function GetAppointmentList(data) {
  return request({
    url: "/CheckAppointment/GetAppointmentList",
    method: "post",
    data: data,
  });
}

//叫号登记相关
export function GetManualInputFormUnitTree(data) {
  return request({
    url: "/CheckAppointment/GetManualInputFormUnitTree?deptCode=" + data,
    method: "get",
  });
}
export function GetManualInputFormFormWindowTree(data) {
  return request({
    url: "/CheckAppointment/GetManualInputFormFormWindowTree?unitId=" + data,
    method: "get",
  });
}
export function GetManualInputFormPatient(data) {
  return request({
    url: "/CheckAppointment/GetManualInputFormPatient?patientId=" + data,
    method: "get",
  });
}
export function SaveManualInputForm(data) {
  return request({
    url: "/CheckAppointment/SaveManualInputForm",
    method: "post",
    data: data,
  });
}
//住院通知相关
export function GetBeHospitalizedInformMessage(data) {
  return request({
    url: "/CheckAppointment/GetBeHospitalizedInformMessage",
    method: "post",
    data:data,
  });
}
export function GetBeHospitalizedInformTemplate(data) {
  return request({
    url: "/CheckAppointment/GetBeHospitalizedInformTemplate",
    method: "get",
  });
}
export function SendBeHospitalizedInform(data) {
  return request({
    url: "/CheckAppointment/SendBeHospitalizedInform",
    method: "post",
    data:data,
  });
}

/**
 * 报道接口实现
 * @param examNo 检查号
 * @param empNo 操作者
 * @returns {*}
 * @constructor
 */
export function PatientReport(examNo,empNo) {
  return request({
    url: "/CheckAppointment/PatientReport?examNo=" + examNo + "&empNo=" + empNo,
    method: "get",
  });
}
