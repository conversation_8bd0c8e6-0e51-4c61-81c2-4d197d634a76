<template>
  <div>
    <el-dialog title="打印预览" top="2vh" :visible.sync="dialogVisible" width="85%" :show-close="true"
               :close-on-press-escape="true" :close-on-click-modal="true"
    >
      <div>
        <div>
          <el-form :inline="true" :model="totalModel" class="demo-form-inline">
            <el-form-item label="打印每页行数">
              <el-input-number v-model="printDatas.totalSize" :min="1" :max="50"></el-input-number>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="totalSizeClick">确定</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-scrollbar style="width: 100%; overflow-x: hidden" :style="tableHeight">
          <div id="print-box" ref="printArea">
            <table v-for="(i, j) in printDatas.printDataList" :key="j" class="my-table"
                   :style="'font-size:' + 1.3 + 'vw'+(j==printDatas.printDataList.length-1 ? 'page-break-after: always;':'')"
            >
              <thead>
              <tr>
                <td :colspan="printDatas.tableTitle.length + 1" style="border: none;">
                  <div v-html="printDatas.titleHtml"></div>
                </td>
              </tr>
              <tr>
                <td :colspan="printDatas.tableTitle.length + 1" style="border: none;">
                  <div style="display: flex;align-items: center;">
                    <div style="text-align: center; font-size: 1.3vw;width: 89%;">
                      <div v-html="printDatas.pageHtml"></div>
                    </div>
                    <div style="font-size: 1.3vw;">第{{ j + 1 }}页(共{{ printDatas.totalPage }}页)</div>
                  </div>
                  <div
                    style="text-align: start;display: flex; flex-wrap: wrap;justify-content: space-between;  "
                  >
                  </div>
                </td>
              </tr>
              <tr>
                <th style="width: 2vw;"></th>
                <th v-for="(item,index) in printDatas.tableTitle" :key="item.key">{{ item.title }}</th>
              </tr>
              </thead>
              <tbody :style="'font-size: 0.8vw;'">
              <tr v-for="(item, index) in i || []" :key="index">
                <td>{{ index + 1 }}</td>
                <td v-for="child in printDatas.tableTitle" :key="child.key"
                    :style="'width:' + (child.width ? child.width : '10vw;')"
                >
                  <span :style="'font-weight:' + (child.fontWeight ? child.fontWeight : '')">{{
                      item[child.key]
                    }}</span>
                </td>
              </tr>

              </tbody>

              <tfoot>
              <tr>
                <td :colspan="4" style="font-weight: 600;font-size: 1.2vw;border: none;text-align: justify"
                    v-for="(item,index) in pageMergeData" :key="index"
                >
                  <span v-if="item[j]">{{ item[j] }}</span>
                </td>
              </tr>
              <tr>
                <td :colspan="4" style="font-weight: 600;font-size: 1.2vw;border: none;text-align: justify"><span>{{
                    sumMergeData[0]
                  }}</span></td>
                <td :colspan="4" style="font-weight: 600;font-size: 1.2vw;border: none;text-align: justify"><span>{{
                    sumMergeData[1]
                  }}</span></td>
                <td :colspan="4" style="font-weight: 600;font-size: 1.2vw;border: none;text-align: justify"><span>{{
                    sumMergeData[2]
                  }}</span></td>
              </tr>
              <tr v-if="printDatas.bottomHtml">
                <td :colspan="printDatas.tableTitle.length + 1" v-html="printDatas.bottomHtml"
                    style="border: none;font-size: 1vw;"
                ></td>
              </tr>
              </tfoot>
            </table>

          </div>
        </el-scrollbar>
        <div class="event-button">
          <el-button type="primary" circle v-print="printModel">打印</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDrugStatementTable } from '@/api/singlePage/drugStatement'

/**
 * 按供货商查询
 */
export default {
  name: 'supplierPrint',
  props: [],
  components: { },
  data() {
    return {
      printDatas: {
        printDataList: [],//打印数据
        tableTitle: [{
          key: 'DRUG_CODE',
          title: '编码',
          width: '7vm'
        }, {
          key: 'DRUG_NAME',
          title: '品名',
          width: 'width: 5%'
        }, {
          key: 'specUnit',
          title: '规格',
          width: 'width: 5%'
        }, {
          key: 'UNITS',
          title: '单位',
          width: 'width: 5%'
        }, {
          key: 'QUANTITY',
          title: '数量',
          width: 'width: 5%'
        }, {
          key: 'PRICE',
          title: '进货价',
          width: 'width: 5%'
        }, {
          key: 'JJJE',
          title: '进价金额',
          width: 'width: 5%'
        }, {
          key: 'PURCHASE_PRICE',
          title: '零价',
          width: 'width: 5%'
        }, {
          key: 'CJ',
          title: '差价',
          width: 'width: 5%'
        }, {
          key: 'FIRM_ID',
          title: '厂家',
          width: 'width: 5%'
        }, {
          key: 'BATCH_NO',
          title: '批号',
          width: 'width: 5%'
        }, {
          key: 'INVENTORY',
          title: '结存',
          width: 'width: 5%'
        }],//标题数据
        pageHtml: '',//是否page同行
        totalPage: 0,//总行数
        totalSize: 12,//打印页每页显示条数
        mergeData: [],//合并数据
        isMerge: false,//是否合并
        titleHtml: '',//每页title文字（需要带样式）
        bottomHtml: '',//每页底部文字（需要带样式）
        mergeSize: 2,
        isSpecialMerge: true,
        specialMergeData: {
          mergeColumn: 2,
          mergeLine: 3,
          mergeSize: 4,
          mergeStyle: 'border: none;',
          data: []
        }
      },
      totalModel: {},
      pageMergeData: [],
      sumMergeData: [],
      printModel: {
        id: 'print-box',
        popTitle: '打印', // 打印配置页上方标题
        extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
        preview: false, // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
        previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
        previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
        zIndex: 20002, // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
        previewBeforeOpenCallback() {
        }, //预览窗口打开之前的callback（开启预览模式调用）
        previewOpenCallback() {
        }, // 预览窗口打开之后的callback（开启预览模式调用）
        beforeEntryIframe() {
        },
        openCallback() {
        }, // 调用打印之后的回调事件
        closeCallback() {
        }, //关闭打印的回调事件（无法确定点击的是确认还是取消）
        url: '',
        standard: '',
        extraCss: ''
      },
      jj: [],
      lj: [],
      sum: [],
      printKey: 0,
      dialogVisible: false,
      tableHeight: 0
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    handleResize() {
      this.tableHeight = 'height:' + (window.innerHeight - 250) + 'px' // 更新高度数据
    },
    totalSizeClick(data) {
      ++this.printKey
      this.printDatas.totalSize = data
      this.chengeRows(data)
    },
    printData(data) {
      getDrugStatementTable(data).then(res => {
        this.storeDispose(data)
        if (res.code === 200) {
          this.tableData = res.data
          let jj = 0
          let lj = 0
          this.tableData.forEach(t => {
            t.JJJE = parseFloat((t.PRICE * t.QUANTITY).toFixed(2))
            t.CJ = parseFloat(((t.PURCHASE_PRICE - t.QUANTITY) - t.JJJE).toFixed(2))
            t.specUnit = t.PACKAGE_SPEC + t.UNITS
            jj += t.QUANTITY * t.PRICE
            lj += t.QUANTITY * t.PURCHASE_PRICE
          })
          this.sumMergeData = [
            '进价合计：' + this.formatInternationalPrice(jj),
            '零价合计：' + this.formatInternationalPrice(lj),
            '进差价：' + this.formatInternationalPrice(lj - jj)]
          this.chengeRows(this.printDatas.totalSize)
          this.dialogVisible = true
        }
      })
    },
    chengeRows(val) {
      let tableData = this.sliceArr(this.tableData, val) //按照行数将表格数据切割为二维数组
      this.printDatas.printDataList = tableData
      this.printDatas.totalPage = tableData.length //这里拿到的就是总页面数
      tableData.forEach((item, index) => {
        let jj = 0
        let lj = 0
        item.forEach((i, j) => {
          jj += i.QUANTITY * i.PRICE //这个就是要计算的列的参数名合计，并且这里是按照每页计算的
          lj += i.QUANTITY * i.PURCHASE_PRICE
        })
        this.$set(this.jj, index, '进价合计：' + this.formatInternationalPrice(jj))
        this.$set(this.lj, index, '零价合计：' + this.formatInternationalPrice(lj))
        this.$set(this.sum, index, '进差价：' + this.formatInternationalPrice(lj - jj))
        this.$set(this.printDatas.printDataList, index, item)
      })
      this.pageMergeData = [this.jj, this.lj, this.sum]
    },
    sliceArr(array, size) {
      if (array.length === 0) {
        return []
      }
      const numOfChunks = Math.ceil(array.length / size)
      const chunks = new Array(numOfChunks)
      for (let i = 0, j = 0; i < numOfChunks; i++) {
        chunks[i] = array.slice(j, j + size)
        j += size
      }
      return chunks
    },
    textDisplay(beginDate, endDate, store) {
      this.printDatas.titleHtml =
        '<div style="font-size: 22px;text-align: center">河南宏力医院药品入库凭证</div>' +
        '<div style="display: flex;justify-content: space-between;">' +
        '<div style="display: flex;align-items: center;"><div>单位：</div><div style="padding: 5px;">' + store + '</div></div>' +
        '<div style="display: flex;align-items: center;height: 30px;">' +
        '<div>统计日期：</div><div>' + beginDate + '</div><div style="padding: 0 5px;">-</div><div>' + endDate + '</div>' +
        '</div></div>'
      this.printDatas.bottomHtml =
        '<div style="border-top: 1px solid black;"></div><div style=\'display: flex;justify-content: flex-end;margin-right: 12%;padding-top: 5px;\'>' +
        '<div style=\'width: 500px;\'>会计：</div>' +
        '<div>制表：</div><div style=\'width: 80px;display: flex;\'>' + this.$store.getters.name + '</div>' +
        '</div>'
    },
    storeDispose(data) {
      let beginDate = data.beginDate + ' 00:00:00'
      let endDate = data.endDate + ' 23:59:59'
      let store = data.manufacturers
      this.textDisplay(beginDate, endDate, store)
    },
    formatInternationalPrice(value) {
      value = value.toFixed(2)
      // 先将数字转换为字符串
      let strValue = value.toString()

      // 检查是否有小数点
      let [integerPart, decimalPart] = strValue.includes('.') ? strValue.split('.') : [strValue, '']
      // 将整数部分插入千位分隔符
      let formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      // 重新组合整数部分和小数部分
      let formattedValue = decimalPart ? `${formattedIntegerPart}.${decimalPart}` : formattedIntegerPart
      return formattedValue
    }
  }
}
</script>

<style scoped lang="scss">
@page {
  size: auto; /* auto is the initial value */
  margin: 3mm; /* this affects the margin in the printer settings */
}

#print-box {
  margin: 10px 10px 10px 10px;
  font-size: 1.6vw !important;
}

.my-table {
  margin-top: 20px;
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

.my-table th,
.my-table td {
  padding: 6px;
  text-align: center;
}

.my-table th {
  border: 1px solid #000;
  font-weight: bold;
}

.my-table td {
  border: 1px solid #000;
}

.ml-2 {
  margin-left: 8px;
}

.mt-2 {
  margin-top: 8px;
}

.event-button {
  position: fixed;
  /* 使用固定定位 */
  bottom: 600px;
  /* 距离底部20像素 */
  right: 200px;
  width: 80px;
  height: 80px;
  font-size: 40px !important;
  border-radius: 50% !important;

  ::v-deep.el-button--medium.is-circle {
    padding: 40px;
  }

  ::v-deep.el-button--medium {
    font-size: 30px;
  }
}
</style>
