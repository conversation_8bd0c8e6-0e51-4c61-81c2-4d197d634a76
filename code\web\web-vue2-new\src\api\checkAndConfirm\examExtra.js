import request from "@/utils/request";

export function ExamChargeThirdPayment(data) {
  return request({
    url: "/ExamExtraCollectFee/ExamChargeThirdPayment",
    method: "post",
    data: data,
  });
}

export function EstimatePatientInHospitalStatus(patientId,visitId) {
  return request({
    url: "/ExamExtraCollectFee/EstimatePatientInHospitalStatus?patientId=" + patientId + '&visitId=' + visitId,
    method: "get",
  });
}
