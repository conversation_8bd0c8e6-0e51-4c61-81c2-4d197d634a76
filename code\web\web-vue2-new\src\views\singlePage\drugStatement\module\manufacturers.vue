<template>
  <div class="statement-home" :style="'height:' + (tableHeight) + 'px'">
    <div class="statement-text">河南宏力医院药品入库凭证</div>
    <div class="statement-data">
      <div class="statement-item" style="display: flex;align-items: center;padding-left: 5px;">
        <div>货源：</div>
        <div style="padding: 5px;">{{ queryForm.manufacturers }}</div>
      </div>
      <div class="statement-item" style="height: 30px;display: flex;align-items: center;padding-right: 50px;">
        <div>统计日期：</div>
        <div>{{ beginDate }}</div>
        <div style="padding: 0 5px;">-</div>
        <div>{{ endDate }}</div>
      </div>
    </div>
    <div class="statement-table">
      <div class="table-title">
        <div class="statement-master">
          <div class="statement-item" :style="item.width" v-for="(item,index) in tableTitle" :key="index">
            {{ item.title }}
          </div>
        </div>
      </div>
      <div class="statement-list-master" v-loading="loading">
        <el-scrollbar style="width: 100%; overflow-x: hidden" :style="'height:' + (tableHeight - 190) + 'px'">
          <div class="statement-line" v-for="(item,index) in tableDate" :key="index">
            <div class="statement-column" :style="tableTitle[0].width">
              {{ item.DRUG_CODE }}
            </div>
            <div class="statement-column" :style="tableTitle[1].width">
              {{ item.DRUG_NAME }}
            </div>
            <div class="statement-column" :style="tableTitle[2].width">
              {{ item.PACKAGE_SPEC + item.UNITS }}
            </div>
            <div class="statement-column" :style="tableTitle[3].width">
              {{ item.UNITS }}
            </div>
            <div class="statement-column" :style="tableTitle[4].width">
              {{ item.QUANTITY }}
            </div>
            <div class="statement-column" :style="tableTitle[5].width">
              {{ item.PRICE }}
            </div>
            <div class="statement-column" :style="tableTitle[6].width">
              {{ (item.PRICE * item.QUANTITY).toFixed(2) }}
            </div>
            <div class="statement-column" :style="tableTitle[7].width">
              {{ item.PURCHASE_PRICE }}
            </div>
            <div class="statement-column" :style="tableTitle[8].width">
              {{ ((item.PURCHASE_PRICE * item.QUANTITY) - (item.PRICE * item.QUANTITY)).toFixed(2) }}
            </div>
            <div class="statement-column" :style="tableTitle[9].width">
              {{ item.BATCH_NO }}
            </div>
            <div class="statement-column" :style="tableTitle[10].width">
              {{ item.INVENTORY }}
            </div>
            <div class="statement-column" :style="tableTitle[11].width">
              {{ item.SUPPLIER }}
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="statement-total">
        <div class="total-item">
          <div>进价小计：</div>
          <div class="total-text">{{ collectData.one }}</div>
        </div>
        <div class="total-item">
          <div>零价小计：</div>
          <div class="total-text">{{ collectData.tow }}</div>
        </div>
        <div class="total-item">
          <div>进零差：</div>
          <div class="total-text">{{ collectData.three }}</div>
        </div>
        <div class="total-item">
          <div>进价合计：</div>
          <div class="total-text">{{ collectData.four }}</div>
        </div>
        <div class="total-item">
          <div>零价合计：</div>
          <div class="total-text">{{ collectData.five }}</div>
        </div>
        <div class="total-item">
          <div>进零差：</div>
          <div class="total-text">{{ collectData.six }}</div>
        </div>
      </div>
      <div style="display: flex;justify-content: flex-end;margin-right: 12%;padding-top: 5px;">
        <div style="display: flex">
          <div>会计：</div>
          <div style="width: 120px;"></div>
        </div>
        <div style="display: flex">
          <div>制表：</div>
          <div>{{ this.$store.getters.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDrugStatementTable } from '@/api/singlePage/drugStatement'

/**
 * 按厂家查询
 */
export default {
  name: 'manufacturers',
  props: ['tableHeight','queryForm'],
  components: {},
  data() {
    return {
      tableDate: [],
      collectData: {
        one: '.00',
        tow: '.00',
        three: '.00',
        four: '.00',
        five: '.00',
        six: '.00'
      },
      beginDate: this.queryForm.beginDate + ' 00:00:00',
      endDate: this.queryForm.beginDate + ' 23:59:59',
      store: '',
      loading: false,
      tableTitle: [{
        title: '编码',
        width: 'width: 10%'
      }, {
        title: '品名',
        width: 'width: 12%'
      }, {
        title: '规格',
        width: 'width: 7%'
      }, {
        title: '单位',
        width: 'width: 6%'
      }, {
        title: '数量',
        width: 'width: 6%'
      }, {
        title: '进货价',
        width: 'width: 7%'
      }, {
        title: '进价金额',
        width: 'width: 8%'
      }, {
        title: '零价',
        width: 'width: 7%'
      }, {
        title: '差价',
        width: 'width: 7%'
      }, {
        title: '批号',
        width: 'width: 9%'
      }, {
        title: '结存',
        width: 'width: 10%'
      }, {
        title: '供货商',
        width: 'width: 11%'
      }]
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    getTable() {
      this.loading = true;
      getDrugStatementTable(this.queryForm).then(res => {
        if (res.code === 200) {
          let data = res.data;
          this.tableDate = data
          if (data.length > 0) {
            let jj = 0;
            let lj = 0;
            data.forEach(t => {
              jj += t.QUANTITY * t.PRICE;
              lj += t.QUANTITY * t.PURCHASE_PRICE;
            })
            this.collectData = {
              one: this.formatInternationalPrice(jj),
              tow: this.formatInternationalPrice(lj),
              three: this.formatInternationalPrice( lj - jj),
              four: this.formatInternationalPrice(jj),
              five: this.formatInternationalPrice(lj),
              six: this.formatInternationalPrice( lj - jj)
            }
          } else {
            this.collectData = {
              one: '.00',
              tow: '.00',
              three: '.00',
              four: '.00',
              five: '.00',
              six: '.00'
            }
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    formatInternationalPrice(value) {
      value = value.toFixed(2)
      // 先将数字转换为字符串
      let strValue = value.toString()

      // 检查是否有小数点
      let [integerPart, decimalPart] = strValue.includes('.') ? strValue.split('.') : [strValue, '']
      // 将整数部分插入千位分隔符
      let formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      // 重新组合整数部分和小数部分
      let formattedValue = decimalPart ? `${formattedIntegerPart}.${decimalPart}` : formattedIntegerPart
      return formattedValue
    },
  }
}
</script>

<style scoped lang="scss">
.statement-home {
  width: 100%;

  .statement-title {
    font-size: 18px;
    text-align: center;
  }

  .statement-text {
    font-size: 22px;
    text-align: center;
  }

  .statement-data {
    display: flex;
    justify-content: space-between;

    .statement-item {
      display: flex;
    }
  }

  .statement-table {
    border: 1px solid black;
    height: 35px;

    .statement-master {
      display: flex;

      .statement-item {
        border-right: 1px solid black;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 34px;
      }
    }
  }

  .statement-list-master {
    border: 1px solid black;

    .statement-line {
      border-bottom: 1px solid black;
      display: flex;

      .statement-column {
        border-right: 1px solid black;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        white-space: nowrap; /* 确保文本在一行内显示 */
        overflow: hidden; /* 隐藏超出容器的内容 */
      }
    }
  }

  .statement-total {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    border-bottom: 1px solid black;
    padding: 5px;

    .total-item {
      display: flex;
      width: 32%;

      .total-text {
        display: flex;
        align-items: center;
      }
    }
  }
}
</style>
