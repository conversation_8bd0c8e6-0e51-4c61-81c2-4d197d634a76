<template>
  <div class="confirm-home">
    <div class="confirm-master">
      <div class="confirm-top">
        <div class="confirm-table">
          <middle-table-function ref="tableFunctionRef" @button-status="buttonStatusMonitor" @path-skip="pathSkip"></middle-table-function>
        </div>
        <div class="confirm-right">
          <right-function :button-status="buttonStatus" :key="key" @table-save="tableSaveMonitor" @right-select="confirmTableMonitor"></right-function>
        </div>
      </div>
      <div class="confirm-bottom">
        <bottom-function @send-table="confirmTableMonitor"></bottom-function>
      </div>
    </div>
  </div>
</template>

<script>
import bottomFunction from './examConfirm/bottomFunction.vue'
import RightFunction from './examConfirm/rightFunction.vue'
import MiddleTableFunction from './examConfirm/middleTableFunction.vue'
import {GetExportDeptDict} from '@/api/checkAndConfirm/checkCommon'
export default {
  name: 'examConfirmIndex',
  props: [],
  components: { MiddleTableFunction, RightFunction, bottomFunction },
  data() {
    return {
      buttonStatus: {},
      key: 0,
    }
  },
  created() {
    this.getExportDeptDict();
  },
  mounted() {
  },
  methods: {
    pathSkip(data){
      this.$emit("path-skip",data)
    },
    tableSaveMonitor(data){
      this.$refs.tableFunctionRef.tableSave(data);
    },
    confirmTableMonitor(data) {
      data.pageNum = 1;
      this.$store.commit("SET_TABLE", []);
      this.$refs.tableFunctionRef.getTable(data);
    },
    getExportDeptDict(){
      GetExportDeptDict().then(res => {
        this.$store.commit('SET_EXPORT_DICT_EXAM', res.data.uploadDept);
        this.$store.commit('SET_APPOINTMENT_DEPT_DICT', res.data.appointmentDept);
      });
    },
    buttonStatusMonitor(data){
      this.buttonStatus = data;
      ++this.key;
    },
  }
}
</script>

<style scoped lang="scss">
.confirm-home {
  height: 100%;
  width: 100%;

  .confirm-master {
    display: flex;
    flex-direction: column;

    .confirm-top {
      display: flex;
      height: 80%;
      border: 1px solid #1c84c6;

      .confirm-table {
        width: 92%;
        border: 1px solid #00a19b;
      }

      .confirm-right {
        width: 8%;
        border: 1px solid #000000;
      }
    }

    .confirm-bottom {
      border: 1px solid #000000;
      height: 20%;
    }

  }
  ::v-deep.el-message-box__header {
    position: relative;
    padding: 15px;
    padding-bottom: 10px;
    background-color: #185F7D;
    text-align: center;
  }
}
</style>
