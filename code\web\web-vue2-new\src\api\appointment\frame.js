import request from '@/utils/request'
const baseURL = 'Frame/'
// 查询检查项目下拉选
export function GainAppointment(query) {
  return request({
    url: baseURL + 'GainAppointment',
    method: 'get',
    params: query,
  })
}
// 查询项目时段配置
export function GetFrameDetail(query) {
  return request({
    url: baseURL + 'GetFrameDetail',
    method: 'get',
    params: query,
  })
}

// 查询基础时段配置
export function GetFrameInfo(query) {
  return request({
    url: baseURL + 'GetFrameInfo',
    method: 'get',
    params: query,
  })
}

//时段详情删除
export function DeleFrameDetail(data) {
  return request({
    url: baseURL + 'DeleFrameDetail',
    method: 'post',
    data: data,
  })
}
//基础时段删除
export function DeleFrameInfo(data) {
  return request({
    url: baseURL + 'DeleFrameInfo',
    method: 'post',
    data: data,
  })
}

//时段详情修改或新增
export function SetFrameDetail(data) {
  return request({
    url: baseURL + 'SetFrameDetail',
    method: 'post',
    data: data,
  })
}
//基础时段修改或新增
export function SetFrame(data) {
  return request({
    url: baseURL + 'SetFrame',
    method: 'post',
    data: data,
  })
}
