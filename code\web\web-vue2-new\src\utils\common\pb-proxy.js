//调用PB插件客户端 xyt
import axios from 'axios'

const ReqConfig = {
  host: '127.0.0.1',
  port: 9000,
  api: function (api) {
    return 'http://' + this.host + ':' + this.port + api
  }
}

const urls = {
  ExamConfirmPrint: ReqConfig.api('/ExamConfirmPrint'),
  ExamTakePacsPrint: ReqConfig.api('/ExamTakePacsPrint'),
  ExamItemRefundPrint: ReqConfig.api('/ExamItemRefundPrint'),
  CardReader:ReqConfig.api('/ReadClinicsCard')
}

// 检查确认单打印
export const ExamConfirmPrint = (params) => {
  return http.post(urls.ExamConfirmPrint, params)
}
// 影像领取单打印
export const ExamTakePacsPrint = (params) => {
  return http.post(urls.ExamTakePacsPrint, params)
}
export const ExamItemRefundPrint = (params) => {
  return http.post(urls.ExamItemRefundPrint, params)
}
export const CardReader = () => {
  return http.get(urls.CardReader)
}
/**
 * 设置允许浏览器跨域
 */
axios.defaults.withCredentials = true
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
axios.defaults.headers.get['Accept'] = '*/*'
//http 请求函数
var http = {
  get (url, params = {}, config) {
    return new Promise((resolve, reject) => {
      axios.get(url, {
        params
      }, config).then(res => {
        if (res) {
          resolve(res)
        }
      }).catch(error => {
        reject(error)
      })
    })
  },
  post (url, params = {}, config) {
    return new Promise((resolve, reject) => {
      axios.post(url, params, config).then(res => {
        if (res) {
          resolve(res)
        }
      }).catch(error => {
        reject(error)
      })
    })
  }
}
