<template>
  <div class="mr-home" :style="maxHeight">
    <div class="mr-left">
      <el-scrollbar style="height: 99%; width: 100%; overflow-x: hidden">
        <el-menu class="el-menu-vertical-demo" style="width: 100%" v-for="(item,index) in caseData"
                 :key="item.index"
        >
          <el-menu-item @click="cseHistoryClick(item)" :index="index.toString()">
            <div class="el-menu-vertical-demo-item">
              <span style="width: 30%">{{ item.CREATE_DATE_TIME }}</span>
              <span style="width: 50%">{{ item.DEPT_NAME }}</span>
              <span style="width: 20%;">{{ item.DOCTOR }}</span>
            </div>
          </el-menu-item>
        </el-menu>
      </el-scrollbar>
    </div>
    <div class="mr-right">
      <el-scrollbar style="height: 99%; width: 100%; overflow-x: hidden">
        <span class="my-sketch-span">1·主诉</span>
        <div class="my-sketch-input">
          <el-input disabled type="textarea" :rows="4" v-model="cseHistory.ILLNESS_DESC">
          </el-input>
        </div>

        <span class="my-sketch-span">2·体检</span>
        <div class="my-sketch-input">
          <el-input disabled type="textarea" :rows="4" v-model="cseHistory.BODY_EXAM">
          </el-input>
        </div>
        <span class="my-sketch-span">3·西医诊断</span>
        <div class="my-sketch-input">
          <el-input disabled type="textarea" :rows="4" v-model="cseHistory.DIAG_DESC">
          </el-input>
        </div>
        <span class="my-sketch-span">4·病情变化情况</span>
        <div class="my-sketch-input">
          <el-input disabled type="textarea" :rows="4" v-model="cseHistory.MEDICAL_RECORD">
          </el-input>
        </div>
        <span class="my-sketch-span">5·既往史</span>
        <div class="my-sketch-input">
          <el-input disabled type="textarea" :rows="4" v-model="cseHistory.ANAMNESIS">
          </el-input>
        </div>
        <span class="my-sketch-span">6·治疗意见</span>
        <div class="my-sketch-input">
          <el-input disabled type="textarea" :rows="4" v-model="cseHistory.OPERATION_RECORD">
          </el-input>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { getMaternalDataReportingInquireByPatientId } from '@/api/singlePage/maternalDataReportingInquire'

export default {
  name: 'moduleMr',
  props: ['rowData', 'maxHeight'],
  components: {},
  data() {
    return {
      caseData: [],
      cseHistory: {}
    }
  },
  created() {
    this.getMaternalByPatientId()
  },
  mounted() {
  },
  methods: {
    cseHistoryClick(item) {
      this.cseHistory = item
    },
    getMaternalByPatientId() {
      getMaternalDataReportingInquireByPatientId(this.rowData.PATIENT_ID).then(res => {
        if (res.code === 200) {
          this.caseData = res.data
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.mr-home {
  padding: 0;
  width: 100%;
  display: flex;

  .mr-left {
    width: 40%;

    .el-menu-vertical-demo-item {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
  }

  .mr-right {
    width: 60%;

    .my-sketch-span {
      font-size: 12px;
      font-weight: bolder;
      margin-left: 2%;
    }

    ::v-deep.el-textarea.is-disabled .el-textarea__inner {
      background-color: #FFFFFF;
      color: black;
    }
  }
}
</style>
