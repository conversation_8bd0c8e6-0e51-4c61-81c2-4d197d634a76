<template>
  <div class="single-master" v-if="bodyStatus">
    <div class="single-title">药品录入</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item style="padding: 0 10px;">
              <el-radio-group v-model="queueForm.type" @change="getDrugEntry">
                <el-radio :label="'0'">入库</el-radio>
                <el-radio :label="'1'">退货</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="开始时间:">
              <el-date-picker
                v-model="queueForm.beginDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker
                v-model="queueForm.endDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="入库单号:">
              <el-input v-model="queueForm.no" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <div class="element-button">
                <el-button type="primary" icon="el-icon-search" @click="getDrugEntry">查询</el-button>
                <el-button type="primary" v-if="queueForm.type === '0' && saveRow.DOCUMENT_NO" icon="el-icon-download" @click="saveTable">保存</el-button>
                <el-button type="primary" v-else icon="el-icon-download" disabled>保存</el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="element-table">
          <div v-if="queueForm.type === '0'">
            <el-table :data="inTableDate" style="width: 100%" border :height="(tableHeight-150)/2" highlight-current-row @row-click="tableClick">
              <el-table-column type="index" width="40" align="center"></el-table-column>
              <el-table-column prop="DOCUMENT_NO" align="center" label="入库单号" width="120"></el-table-column>
              <el-table-column prop="SUB_STORAGE" align="center" label="库房" width="80"></el-table-column>
              <el-table-column prop="IMPORT_DATE" align="center" label="入库日期" width="120"></el-table-column>
              <el-table-column prop="ACCOUNT_RECEIVABLE" align="center" label="应付款" width="90"></el-table-column>
              <el-table-column prop="IMPORT_CLASS" align="center" label="入库类别" width="100"></el-table-column>
              <el-table-column prop="OPERATOR" align="center" label="录入者" width="90"></el-table-column>
              <el-table-column prop="SUPPLIER" align="center" label="供应商" width="200"></el-table-column>
            </el-table>
          </div>

          <div v-if="queueForm.type === '1'">
            <el-table :data="inTableDate" style="width: 100%" border :height="(tableHeight-150)/2" highlight-current-row @row-click="tableClick">
              <el-table-column type="index" width="40" align="center"></el-table-column>
              <el-table-column prop="DOCUMENT_NO" align="center" label="入库单号" width="120"></el-table-column>
              <el-table-column prop="DEPT_NAME" align="center" label="库房" width="80"></el-table-column>
              <el-table-column prop="EXPORT_DATE" align="center" label="入库日期" width="120"></el-table-column>
              <el-table-column prop="ACCOUNT_RECEIVABLE" align="center" label="应付款" width="90"></el-table-column>
              <el-table-column prop="EXPORT_CLASS" align="center" label="入库类别" width="100"></el-table-column>
              <el-table-column prop="OPERATOR" align="center" label="录入者" width="90"></el-table-column>
              <el-table-column prop="RECEIVER" align="center" label="供应商" width="200"></el-table-column>
            </el-table>
          </div>
          <el-table :data="inParticularsTableDate" style="width: 100%" border :height="(tableHeight-150)/2"
                    highlight-current-row @row-click="projectTableClick">
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column prop="DOCUMENT_NO" align="center" label="入库单号" width="120"></el-table-column>
            <el-table-column prop="DRUG_CODE" align="center" label="药品代码" width="140"></el-table-column>
            <el-table-column prop="DRUG_NAME" align="center" label="药品名称" width="160"></el-table-column>
            <el-table-column prop="ENTERING_PRICE" align="center" label="录入价" width="80">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.STATUS" v-model="scope.row.ENTERING_PRICE"
                          ref="enteringPriceRefs"
                          @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.ENTERING_PRICE }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="PURCHASE_PRICE" align="center" label="进货价" width="80"></el-table-column>
            <el-table-column prop="DISCOUNT" align="center" label="折扣" width="80"></el-table-column>
            <el-table-column prop="TRADE_PRICE" align="center" label="零售价" width="80"></el-table-column>
            <el-table-column prop="DRUG_SPEC" align="center" label="规格" width="150"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDrugEntryList,getDrugEntryParticulars,saveDrugEntry } from '@/api/singlePage/drugEntry'
import {getUserInfoByEmpNo} from '@/api/singlePage/drugStatement';

export default {
  name: 'inquires',
  props: [],
  components: {},
  data() {
    return {
      inTableDate: [],
      inParticularsTableDate:[],
      saveRow:{},
      queueForm: {
        beginDate: this.formatDateMonth(new Date()),
        endDate: this.formatDate(new Date()),
        patientId: '',
        status: '',
        type: '0',
        no: ''
      },
      tableHeight: undefined,
      bodyStatus: false,
    }
  },
  created() {
    this.empNo = this.$route.query && this.$route.query.emp_no;
    this.handleResize()
    this.getUserInfo(this.empNo)
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    getUserInfo(empNo){
      if (empNo){
        getUserInfoByEmpNo(empNo).then(res => {
          if (res.code === 200){
            this.getDrugEntry()
            this.$store.commit('SET_ID', res.data.EMP_NO);
            console.log(this.$store.getters.empNo)
            this.bodyStatus = true;
          }
        })
      }else{
        this.bodyStatus = false;
        this.$msgbox.alert(
          '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
          '登录失败!!!' + '</div>' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
      }
    },
    saveTable(){
      let empNo = this.$store.getters.empNo;
      let particulars = this.inParticularsTableDate;
      let newParticularMaster=[];
      let newParticular = [];
      let uniqueMap = [];
      particulars.forEach(x => {
        const key = `${x.DRUG_CODE}-${x.DRUG_SPEC}-${x.FIRM_ID}`
        if (!uniqueMap.includes(key)){
          uniqueMap.push(key);
          newParticular.push({
            itemCode: x.DRUG_CODE,
            itemSpec: x.DRUG_SPEC,
            firmId: x.FIRM_ID,
            units: x.UNITS,
            price: x.PURCHASE_PRICE,
            enterDate: '',
            operatorId: empNo,
            flag: '0',
          })
        }
        newParticularMaster.push({
          itemCode: x.DRUG_CODE,
          documentNo: x.DOCUMENT_NO,
          price: x.PURCHASE_PRICE.toString(),
        })
      })
      console.log(this.saveRow)
      let saveData = {
        documentNo: this.saveRow.DOCUMENT_NO,
        empNo: empNo,
        items: newParticularMaster,
        prices: newParticular,
      }
      if (particulars.length === 0 || newParticular.length === 0){
        this.$msgbox.alert(
          '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
          '数据错乱' + '</div>' + '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
          '请重新提取数据 / 按F5刷新' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
        return;
      }
      //调用保存接口
      saveDrugEntry(saveData).then((res) => {
      })
    },
    InputBlur(row){
      this.$nextTick(() => {
        row.STATUS = false
      })
    },
    projectTableClick(row, column, event){
      if (this.queueForm.type === '0'){
        if (column.property === 'ENTERING_PRICE') {
          row.STATUS = true
          setTimeout(() => {
            this.$refs.enteringPriceRefs.focus()
          }, 200)
        }
      }
    },
    getDrugEntry() {
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.saveRow = {};
      getDrugEntryList(this.queueForm).then(res => {
        this.inTableDate = res.data
        this.inParticularsTableDate = [];
      }).finally(() => {
        loading.close();
      })
    },
    tableClick(row){
      console.log(row)
      this.saveRow = row;
      this.inParticularsTableDate = [];
      getDrugEntryParticulars(row.DOCUMENT_NO,this.queueForm.type).then(res => {
        if (res.code === 200){
          this.inParticularsTableDate = res.data;
          this.inParticularsTableDate.forEach(t => {
            t.STATUS = false;
          })
        }
      })
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    formatDateMonth(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      return `${year}-${month}-01`
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
</style>
