<template>
    <!-- 检查子类汇总查询 -->
    <div>
        <el-dialog :visible.sync="checkSubclassesStatus" :title="title" width="85%">
            <div class="refund-form">
                <el-form ref="form" :model="queueForm" :inline="true">
                    <el-form-item label="预约日期:">
                        <div class="form-flex">
                            <el-date-picker v-model="queueForm.beginDate" type="date"
                                value-format="yyyy-MM-dd"></el-date-picker>
                            <div class="date-f">-</div>
                            <el-date-picker v-model="queueForm.endDate" type="date"
                                value-format="yyyy-MM-dd"></el-date-picker>
                        </div>
                    </el-form-item>
                    <el-form-item label="检查子类:">
                        <el-select v-model="queueForm.examSubClass" placeholder="请选择">
                            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-radio-group v-model="queueForm.groupData" @change="triageMonitor">
                            <el-radio :label="1">全部</el-radio>
                            <el-radio :label="2">未分诊</el-radio>
                            <el-radio :label="3">已分诊</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="dataInitialize(queueForm)">提取</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div class="refund-table">
                <el-table :data="tableData" border style="width: 100%" max-height="600" highlight-current-row>
                    <el-table-column align="center" prop="checkSubclasses" label="检查子类" />
                    <el-table-column align="center" prop="totalCount" label="总数" />
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { GetCheckSubclassesTable } from "@/api/checkAndConfirm/checkSubclasses"

export default {
    name: 'refundInquiry',
    props: [],
    components: {},
    data() {
        return {
            title: '检查子类汇总查询',
            checkSubclassesStatus: false,
            tableData: [],
            queueForm: {
                groupData: 1, // 默认值为1
                examSubClass: '肺功能', // 默认值为肺功能
            },
            options: [{
                value: '肺功能',
                label: '肺功能'
            }, {
                value: '一氧化氮',
                label: '一氧化氮'
            }, {
                value: '睡眠检测',
                label: '睡眠检测'
            }],
        }
    },
    created() {
    },
    mounted() {
    },
    methods: {
        init(status) {
            if (status) {
                // 从store获取patient数据，并合并默认值
                const patientData = this.$store.getters.patient || {};
                this.queueForm = {
                    ...patientData,
                    groupData: patientData.groupData || 1, // 确保groupData默认为1
                    examSubClass: patientData.examSubClass || '肺功能', // 确保examSubClass默认为肺功能
                };
                this.dataInitialize(this.queueForm);
            }
        },
        dataInitialize(data) {
            this.tableData = [];
            this.checkSubclassesStatus = true;
            GetCheckSubclassesTable(data).then(res => {
                if (res.code === 200) {
                    this.tableData = res.data.list;
                }
            })
        },
        triageMonitor(data) {
            this.queueForm.type = data; // 如果需要传递额外参数，可保留此方法
        },
    }
}
</script>

<style scoped lang="scss">
// 所有样式保持不变
.refund-form {
    margin-top: 2px;
    margin-left: 10px;

    .form-flex {
        display: flex;
    }

    .date-f {
        padding: 0 5px;
    }

    ::v-deep.el-date-editor.el-input,
    .el-date-editor.el-input__inner {
        width: 135px;
    }

    ::v-deep.el-form-item {
        margin-bottom: 0;
    }
}

.refund-table {
    margin-top: 10px;

    ::v-deep.el-table--medium .el-table__cell {
        padding: 3px 0;
    }

    ::v-deep.el-table .el-table__header-wrapper th,
    .el-table .el-table__fixed-header-wrapper th {
        height: 30px;
        font-size: 14px;
    }

    ::v-deep.el-table th.el-table__cell>.cell {
        padding-left: 2px;
        padding-right: 2px;
    }

    ::v-deep.el-table .cell {
        padding-left: 2px;
        padding-right: 2px;
    }

    ::-webkit-scrollbar {
        width: 11px;
        height: 11px;
    }

    ::-webkit-scrollbar-thumb {
        background-color: #6bcaaf;
        border-radius: 10px;
    }

    ::v-deep.el-table__body tr.current-row>td.el-table__cell,
    .el-table__body tr.selection-row>td.el-table__cell {
        background-color: #1890FF;
        color: #FFFFFF;
    }
}
</style>