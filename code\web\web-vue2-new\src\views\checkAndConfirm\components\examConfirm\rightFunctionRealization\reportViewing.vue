<template>
  <div class="img-home">
    <el-dialog :title="title" :visible.sync="uploadStatus" width="80%">
      <!--    待测试-->
      <div v-if="single" class="home">
        <div class="pdf">
          <a :href="exportPathNew">
            <el-button type="text" class="myButton">
              <el-image fit="contain"
                        class="myImages"
                        :src="pdf"></el-image>
              <span class="mySpan">{{ pdfName }}</span>
            </el-button>
          </a>
        </div>
        <div>
          <el-carousel :interval="interval" indicator-position="none">
            <el-carousel-item v-for="item in imageList" :key="item">
              <el-image fit="contain"
                        :preview-src-list="imageList"
                        :z-index="imageList.length"
                        style="width: 100%;height: 100%;background-color: #FFFFFF"
                        :src="item"></el-image>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
      <div v-if="much" class="home">
        <div>
          <div style="display: flex;justify-content: center;">
            <div style="margin-right: 1%;margin-left: 1%;" v-for="item in buttonList" :key="item.examItemNo">
              <el-button @click="buttonDispose(item.examItemNo)" type="primary" plain>{{item.examSubClass}}</el-button>
            </div>
          </div>

          <div class="pdf">
            <a :href="exportPathNew">
              <el-button type="text" class="myButton">
                <el-image fit="contain"
                          class="myImages"
                          :src="pdf"></el-image>
                <span class="mySpan">{{ pdfName }}</span>
              </el-button>
            </a>
          </div>
          <div>
            <el-carousel :interval="interval" indicator-position="none">
              <el-carousel-item v-for="item in imageList" :key="item">
                <el-image fit="contain"
                          :preview-src-list="imageList"
                          :z-index="imageList.length"
                          style="width: 100%;height: 100%;background-color: #FFFFFF"
                          :src="item"></el-image>
              </el-carousel-item>
            </el-carousel>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetImages, DownloadPdf } from '@/api/checkAndConfirm/uploadPdf'
import pdf from '@/assets/icons/svg/dpf.png'
export default {
  name: 'reportViewing',
  props: [],
  components: {},
  data() {
    return {
      pdf: pdf,
      pdfName: '',
      examNo: '',
      buttonList: [],
      imageList: [],
      interval: 10000,
      examItemNo: 1,
      much: false,
      single: false,
      title: '报告查看',
      uploadStatus: false,
      exportPathNew: '',
    }
  },
  methods: {
    reportView(row){
      this.getImages(row.examNo);
    },
    /**
     * 获取检查号的 images图片信息
     */
    getImages(examNo) {
      const loading = this.$loading({
        lock: true,
        text: '休息一下,报告正在查询中(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      GetImages(examNo).then(res => {
        loading.close()
        this.uploadStatus = true;
        let data = res.data
        if (data.length > 1) {
          this.much = true
          this.single = false
          this.buttonList = data
          this.imageList = data[0].images
          this.pdfName = data[0].pdfName
        } else {
          this.single = true
          this.much = false
          this.imageList = data[0].images
          this.pdfName = data[0].pdfName
        }

      })
    },
    buttonDispose(examItemNo) {
      let data = this.buttonList
      this.examItemNo = examItemNo
      this.exportPathNew = this.exportPath + this.examNo + '/' + examItemNo
      this.imageList = data[examItemNo - 1].images
      this.pdfName = data[examItemNo - 1].pdfName
    },
    downloadPdf() {
      DownloadPdf(this.examNo, this.examItemNo)
    }
  }
}
</script>

<style scoped lang="scss">
.img-home{
  .home {
    border: 1px solid #defbfb;
    width: 80%;
    min-height: 600px;
    max-height: 700px;
    margin: 0 auto;
    background-color: #defbfb;
    box-shadow: 5px 5px 5px darkgrey;
    border-radius: 10px 10px 10px 10px;
    margin-top: 2%;
    .el-carousel__item h3 {
      color: #475669;
      font-size: 18px;
      opacity: 0.75;
      line-height: 300px;
      margin: 0;
    }

    .pdf {
      margin-top: 1%;
      width: 1.5%;
      display: table-cell;
      vertical-align: middle;
      text-align: center;
    }

    .myImages {
      height: 35px;
    }

    .myButton {
      /*padding-top: 0;*/
      /*padding-bottom: 0;*/
      /*padding-left: 0;*/
      padding: 0;
    }

    .mySpan {
    }

    .el-carousel__item:nth-child(2n) {
      background-color: #99a9bf;
    }

    .el-carousel__item:nth-child(2n+1) {
      background-color: #d3dce6;
    }

    ::v-deep .el-carousel__container {
      position: relative;
      height: 600px;
      width: 69%;
      margin: 0 auto;
      margin-top: 4%;
      margin-bottom: 1%;
    }

    ::v-deep .el-image {
      position: relative;
      display: block;
      /*overflow: overlay;*/
    }
  }
  ::v-deep.el-dialog:not(.is-fullscreen) {
    margin-top: 1vh !important;
  }
}
</style>
