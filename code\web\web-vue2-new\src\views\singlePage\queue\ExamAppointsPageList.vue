<template>
  <div class="single-master">
    <div class="single-title">全院检查预约安排</div>
    <div class="single-element" >
      <div class="element-master" >
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <div style="display: flex">
              <div>
                <div>
                  <el-form-item label="检查日期:">
                    <el-date-picker
                      v-model="queueForm.appointmentDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      format="yyyy-MM-dd"
                      placeholder="选择日期"
                      :clearable="false"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item label="查询类型:">
                    <el-select v-model="queueForm.type" placeholder="请选择">
                      <el-option
                        v-for="item in typeTree"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="患者ID:">
                    <el-input v-model="queueForm.patientId" placeholder="不支持模糊查询" clearable></el-input>
                  </el-form-item>
                  <el-form-item label="姓名:">
                    <el-input v-model="queueForm.name" placeholder="支持模糊查询" clearable></el-input>
                  </el-form-item>
                </div>
                <div>
                  <el-form-item label="时间类型:">
                    <el-select v-model="queueForm.timeId" placeholder="请选择" clearable>
                      <el-option
                        v-for="item in timeTree"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="预约地点:">
                    <el-select @change="getWindowTree" v-model="queueForm.unitId" placeholder="请选择" clearable>
                      <el-option
                        v-for="item in unitTree"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="预约诊室:" v-if="queueForm.unitId">
                    <el-select v-model="queueForm.windowId" placeholder="请选择" clearable>
                      <el-option
                        v-for="item in windowTree"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>
              </div>
              <div style="display:flex;flex-direction: column;justify-content: center;align-items: center;">
                <el-button type="primary" @click="getPageList" icon="el-icon-search">搜索</el-button>
                <el-button type="danger" @click="results" style="margin-top: 2px;" icon="el-icon-refresh">重置</el-button>
              </div>
            </div>
          </el-form>
        </div>
        <div class="element-table">
          <el-table :data="tableData" style="width: 100%" border :max-height="(tableHeight-240)" highlight-current-row>
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column prop="examNo" align="center" label="申请号"></el-table-column>
            <el-table-column prop="patientId" align="center" sortable label="患者Id"></el-table-column>
            <el-table-column prop="name" align="center" sortable label="姓名"></el-table-column>
            <el-table-column prop="examClass" align="center" label="类型"></el-table-column>
            <el-table-column prop="examSubClass" align="center" sortable label="子类"></el-table-column>
            <el-table-column prop="device" align="center" sortable label="项目" width="230"></el-table-column>
            <el-table-column prop="performedBy" align="center" sortable label="执行科室"></el-table-column>
            <el-table-column prop="memo" align="center" sortable label="状态" width="70">
              <template slot-scope="scope">
                <el-tag>{{scope.row.memo}}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="patientSource" align="center" sortable label="来源" width="70">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.patientSource === '1'">门诊</el-tag>
                <el-tag v-else-if="scope.row.patientSource === '2'">住院</el-tag>
                <el-tag v-else-if="scope.row.patientSource === '3'">体检</el-tag>
                <el-tag v-else-if="scope.row.patientSource === '4'">便民</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="appointmentDate" align="center" label="预约日期"></el-table-column>
            <el-table-column prop="appointmentTime" align="center" label="预约时间"></el-table-column>
            <el-table-column prop="unitName" align="center" sortable label="预约地点"></el-table-column>
            <el-table-column prop="windowName" align="center" sortable label="预约诊室"></el-table-column>
          </el-table>
          <div style="display: flex;justify-content: flex-end;margin-right: 20%;">
            <pagination v-show="total > 0" :limit.sync="queueForm.pageSize" :page.sync="queueForm.pageNum" :total="total"
                        @pagination="getPageList" :page-sizes="[10, 20, 50,100,200]"/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getThisDateDefault} from '@/utils/DateUtils';
import {GetQueuePageList,GetUnitList,GetWindowList} from '@/api/singlePage/queue/queuePageList'
export default {
  name: 'ExamAppointsPageList',
  computed: {
  },
  props: [],
  components: {},
  data() {
    return {
      tableData:[],
      total: 0,
      queueForm:{
        appointmentDate: getThisDateDefault(),
        pageNum: 1,
        pageSize: 10,
        patientId: '',
        name: '',
        timeId: '',
        unitId: '',
        windowId: '',
        type: '0',
        deptCode: '',
        empNo: '',
        performedBy: '',
      },
      tableHeight: undefined,
      typeTree:[{ value: '0', label: '全部' },
        { value: '1', label: '本科室' },
        { value: '2', label: '个人' },],
      timeTree:[{ value: '凌晨', label: '凌晨'},
        { value: '早晨', label: '早晨'},
        { value: '上午', label: '上午'},
        { value: '中午', label: '中午'},
        { value: '下午', label: '下午'},
        { value: '傍晚', label: '傍晚'},
        { value: '晚上', label: '晚上'},],
      unitTree:[],
      windowTree: [],
    }
  },
  created() {
    let empNo = this.$route.query && this.$route.query.empNo;
    let deptCode = this.$route.query && this.$route.query.deptCode;
    if (empNo){
      this.queueForm.type = "2"
      this.queueForm.empNo = empNo;
    }
    if (deptCode){
      this.queueForm.type = "1";
      this.queueForm.deptCode = deptCode;
    }
    this.handleResize()
    this.getUnitTree()
    this.getPageList();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    getPageList(){
      this.tableData = [];
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      GetQueuePageList(this.queueForm).then(res => {
        if (res.code === 200){
          this.tableData = res.data.list;
          this.total = res.data.total;
        }
      }).finally(() => {
        loading.close();
      })
    },
    getUnitTree(){
      this.unitTree = [];
      GetUnitList().then(res => {
        if (res.code === 200){
          this.unitTree = res.data;
        }
      })
    },
    getWindowTree(unitId){
      this.queueForm.windowId = '';
      this.windowTree = [];
      if (unitId){
        GetWindowList(unitId).then(res => {
          if (res.code === 200){
            this.windowTree = res.data;
          }
        })
      }

    },
    results(){
      this.queueForm={
        appointmentDate: getThisDateDefault(),
          pageNum: 1,
          pageSize: 10,
          patientId: '',
          name: '',
          timeId: '',
          unitId: '',
          windowId: '',
          type: this.queueForm.type,
          deptCode: this.queueForm.deptCode,
          empNo: this.queueForm.empNo,
          performedBy: '',
      };
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
::v-deep.el-button + .el-button {
  margin-left: 0px !important;
}
</style>
