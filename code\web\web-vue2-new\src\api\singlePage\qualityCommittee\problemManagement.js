// 问题管理模块
import request from '@/utils/request'

export function GetProblemManagement(data) {
    return request({
        url: '/ProblemManagement/GetProblemManagement',
        method: 'post',
        data: data
    })
}

export function AddProblemManagement(data) {
    return request({
        url: '/ProblemManagement/AddProblemManagement',
        method: 'post',
        data: data
    })
}

export function UpdateProblemManagement(data) {
    return request({
        url: '/ProblemManagement/UpdateProblemManagement',
        method: 'post',
        data: data
    })
}

export function DetailProblemManagement(id) {
    return request({
        url: '/ProblemManagement/DetailProblemManagement?id=' + id,
        method: 'get'
    })
}

export function DelProblemManagement(id) {
    return request({
        url: '/ProblemManagement/DelProblemManagement?id=' + id,
        method: 'get'
    })
}