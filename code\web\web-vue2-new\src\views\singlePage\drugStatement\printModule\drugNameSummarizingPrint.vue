<template>
  <div>
    <el-dialog title="打印预览" top="2vh" :visible.sync="dialogVisible" width="85%" :show-close="true"
               :close-on-press-escape="true" :close-on-click-modal="true"
    >
      <print-components @page-size="totalSizeClick"
                        :print-data="printDatas"
                        :key="printKey"
                        :print-data-list="printDatas.printDataList"
                        :table-title="printDatas.tableTitle"
                        :page-html="printDatas.pageHtml"
                        :total-page="printDatas.totalPage"
                        :total-size="printDatas.totalSize"
                        :merge-data="printDatas.mergeData"
                        :is-merge="printDatas.isMerge"
                        :title-html="printDatas.titleHtml"
                        :bottom-html="printDatas.bottomHtml"
                        :merge-size="printDatas.mergeSize"
      >
      </print-components>
    </el-dialog>
  </div>
</template>

<script>
import { getDrugStatementTable } from '@/api/singlePage/drugStatement'
import PrintComponents from '../../../../components/print/printComponents.vue'

/**
 * 按药品名称汇总
 */
export default {
  name: 'drugNameSummarizingPrint',
  props: [],
  components: { PrintComponents },
  data() {
    return {
      printDatas: {
        printDataList: [],//打印数据
        tableTitle: [{
          key: 'DRUG_NAME',
          title: '药品名称',
          width: 'width: 20%'
        }, {
          key: 'DRUG_SPEC',
          title: '规格',
          width: 'width: 5%'
        }, {
          key: 'PACKAGE_SPEC',
          title: '包装规格',
          width: 'width: 5%'
        }, {
          key: 'QUANTITY',
          title: '数量',
          width: 'width: 4%'
        }, {
          key: 'PRICE',
          title: '进价',
          width: 'width: 5%'
        }, {
          key: 'JJJE',
          title: '进价金额',
          width: 'width: 7%'
        }, {
          key: 'PURCHASE_PRICE',
          title: '零价',
          width: 'width: 5%'
        }, {
          key: 'LJJE',
          title: '零价金额',
          width: 'width: 7%'
        }, {
          key: 'CJJE',
          title: '差价金额',
          width: 'width: 7%'
        }, {
          key: 'FIRM_ID',
          title: '厂家',
          width: 'width: 15%'
        }, {
          key: 'SUPPLIER',
          title: '供应商',
          width: 'width: 20%'
        }],//标题数据
        pageHtml: '',//是否page同行
        totalPage: 0,//总行数
        totalSize: 17,//打印页每页显示条数
        mergeData: [],//合并数据
        isMerge: true,//是否合并
        titleHtml: '药品名称汇总',//每页title文字（需要带样式）
        bottomHtml: '',//每页底部文字（需要带样式）
        mergeSize: 4
      },
      printKey: 0,
      dialogVisible: false,
      QUANTITY: [],
      PRICE: [],
      JJJE: [],
      PURCHASE_PRICE: [],
      LJJE: [],
      CJJE: []
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    totalSizeClick(data) {
      ++this.printKey
      this.printDatas.totalSize = data
      this.chengeRows(data)
    },
    printData(data) {
      getDrugStatementTable(data).then(res => {
        if (res.code === 200) {
          let dataList = res.data
          this.tableData = dataList
          dataList.forEach(t => {
            t.CJJE = parseFloat((t.LJJE - t.JJJE).toFixed(2))
          })
          this.chengeRows(this.printDatas.totalSize)
          this.dialogVisible = true
        }
      })
    },
    chengeRows(val) {
      let tableData = this.sliceArr(this.tableData, val) //按照行数将表格数据切割为二维数组
      this.printDatas.printDataList = tableData
      this.printDatas.totalPage = tableData.length //这里拿到的就是总页面数
      tableData.forEach((item, index) => {
        let QUANTITY = 0
        let PRICE = 0
        let JJJE = 0
        let PURCHASE_PRICE = 0
        let LJJE = 0
        let CJJE = 0
        item.forEach((i, j) => {
          QUANTITY += i.QUANTITY //这个就是要计算的列的参数名合计，并且这里是按照每页计算的
          PRICE += i.PRICE
          JJJE += i.JJJE
          PURCHASE_PRICE += i.PURCHASE_PRICE
          LJJE += i.LJJE
          CJJE += i.CJJE
        })
        this.$set(this.QUANTITY, index, QUANTITY.toFixed(2))
        this.$set(this.PRICE, index, PRICE.toFixed(2))
        this.$set(this.JJJE, index, JJJE.toFixed(2))
        this.$set(this.PURCHASE_PRICE, index, PURCHASE_PRICE.toFixed(2))
        this.$set(this.LJJE, index, LJJE.toFixed(2))
        this.$set(this.CJJE, index, CJJE.toFixed(2))
        this.$set(this.printDatas.printDataList, index, item)
      })
      this.printDatas.mergeData = [this.QUANTITY, this.PRICE, this.JJJE, this.PURCHASE_PRICE, this.LJJE,this.CJJE,[],[]]
    },
    sliceArr(array, size) {
      if (array.length === 0) {
        return []
      }
      const numOfChunks = Math.ceil(array.length / size)
      const chunks = new Array(numOfChunks)
      for (let i = 0, j = 0; i < numOfChunks; i++) {
        chunks[i] = array.slice(j, j + size)
        j += size
      }
      return chunks
    }
  }
}
</script>

<style scoped lang="scss">

</style>
