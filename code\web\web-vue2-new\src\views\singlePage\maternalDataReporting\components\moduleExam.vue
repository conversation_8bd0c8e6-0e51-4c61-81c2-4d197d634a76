<template>
  <div class="module-exam-master" :style="maxHeight">
    <div class="single-master">
      <div class="element-table">
        <el-table :data="examData" style="width: 100%" border height="300px" highlight-current-row
                  @row-click="tableClick"
        >
          <el-table-column type="index" width="40" align="center"></el-table-column>
          <el-table-column prop="REQ_DATE_TIME" align="center" label="申请时间" width="120"></el-table-column>
          <el-table-column prop="EXAM_NO" align="center" label="申请序号" width="80"></el-table-column>
          <el-table-column prop="PATIENT_LOCAL_ID" align="center" label="检查号" width="75"></el-table-column>
          <el-table-column prop="EXAM_CLASS" align="center" label="检查类别" width="95"></el-table-column>
          <el-table-column prop="EXAM_SUB_CLASS" align="center" label="子类" width="120"></el-table-column>
          <el-table-column prop="REQ_PHYSICIAN" align="center" label="申请者" width="75"></el-table-column>
          <el-table-column prop="REPORTER" align="center" label="报告者" width="75"></el-table-column>
          <el-table-column prop="REPORT_DATE_TIME" align="center" label="报告时间" width="120"></el-table-column>
          <el-table-column prop="CONFIRM_DATE_TIME" align="center" label="报告确认时间" width="120"></el-table-column>
          <el-table-column prop="DEPT_NAME" align="center" label="申请科室" width="120"></el-table-column>
        </el-table>
      </div>
    </div>
    <div class="exam-case">
      <el-scrollbar style="height: 99%; width: 100%; overflow-x: hidden">
        <div v-if="thisExam && thisExam.EXAM_PARA">
          <div class="case-title">[检查参数]</div>
          <div class="case-text">{{ thisExam.EXAM_PARA }}</div>
        </div>
        <div v-if="thisExam && thisExam.DESCRIPTION">
          <div class="case-title">[检查所见]</div>
          <div class="case-text">{{ thisExam.DESCRIPTION }}</div>
        </div>
        <div v-if="thisExam && thisExam.IMPRESSION">
          <div class="case-title">[印象]</div>
          <div class="case-text">{{ thisExam.IMPRESSION }}</div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { getMaternalDataReportingExamApi } from '@/api/singlePage/maternalDataReportingInquire'

export default {
  name: 'moduleExam',
  props: ['rowData', 'maxHeight'],
  components: {},
  data() {
    return {
      examData: [],
      thisExam: {}
    }
  },
  created() {
    this.getMaternalDataReportingExam()
  },
  mounted() {
  },
  methods: {
    tableClick(row) {
      this.thisExam = row
    },
    getMaternalDataReportingExam() {
      getMaternalDataReportingExamApi(this.rowData.PATIENT_ID).then(res => {
        if (res.code === 200) {
          this.examData = res.data
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../../assets/styles/singlePage";

.module-exam-master {
  padding: 0;
  width: 100%;

  .exam-case {
    padding-left: 1%;
    height: 40%;
    border: 1px solid #3A71A8;

    .case-title {
      font-size: 17px;
      font-weight: 600;
    }

    .case-text {
      padding: 1px 0;
      padding-left: 50px;
      font-size: 16px;
      font-weight: 600;
      word-break: break-all;
    }

    @media screen and (max-width: 1250px) {
      .case-title {
        font-size: 16px;
      }
      .case-text {
        font-size: 15px;
      }
    }
    @media screen and (max-width: 1100px) {
      .case-title {
        font-size: 15px;
      }
      .case-text {
        font-size: 14px;
      }
    }
    @media screen and (max-width: 600px) {
      .case-title {
        font-size: 14px;
      }
      .case-text {
        font-size: 13px;
      }
    }
  }
}
</style>
