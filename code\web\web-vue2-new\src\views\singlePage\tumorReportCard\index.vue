<template>
    <div class="single-master">
        <div class="single-title">肿瘤报卡</div>
        <div class="single-element">
            <div class="element-master">
                <div class="element-form">
                    <el-form :inline="true" :model="queueForm" class="demo-form-inline">
                        <el-form-item label="开始时间:">
                            <el-date-picker v-model="queueForm.beginDate" type="date" value-format="yyyy-MM-dd"
                                format="yyyy-MM-dd" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="结束时间:">
                            <el-date-picker v-model="queueForm.endDate" type="date" value-format="yyyy-MM-dd"
                                format="yyyy-MM-dd" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <div class="element-button">
                                <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
                                <export-excel excelName="肿瘤报卡数据导出" :excelData="excelDate || []" :columnMap="columnMap">
                                    <el-button type="primary" icon="el-icon-folder-opened" slot="trigger"
                                        style="margin-left: 10%;">导出</el-button>
                                </export-excel>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="element-table">
                    <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 167"
                        highlight-current-row>
                        <el-table-column type="index" :index="indexMethod" align="center" width="40" />
                        <el-table-column prop="name" align="center" label="姓名" />
                        <el-table-column prop="id_no" align="center" label="身份证号" />
                        <el-table-column prop="性别" align="center" label="性别" />
                        <el-table-column prop="phone" align="center" label="联系电话" />
                        <el-table-column prop="fbbw" align="center" label="发病部位" />
                        <el-table-column prop="bllx" align="center" label="病理类型" />
                        <el-table-column prop="zdjg" align="center" label="诊断结果" />
                        <el-table-column prop="转归" align="center" label="转归" />
                    </el-table>
                    <!-- 分页 -->
                    <div>
                        <pagination v-show="total > 0" :limit.sync="queueForm.pageSize" :page.sync="queueForm.pageNum"
                            :total="total" @pagination="getList" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
  
<script>
import {
    GetTumorReportCardList,
    ExcelTumorReportCardList
} from "@/api/singlePage/tumorReportCard";
import ExportExcel from "@/components/excel/exportExcel";
export default {
    name: 'reportCardReview',
    props: [],
    components: {
        ExportExcel
    },
    data() {
        return {
            queueForm: {
                pageNum: 1,
                pageSize: 20,
                beginDate: this.getThreeDaysAgo(),
                endDate: this.getToday(),
            },
            tableDate: [],
            excelDate: [],
            total: 0,
            tableHeight: undefined,
            columnMap: [
                {
                    label: "姓名",
                    key: "姓名",
                },
                {
                    label: "身份证号",
                    key: "身份证号",
                },
                {
                    label: "其它证件类型",
                    key: "其它证件类型",
                },
                {
                    label: "其它证件号",
                    key: "其它证件号",
                },
                {
                    label: "联系电话",
                    key: "联系电话",
                },
                {
                    label: "性别",
                    key: "性别",
                },
                {
                    label: "出生年月",
                    key: "出生年月",
                },
                {
                    label: "文化程度",
                    key: "文化程度",
                },
                {
                    label: "婚姻状况",
                    key: "婚姻状况",
                },
                {
                    label: "联系人姓名",
                    key: "联系人姓名",
                },
                {
                    label: "与患者关系",
                    key: "与患者关系",
                },
                {
                    label: "联系人电话",
                    key: "联系人电话",
                },
                {
                    label: "民族",
                    key: "民族",
                },
                {
                    label: "职业",
                    key: "职业",
                },
                {
                    label: "工作单位",
                    key: "工作单位",
                },
                {
                    label: "户籍详细地址",
                    key: "户籍详细地址",
                },
                {
                    label: "常住详细地址",
                    key: "常住详细地址",
                },
                {
                    label: "发病部位",
                    key: "发病部位",
                },
                {
                    label: "病理类型",
                    key: "病理类型",
                },
                {
                    label: "icD10编号",
                    key: "icD10编号",
                },
                {
                    label: "行为",
                    key: "行为",
                },
                {
                    label: "分级",
                    key: "分级",
                },
                {
                    label: "分期",
                    key: "分期",
                },
                {
                    label: "t",
                    key: "t",
                },
                {
                    label: "n",
                    key: "n",
                },
                {
                    label: "m",
                    key: "m",
                },
                {
                    label: "诊断依据",
                    key: "诊断依据",
                },
                {
                    label: "发病日期",
                    key: "发病日期",
                },
                {
                    label: "确诊日期",
                    key: "确诊日期",
                },
                {
                    label: "诊断结果",
                    key: "诊断结果",
                },
                {
                    label: "诊断详细结果",
                    key: "诊断详细结果",
                },
                {
                    label: "诊断单位",
                    key: "诊断单位",
                },
                {
                    label: "门诊号",
                    key: "门诊号",
                },
                {
                    label: "住院号",
                    key: "住院号",
                },
                {
                    label: "首诊日期",
                    key: "首诊日期",
                },
                {
                    label: "最后接触日期",
                    key: "最后接触日期",
                },
                {
                    label: "最后接触状态",
                    key: "最后接触状态",
                },
                {
                    label: "死亡地点",
                    key: "死亡地点",
                },
                {
                    label: "根本死因",
                    key: "根本死因",
                },
                {
                    label: "死因ICD编号",
                    key: "死因编号",
                },
                {
                    label: "死亡地点",
                    key: "死亡地点",
                },
                {
                    label: "其它死亡地点",
                    key: "其它死亡地点",
                },
                {
                    label: "死亡报告医师",
                    key: "死亡报告医师",
                },
                {
                    label: "治疗项目",
                    key: "治疗项目",
                },
                {
                    label: "转归",
                    key: "转归",
                },
                {
                    label: "原诊断",
                    key: "原诊断",
                },
                {
                    label: "原诊断日期",
                    key: "原诊断日期",
                },
                {
                    label: "报告单位",
                    key: "报告单位",
                },
                {
                    label: "报告医师",
                    key: "报告医师",
                }
            ],
        }
    },

    created() {
        this.getList();
        this.handleResize();
    },

    mounted() {
        window.addEventListener('resize', this.handleResize); // 添加监听器
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize); // 移除监听器
    },

    methods: {
        // 初始化数据
        getList() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            // 初始化数据
            GetTumorReportCardList(this.queueForm).then(res => {
                this.tableDate = res.data.list;
                this.total = res.data.total[0].total;
            }).finally(() => {
                loading.close();
            });
            // 导出
            ExcelTumorReportCardList(this.queueForm).then(res => {
                this.excelDate = res.data.excelList;
            }).finally(() => {
                loading.close();
            });
        },

        // 序号翻页递增
        indexMethod(index) {
            let nowPage = this.queueForm.pageNum; //当前第几页，根据组件取值即可
            let nowLimit = this.queueForm.pageSize; //当前每页显示几条，根据组件取值即可
            return index + 1 + (nowPage - 1) * nowLimit; // 这里可以理解成一个公式
        },

        // 获取当前日期
        getToday() {
            //获取当前日期
            let myDate = new Date();
            let nowY = myDate.getFullYear();
            let nowM = myDate.getMonth() + 1;
            let nowD = myDate.getDate();
            let endTime =
                nowY +
                "-" +
                (nowM < 10 ? "0" + nowM : nowM) +
                "-" +
                (nowD < 10 ? "0" + nowD : nowD); //当前日期
            return endTime;
        },

        // 获取三十天之前的日期
        getThreeDaysAgo() {
            let myDate = new Date();
            let lw = new Date(myDate - 1000 * 60 * 60 * 24 * 30); //最后一个数字可改，最后一个数就是往前多少天的意思
            let lastY = lw.getFullYear();
            let lastM = lw.getMonth() + 1;
            let lastD = lw.getDate();
            let beginTime =
                lastY +
                "-" +
                (lastM < 10 ? "0" + lastM : lastM) +
                "-" +
                (lastD < 10 ? "0" + lastD : lastD); //三十天之前日期
            return beginTime;
        },

        // table中不显示时分秒
        formatterTime(row, column) {
            let data = row[column.property]
            return /\d{4}-\d{1,2}-\d{1,2}/g.exec(data)
        },

        // 自定义高度变化更新高度
        handleResize() {
            this.tableHeight = window.innerHeight; // 更新高度数据
        },
    }
}
</script>
  
<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
</style>
  