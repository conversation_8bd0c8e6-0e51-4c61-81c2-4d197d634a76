<template>
    <div class="single-master">
        <div class="single-title">寄生虫查询</div>
        <div class="single-element">
            <div class="element-master">
                <div class="element-form">
                    <el-form :inline="true" :model="queueForm" class="demo-form-inline">
                        <el-form-item label="开始时间:">
                            <el-date-picker v-model="queueForm.beginDate" type="date" value-format="yyyy-MM-dd"
                                format="yyyy-MM-dd" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="结束时间:">
                            <el-date-picker v-model="queueForm.endDate" type="date" value-format="yyyy-MM-dd"
                                format="yyyy-MM-dd" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="ID号:">
                            <el-input v-model="queueForm.patientId" placeholder="请输入患者id" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="患者姓名:">
                            <el-input v-model="queueForm.patientName" placeholder="请输入患者姓名" clearable></el-input>
                        </el-form-item>
                        <el-form-item>
                            <div class="element-button">
                                <el-button type="primary" icon="el-icon-search" @click="timeJudge">查询</el-button>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="element-table">
                    <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 167"
                        highlight-current-row>
                        <el-table-column prop="requesteD_DATE_TIME" align="center" label="报告日期" />
                        <el-table-column prop="tesT_NO" align="center" label="检验号">
                            <template slot-scope="scope">
                                <div class="text-click" @click="opentestNo(scope.row)">{{ scope.row.tesT_NO }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" align="center" label="病人姓名" />
                        <el-table-column prop="patienT_ID" align="center" label="病人ID" />
                        <el-table-column prop="result" align="center" label="检验结果" />
                        <el-table-column prop="doC_NAME" align="center" label="开单医生">
                            <template slot-scope="scope">
                                <div class="text-click" @click="opendocName(scope.row)">{{ scope.row.doC_NAME }}</div>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!-- 分页 -->
                    <div>
                        <pagination v-show="total > 0" :limit.sync="queueForm.pageSize" :page.sync="queueForm.pageNum"
                            :total="total" @pagination="timeJudge" />
                    </div>
                </div>
                <!-- 检验号弹框 -->
                <el-dialog :visible.sync="testNoDialog" :title="titleTestNo" width="85%">
                    <div class="element-table">
                        <el-table :data="tableDateTestNo" style="width: 100%" border :height="tableHeight - 180"
                            highlight-current-row>
                            <el-table-column prop="tesT_NO" align="center" label="检验号" />
                            <el-table-column prop="reporT_ITEM_NAME" align="center" label="检验报告项目名称" />
                            <el-table-column prop="result" align="center" label="结果" />
                            <el-table-column prop="units" align="center" label="单位" />
                            <el-table-column prop="resulT_DATE_TIME" align="center" label="检验时间" />
                            <el-table-column prop="abnormaL_INDICATOR" align="center" label="结果正常标志" />
                            <el-table-column prop="resultS_RPT_DATE_TIME" align="center" label="报告时间" />
                        </el-table>
                    </div>
                </el-dialog>
                <!-- 医生姓名弹框 -->
                <el-dialog :visible.sync="docNameDialog" :title="titleDocName" width="85%">
                    <div class="element-table">
                        <el-table :data="tableDateDocName" style="width: 100%" border :height="tableHeight - 180"
                            highlight-current-row>
                            <el-table-column prop="name" align="center" label="姓名" />
                            <el-table-column prop="xb" align="center" label="性别" />
                            <el-table-column prop="deptName" align="center" label="医生科室" />
                            <el-table-column prop="sjhm" align="center" label="手机号码" />
                            <el-table-column prop="swh" align="center" label="手机小号" />
                        </el-table>
                    </div>
                </el-dialog>
            </div>
        </div>
    </div>
</template>

<script>
import {
    GetParasiteList,
    GetParasiteByTestNo,
    GetParasiteByDocName
} from "@/api/singlePage/parasite";
export default {
    name: 'parasiteIndex',
    props: [],
    data() {
        return {
            queueForm: {
                pageNum: 1,
                pageSize: 10,
                beginDate: this.formatDate(new Date()),
                endDate: this.formatDate(new Date()),
            },
            tableDate: [],
            total: 0,
            tableDateTestNo: [],
            tableDateDocName: [],
            titleTestNo: "检验详情信息",
            titleDocName: "医生详情信息",
            testNoDialog: false,
            docNameDialog: false
        }
    },

    created() {
        this.timeJudge();
        this.handleResize();
    },

    mounted() {
        window.addEventListener('resize', this.handleResize); // 添加监听器
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize); // 移除监听器
    },

    methods: {
        // 选择时间限制
        timeJudge() {
            const start = new Date(this.queueForm.beginDate);
            const end = new Date(this.queueForm.endDate);
            // 开始时间是否大于结束时间
            if (start > end) {
                this.showMessageA('开始时间不能大于结束时间'); // 调用消息提示
            } else {
                // 计算日期差是否超过1周（以天为单位）
                const diffDays = (end - start) / (1000 * 60 * 60 * 24); // 毫秒转为天
                if (diffDays >= 7) {
                    this.showMessageA('时间跨度不能超过一周'); // 调用消息提示
                } else {
                    // 如果验证通过
                    this.showMessageA('查询数据成功'); // 调用消息提示
                    this.getList();
                }
            }
        },

        // 初始化数据
        getList() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            GetParasiteList(this.queueForm).then(res => {
                this.tableDate = res.data.list;
                this.total = res.data.total[0].total;
            }).finally(() => {
                loading.close();
            });
        },

        // 点击某一行检验号操作
        opentestNo(row) {
            this.testNoDialog = true;
            GetParasiteByTestNo(row).then(res => {
                this.tableDateTestNo = res.data.listTestNo;
            });
        },

        // 点击某一行医生姓名操作
        opendocName(row) {
            this.docNameDialog = true;
            GetParasiteByDocName(row).then(res => {
                this.tableDateDocName = res.data.listDocName;
            });
        },

        // 序号翻页递增
        indexMethod(index) {
            let nowPage = this.queueForm.pageNum; //当前第几页，根据组件取值即可
            let nowLimit = this.queueForm.pageSize; //当前每页显示几条，根据组件取值即可
            return index + 1 + (nowPage - 1) * nowLimit; // 这里可以理解成一个公式
        },

        // 默认当前时间
        formatDate(date) {
            const year = date.getFullYear()
            const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
            const day = date.getDate().toString().padStart(2, '0')
            return `${year}-${month}-${day}`
        },

        // 提示信息封装
        showMessageA(message) {
            const h = this.$createElement;
            this.$notify({
                title: '提示信息！',
                message: h('i', { style: 'color: teal' }, message)
            });
        },

        // 自定义高度变化更新高度
        handleResize() {
            this.tableHeight = window.innerHeight; // 更新高度数据
        },
    }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.text-click {
    color: #00afff;
}

:hover.text-click {
    cursor: pointer;
    border-bottom: 1px solid #00afff;
}
</style>