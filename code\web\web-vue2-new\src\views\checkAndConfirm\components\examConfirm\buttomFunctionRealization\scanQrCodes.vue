<template>
  <div class="qr-home">
    <el-dialog
      title="患者扫码"
      :visible.sync="status"
      width="30%">
      <div class="qf-flex" >
        <el-input style="width: 150px;" ref="scanInput" v-model="patientId" @input="onScan" @blur="patientIdSend" />
      </div>
      <div>
        <div class="qf-flex" style="margin-top: 20px;">
          <el-button type="primary" round @click="patientIdSend">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
/**
 * 扫码查询功能
 */
export default {
  name: 'scanQrCodes',
  props: [],
  components: {},
  data() {
    return {
      status: false,
      patientId: '',
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    patientIdSend(){
      if (this.patientId){
        this.$emit("send-patient",this.patientId)
        this.status = false;
      }else{
      }
    },
    onScan(event) {
      this.patientId = event;
      if (event.length >= 8){
        setTimeout(() => {
          this.$refs.scanInput.blur();
        },500)
      }
      // 当扫码枪扫描时，数据会在这里被捕获
    },
    codeScanner(status){
      this.status = status;
      this.patientId = '';
      setTimeout(() => {
        this.$refs.scanInput.focus();
      },200)
    },
  }
}
</script>

<style scoped lang="scss">
.qr-home{

  ::v-deep.el-radio__label {
    font-size: 18px;
    padding-left: 10px;
  }

  .qf-flex{
    display: flex;
    justify-content: center;
  }
}
</style>
