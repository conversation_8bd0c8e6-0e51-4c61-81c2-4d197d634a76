<template>
  <div class="single-master">
  
    <div class="element-master">
      <div class="my-table">
  
        <el-table :data="secondList" style="width: 100%" border height="300px" highlight-current-row
          @row-click="handleTableClick">
          <el-table-column prop="reQ_DATE_TIME" align="center" label="申请时间" :show-overflow-tooltip="true" width="150px" />
  
          <el-table-column prop="exaM_NO" align="center" label="检查号" :show-overflow-tooltip="true" width="80px" />
          <el-table-column prop="exaM_CLASS" align="center" label="检查类别" :show-overflow-tooltip="true" width="80px" />
          <el-table-column prop="exaM_SUB_CLASS" align="center" label="申请者" :show-overflow-tooltip="true" width="60px" />
          <el-table-column prop="reQ_PHYSICIAN" align="center" label=" 报告者" :show-overflow-tooltip="true" width="60px">
  
          </el-table-column>
          <el-table-column prop="reporT_DATE_TIME" align="center" label=" 报告时间" :show-overflow-tooltip="true"
            width="150px">
  
          </el-table-column>
          <el-table-column prop="confirM_DATE_TIME" align="center" label=" 报告确认时间" :show-overflow-tooltip="true"
            width="150px">
  
          </el-table-column> <el-table-column prop="depT_NAME" align="center" label=" 申请科室" :show-overflow-tooltip="true">
  
          </el-table-column>
        </el-table>
  
      </div>
  
      <div v-for="(item, index) in examResultList" :key="index" :value="item">
        <el-tag> {{examNo}}</el-tag>
        <el-tag> 检查参数：</el-tag>
        <el-input v-model="item.exaM_PARA" clearable disabled type="textarea" autosize></el-input>
        <el-tag> 检查所见：</el-tag>
        <el-input v-model="item.impression" clearable disabled type="textarea" autosize></el-input>
        <el-tag> 印象：</el-tag>
        <el-input v-model="item.description" clearable disabled type="textarea" autosize></el-input>
  
      </div>
  
  
  
    </div>
  
  </div>
</template>

<script>

import {
  GetExamResultList
} from "@/api/singlePage/diagnosedInfo";
export default {
  name: 'parasiteIndex',
  props:
  {
    secondList: Array,


  },
  data() {
    return {
      examNo: undefined,
      examResultList: [],
      currentTabIndex: '0',
      activeName: 'first',
      queueForm: {
        pageNum: 1,
        pageSize: 10,

      },
      baseInfo: [],
      tableDate: [],
      total: 0,
      tableDateTestNo: [],
      tableDateDocName: [],
      titleTestNo: "检验详情信息",
      titleDocName: "医生详情信息",
      testNoDialog: false,
      docNameDialog: false,
      tableHeight: undefined,
    }
  },

  created() {
    //this.getList();
    this.handleResize();
  },
  watch: {
    secondList: {
      handler(newVal, oldVal) {
        if (newVal) {
          // console.log('11', newVal);

        }
      },
      deep: true
    }
  },

  mounted() {
    window.addEventListener('resize', this.handleResize); // 添加监听器


  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize); // 移除监听器
  },

  methods: {

    handleTableClick(row) {
      this.examNo = row.exaM_NO

      let param = {
        examNo: row.exaM_NO
      }
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力导出中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      GetExamResultList(param).then(res => {
        this.examResultList = res.data.examResult;
      }).finally(() => {
        loading.close();
      });
    },


    // 自定义高度变化更新高度
    handleResize() {
      this.tableHeight = window.innerHeight; // 更新高度数据
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.text-click {
  color: #00afff;
}

:hover.text-click {
  cursor: pointer;
  border-bottom: 1px solid #00afff;
}

.my-table {
  ::v-deep.el-table--medium .el-table__cell {
    padding: 0;
  }

  ::v-deep.el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    word-break: break-word;
    background-color: #f8f8f9;
    color: #515a6e;
    height: 30px;
    font-size: 14px;
  }

  ::v-deep.el-table th.el-table__cell>.cell {
    padding: 0;
  }

  ::v-deep.el-table--border .el-table__cell:first-child .cell {
    padding: 0;
  }

  ::v-deep.el-button+.el-button {
    margin-left: 2px;
  }

  ::v-deep.el-table .cell {
    padding: 1px;
  }

  /* ---el-table滚动条公共样式--- */
  // 滚动条的宽度
  ::v-deep.el-table__body-wrapper::-webkit-scrollbar {
    width: 10px; // 横向滚动条
    height: 10px; // 纵向滚动条 必写
  }

  // 滚动条的滑块
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }
}
</style>