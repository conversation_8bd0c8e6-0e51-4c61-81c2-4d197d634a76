<template>
    <div class="single-master">
        <div class="single-title">会议管理模块</div>
        <div class="single-element">
            <div class="element-master">
                <!-- form -->
                <div class="element-form">
                    <el-form :inline="true" :model="queueForm">
                        <el-form-item label="委员会名称:">
                            <el-select v-model="queueForm.committeeName" filterable clearable placeholder="请选择">
                                <el-option v-for="item in committeeNameDict" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="日期:">
                            <el-date-picker v-model="queueForm.yearAndMonth" type="month" placeholder="请选择日期"
                                value-format="yyyy-MM" format="yyyy-MM" />
                        </el-form-item>
                        <el-form-item>
                            <div class="element-button">
                                <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
                                <el-button type="primary" icon="el-icon-plus" @click="add">新增</el-button>
                                <el-button type="primary" icon="el-icon-delete" @click="del">删除</el-button>
                                <el-button type="primary" icon="el-icon-edit-outline" @click="historical">历史记录</el-button>
                                <el-button type="primary" icon="el-icon-share" @click="indexOne">首页</el-button>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <!-- table -->
                <div class="element-table">
                    <div class="my-table">
                        <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 117"
                            highlight-current-row @row-click="handleRowClick" @cell-dblclick="doubleSelectionChange">
                            <el-table-column type="index" align="center" />
                            <el-table-column prop="id" align="center" label="委员会名称" />
                            <el-table-column prop="yearandmonth" align="center" label="日期" />
                            <el-table-column prop="filenamE1" align="center" label="附件1">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.fileurL1 !== null">
                                        {{ (scope.row.filenamE1) }}
                                    </span>
                                    <span v-else style="color: red;">
                                        未上传文件
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="filenamE2" align="center" label="附件2">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.fileurL2 !== null">
                                        {{ scope.row.filenamE2 }}
                                    </span>
                                    <span v-else style="color: red;">
                                        未上传文件
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="filenamE3" align="center" label="附件3">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.fileurL3 !== null">
                                        {{ scope.row.filenamE3 }}
                                    </span>
                                    <span v-else style="color: red;">
                                        未上传文件
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="filenamE4" align="center" label="附件4">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.fileurL4 !== null">
                                        {{ scope.row.filenamE4 }}
                                    </span>
                                    <span v-else style="color: red;">
                                        未上传文件
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column fixed="right" align="center" label="附件查看">
                                <template slot-scope="scope">
                                    <el-dropdown trigger="click" @command="handleCommand">
                                        <el-button type="text"><span style="color: red;">查看附件</span></el-button>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item
                                                :command="{ action: 'downloadAttachment1', row: scope.row, attachmentName: '附件1会议通知' }">
                                                附件1会议通知
                                            </el-dropdown-item>
                                            <el-dropdown-item
                                                :command="{ action: 'downloadAttachment2', row: scope.row, attachmentName: '附件2签到表' }">
                                                附件2签到表
                                            </el-dropdown-item>
                                            <el-dropdown-item
                                                :command="{ action: 'downloadAttachment3', row: scope.row, attachmentName: '附件3照片' }">
                                                附件3照片
                                            </el-dropdown-item>
                                            <el-dropdown-item
                                                :command="{ action: 'downloadAttachment4', row: scope.row, attachmentName: '附件4课件' }">
                                                附件4课件
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <!-- 附件查看drawer -->
                <el-drawer :title="titleDrawer" :visible.sync="drawerOpen" :before-close="handleCloseDra"
                    :direction="directionDra" size="85%">
                    <iframe :src="srcFileUrl" frameborder="no" style="width: 100%; height: 100%" scrolling="auto" />
                </el-drawer>
                <!-- 历史记录drawer -->
                <el-drawer :title="titleDrawerHis" :visible.sync="drawerOpenHis" :direction="directionDraHis"
                    :before-close="handleCloseHis" size="80%">
                    <!-- formHis -->
                    <div class="element-form">
                        <el-form :inline="true" :model="queueFormHis">
                            <el-form-item label="委员会名称:">
                                <el-select v-model="queueFormHis.committeeName" filterable clearable placeholder="请选择">
                                    <el-option v-for="item in committeeNameDict" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="日期:">
                                <el-date-picker v-model="queueFormHis.yearAndMonth" type="month" placeholder="请选择日期"
                                    value-format="yyyy-MM" format="yyyy-MM" />
                            </el-form-item>
                            <el-form-item>
                                <div class="element-button">
                                    <el-button type="primary" icon="el-icon-search" @click="getHistorical">查询</el-button>
                                    <el-button type="primary" icon="el-icon-delete" @click="delHis">删除</el-button>
                                </div>
                            </el-form-item>
                        </el-form>
                    </div>
                    <!-- tableHis -->
                    <div class="element-table">
                        <div class="my-table">
                            <el-table :data="tableDateHis" style="width: 100%" border :height="tableHeight - 117"
                                highlight-current-row @row-click="handleRowClick">
                                <el-table-column type="index" align="center" />
                                <el-table-column prop="id" align="center" label="委员会名称" />
                                <el-table-column prop="yearandmonth" align="center" label="日期" />
                                <el-table-column prop="filenamE1" align="center" label="附件1会议通知">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.fileurL1 !== null">
                                            {{ (scope.row.filenamE1) }}
                                        </span>
                                        <span v-else style="color: red;">
                                            未上传文件
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="filenamE2" align="center" label="附件2签到表">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.fileurL2 !== null">
                                            {{ scope.row.filenamE2 }}
                                        </span>
                                        <span v-else style="color: red;">
                                            未上传文件
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="filenamE3" align="center" label="附件3照片">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.fileurL3 !== null">
                                            {{ scope.row.filenamE3 }}
                                        </span>
                                        <span v-else style="color: red;">
                                            未上传文件
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="filenamE4" align="center" label="附件4课件">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.fileurL4 !== null">
                                            {{ scope.row.filenamE4 }}
                                        </span>
                                        <span v-else style="color: red;">
                                            未上传文件
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column fixed="right" align="center" label="附件查看">
                                    <template slot-scope="scope">
                                        <el-dropdown trigger="click" @command="handleCommand">
                                            <el-button type="text"><span style="color: red;">查看附件</span></el-button>
                                            <el-dropdown-menu slot="dropdown">
                                                <el-dropdown-item
                                                    :command="{ action: 'downloadAttachment1', row: scope.row, attachmentName: '附件1会议通知' }">
                                                    附件1会议通知
                                                </el-dropdown-item>
                                                <el-dropdown-item
                                                    :command="{ action: 'downloadAttachment2', row: scope.row, attachmentName: '附件2签到表' }">
                                                    附件2签到表
                                                </el-dropdown-item>
                                                <el-dropdown-item
                                                    :command="{ action: 'downloadAttachment3', row: scope.row, attachmentName: '附件3照片' }">
                                                    附件3照片
                                                </el-dropdown-item>
                                                <el-dropdown-item
                                                    :command="{ action: 'downloadAttachment4', row: scope.row, attachmentName: '附件4课件' }">
                                                    附件4课件
                                                </el-dropdown-item>
                                            </el-dropdown-menu>
                                        </el-dropdown>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </div>
                </el-drawer>
                <!-- 双击弹框drawer -->
                <el-drawer :title="titleDrawOpenDouble" :visible.sync="drawOpenDouble" :before-close="handleCloseDouble"
                    size="60%">
                    <el-scrollbar style="height: 98%; width: 100%; overflow-x: hidden">
                        <div class="single-master">
                            <div class="single-element">
                                <div class="element-master">
                                    <div v-if="this.queueFormDouble.meettingTitle !== ''">
                                        <h1>
                                            {{ this.queueFormDouble.meettingtitle }}
                                        </h1>
                                    </div>
                                    <div v-if="this.queueFormDouble.meettingcontent !== ''">
                                        <h3>
                                            {{ this.queueFormDouble.meettingcontent }}
                                        </h3>
                                    </div>
                                    <div v-if="this.queueFormDouble.fileurL1 !== ''">
                                        <h5>
                                            附件1会议通知:
                                            <el-link :href="this.queueFormDouble.safeUrl1" target="_blank">
                                                {{ this.queueFormDouble.filenamE1 }}
                                            </el-link>
                                        </h5>
                                    </div>
                                    <div v-if="this.queueFormDouble.fileurL2 !== ''">
                                        <h5>
                                            附件2签到表:
                                            <el-link :href="this.queueFormDouble.safeUrl2" target="_blank">
                                                {{ this.queueFormDouble.filenamE2 }}
                                            </el-link>
                                        </h5>
                                    </div>
                                    <div v-if="this.queueFormDouble.fileurL3 !== ''">
                                        <h5>
                                            附件3照片:
                                            <el-link :href="this.queueFormDouble.safeUrl3" target="_blank">
                                                {{ this.queueFormDouble.filenamE3 }}
                                            </el-link>
                                        </h5>
                                    </div>
                                    <div v-if="this.queueFormDouble.fileurL4 !== ''">
                                        <h5>
                                            附件4课件:
                                            <el-link :href="this.queueFormDouble.safeUrl4" target="_blank">
                                                {{ this.queueFormDouble.filenamE4 }}
                                            </el-link>
                                        </h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-scrollbar>
                </el-drawer>
                <!-- 新增dialog -->
                <el-dialog :visible.sync="dialogOpen" :title="titleDialog" width="75%" :before-close="handleCloseDia">
                    <div class="scroll-container">
                        <el-scrollbar style="height: 98%; width: 100%; overflow-x: hidden">
                            <el-form :inline="true" :model="queueFormFile" :rules="rules" ref="dataForm">
                                <div>
                                    <el-form-item label="委员会名称:" prop="committeeName">
                                        <el-select v-model="queueFormFile.committeeName" filterable clearable
                                            placeholder="请选择">
                                            <el-option v-for="item in committeeNameDict" :key="item.value"
                                                :label="item.label" :value="item.value">
                                            </el-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="日期:" prop="yearAndMonth">
                                        <el-date-picker v-model="queueFormFile.yearAndMonth" type="month"
                                            placeholder="请选择日期" value-format="yyyy-MM" format="yyyy-MM" />
                                    </el-form-item>
                                </div>
                                <div>
                                    <el-form-item label="会议记录标题:" prop="meettingTitle">
                                        <el-input placeholder="请输入会议记录标题" v-model="queueFormFile.meettingTitle" clearable
                                            style="width: 200px;" />
                                    </el-form-item>
                                </div>
                                <div>
                                    <el-form-item label="会议记要内容:" prop="meettingContent">
                                        <el-input placeholder="请输入会议记要内容" v-model="queueFormFile.meettingContent"
                                            type="textarea" :autosize="{ minRows: 5, maxRows: 6 }" style="width: 500px;"
                                            show-word-limit maxlength="3000(输入框可上下自定义拉长)">
                                        </el-input>
                                    </el-form-item>
                                </div>
                                <div>
                                    <span>
                                        <el-form-item>
                                            <input v-model="attachment1" type="text" class="unit_input" />
                                            <el-upload class="upload-demo" ref="upload" action="#" :multiple="false"
                                                :show-file-list="true" :before-upload="beforeUpload" accept=".pdf"
                                                :on-preview="handlePreview" :on-exceed="handleExceed" :on-change="onChange1"
                                                :file-list="fileList1" :auto-upload="false" :limit="10" drag>
                                                <i class="el-icon-upload"></i>
                                                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                            </el-upload>
                                        </el-form-item>
                                    </span>
                                    <span style="margin-left: 200px;">
                                        <el-form-item>
                                            <input v-model="attachment2" type="text" class="unit_input" readonly />
                                            <el-upload class="upload-demo" ref="upload" action="#" :multiple="false"
                                                :show-file-list="true" :before-upload="beforeUpload" accept=".pdf"
                                                :on-preview="handlePreview" :on-exceed="handleExceed" :on-change="onChange2"
                                                :file-list="fileList2" :auto-upload="false" :limit="10" drag>
                                                <i class="el-icon-upload"></i>
                                                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                            </el-upload>
                                        </el-form-item>
                                    </span>
                                </div>
                                <div>
                                    <span>
                                        <el-form-item>
                                            <input v-model="attachment3" type="text" class="unit_input" readonly />
                                            <el-upload class="upload-demo" ref="upload" action="#" :multiple="false"
                                                :show-file-list="true" :before-upload="beforeUpload" accept=".pdf"
                                                :on-preview="handlePreview" :on-exceed="handleExceed" :on-change="onChange3"
                                                :file-list="fileList3" :auto-upload="false" :limit="10" drag>
                                                <i class="el-icon-upload"></i>
                                                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                            </el-upload>
                                        </el-form-item>
                                    </span>
                                    <span style="margin-left: 200px;">
                                        <el-form-item>
                                            <input v-model="attachment4" type="text" class="unit_input" readonly />
                                            <el-upload class="upload-demo" ref="upload" action="#" :multiple="false"
                                                :show-file-list="true" :before-upload="beforeUpload" accept=".pdf"
                                                :on-preview="handlePreview" :on-exceed="handleExceed" :on-change="onChange4"
                                                :file-list="fileList4" :auto-upload="false" :limit="10" drag>
                                                <i class="el-icon-upload"></i>
                                                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                            </el-upload>
                                        </el-form-item>
                                    </span>
                                </div>
                            </el-form>
                        </el-scrollbar>
                    </div>
                    <span slot="footer">
                        <el-button type="primary" @click="dialogOpen = false">取 消</el-button>
                        <el-button type="primary" @click="saveUpload">确 定</el-button>
                    </span>
                </el-dialog>
            </div>
        </div>
    </div>
</template>

<script>
import {
    GetCommitteeNameDict
} from "@/api/singlePage/qualityCommittee/systemManagement";
import {
    MeetingManagementFile,
    MeetingManagementFileOne,
    GetMeetingManagementFile,
    GetMeetingManagementFileHistorical,
    MeetingManagementDel,
    MeetingManagementDelHis,
    GetMeetingManagementFileDetail
} from "@/api/singlePage/qualityCommittee/meetingManagement";
import env from '@/utils/ApiConfig';
export default {
    name: '',
    props: [],
    data() {
        return {
            envUrl: env.get_file_url(),
            tableHeight: undefined,
            extraFunctionKey: 0,
            srcFileUrl: '', // 文件路径
            titleDrawer: '会议管理文件查看', // 双击抽屉弹框标题
            drawerOpen: false, // 双击抽屉弹框标识
            directionDra: 'btt', // 弹框打开方式 (ltr:从左往右开,rtl:从右往左开,ttb:从上往下开,btt:从下往上开,)
            titleDrawerHis: '会议管理历史记录文件查看', // 历史记录按钮抽屉弹框标题
            drawerOpenHis: false, // 历史记录按钮抽屉弹框标识
            directionDraHis: 'ttb', // 弹框打开方式 (ltr:从左往右开,rtl:从右往左开,ttb:从上往下开,btt:从下往上开,)
            titleDrawOpenDouble: '会议内容', // 双击抽屉弹框标题
            drawOpenDouble: false, // 双击抽屉弹框标识
            queueForm: { // 首页传递参数
                pageNum: 1,
                pageSize: 20,
                committeeName: '',
                yearAndMonth: ''
            },
            queueFormHis: {// 历史记录传递参数
                committeeName: '',
                yearAndMonth: ''
            },
            queueFormFile: { // 新增弹框传递参数
                committeeName: '',
                yearAndMonth: '',
                meettingTitle: '',
                meettingContent: ''
            },
            queueFormDouble: {// 双击弹框参数
                meettingtitle: '',
                meettingcontent: '',
                filenamE1: '',
                filenamE2: '',
                filenamE2: '',
                filenamE2: '',
                fileurL1: '',
                fileurL2: '',
                fileurL3: '',
                fileurL4: '',
                safeUrl1: '',
                safeUrl2: '',
                safeUrl3: '',
                safeUrl4: ''
            },
            delForm: {}, // 删除参数
            titleDialog: '会议管理文件上传', // 新增弹框标题
            dialogOpen: false, // 新增弹框标识
            attachment1: '附件1会议通知',
            attachment2: '附件2签到表',
            attachment3: '附件3照片',
            attachment4: '附件4课件',
            fileList1: [], // 附件1会议通知
            fileList2: [], // 附件2签到表
            fileList3: [], // 附件3照片
            fileList4: [], // 附件4课件
            committeeNameDict: [], // 委员会名称字典数据
            tableDate: [],// 主页面数据展示
            tableDateHis: [],// 历史记录数据展示
            rules: {
                committeeName: [
                    { required: true, message: '请输入委员会名称', trigger: 'blur' },
                ],
                yearAndMonth: [
                    { required: true, message: '请输入年月', trigger: 'blur' },
                ],
                meettingTitle: [
                    { required: true, message: '请输入会议记录标题', trigger: 'blur' },
                ],
                meettingContent: [
                    { required: true, message: '请输入会议记要内容', trigger: 'blur' },
                ],
            }
        }
    },

    created() {
        this.getList();
        this.handleResize();
    },

    mounted() {
        window.addEventListener('resize', this.handleResize); // 添加监听器
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize); // 移除监听器
    },

    methods: {
        // 初始化数据
        getList() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            // 查询会议管理上传文件
            GetMeetingManagementFile(this.queueForm).then(res => {
                this.tableDate = res.data.list;
            }).finally(() => {
                loading.close();
            });
            // 委员会名称字典数据查询
            GetCommitteeNameDict().then(res => {
                this.committeeNameDict = res.data.getCommitteeNameDictRetVueModels;
            }).finally(() => {
                loading.close();
            });
        },

        // 历史记录按钮操作
        historical() {
            this.drawerOpenHis = true;
            this.getHistorical();
        },

        // 查询历史记录
        getHistorical() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            // 查询会议管理上传文件(历史记录数据)
            GetMeetingManagementFileHistorical(this.queueFormHis).then(res => {
                this.tableDateHis = res.data.list;
            }).finally(() => {
                loading.close();
            });
        },

        // 历史记录删除按钮操作
        delHis() {
            if (this.delForm.fileid === undefined) {
                this.showMessageA("请选择要删除的数据！");
            } else {
                let fileid = this.delForm.fileid;
                this.$modal.confirm(
                    '确认要删除委员会名称为: ' + this.delForm.id +
                    '日期为:' + this.delForm.yearandmonth +
                    '的数据吗?')
                    .then(function () {
                        return MeetingManagementDelHis(fileid);
                    })
                    .then(() => {
                        this.showMessageA("删除数据成功");
                        this.reset();
                        this.getHistorical();
                    })
                    .catch(() => {
                        this.reset();
                        this.showMessageA("删除数据失败");
                    });
            }
        },

        // 删除按钮操作
        del() {
            if (this.delForm.fileid === undefined) {
                this.showMessageA("请选择要删除的数据！");
            } else {
                let fileid = this.delForm.fileid;
                this.$modal.confirm(
                    '确认要删除委员会名称为: ' + this.delForm.id +
                    '日期为:' + this.delForm.yearandmonth +
                    '的数据吗?')
                    .then(function () {
                        return MeetingManagementDel(fileid);
                    })
                    .then(() => {
                        this.showMessageA("删除数据成功");
                        this.reset();
                        this.getList();
                    })
                    .catch(() => {
                        this.reset();
                        this.showMessageA("删除数据失败");
                    });
            }
        },

        // 新增按钮操作
        add() {
            this.dialogOpen = true;
        },

        // 新增弹框确定按钮操作
        saveUpload: function () {
            this.$refs["dataForm"].validate((valid) => {
                if (valid) {
                    // 判断是否上传文件
                    if (this.fileList1.length === 0) {
                        this.showMessageA("请上传附件1会议通知");
                    } else if (this.fileList2.length === 0) {
                        this.showMessageA("请上传附件2签到表");
                    } else if (this.fileList3.length === 0) {
                        this.showMessageA("请上传附件3照片");
                    } else {
                        let formData = new FormData();
                        if (this.fileList4.length === 0) {
                            formData.append('committeeName', this.queueFormFile.committeeName);
                            formData.append('yearAndMonth', this.queueFormFile.yearAndMonth);
                            formData.append('meettingTitle', this.queueFormFile.meettingTitle);
                            formData.append('meettingContent', this.queueFormFile.meettingContent);
                            formData.append('fileUrlA', this.fileList1[0].fileUrlA.split("wwwroot")[1]);
                            formData.append('fileNameA', this.fileList1[0].fileNameA);
                            formData.append('attachmentA', this.fileList1[0].attachmentA);
                            formData.append('fileUrlB', this.fileList2[0].fileUrlB.split("wwwroot")[1]);
                            formData.append('fileNameB', this.fileList2[0].fileNameB);
                            formData.append('attachmentB', this.fileList2[0].attachmentB);
                            formData.append('fileUrlC', this.fileList3[0].fileUrlC.split("wwwroot")[1]);
                            formData.append('fileNameC', this.fileList3[0].fileNameC);
                            formData.append('attachmentC', this.fileList3[0].attachmentC);
                        } else {
                            formData.append('committeeName', this.queueFormFile.committeeName);
                            formData.append('yearAndMonth', this.queueFormFile.yearAndMonth);
                            formData.append('meettingTitle', this.queueFormFile.meettingTitle);
                            formData.append('meettingContent', this.queueFormFile.meettingContent);
                            formData.append('fileUrlA', this.fileList1[0].fileUrlA.split("wwwroot")[1]);
                            formData.append('fileNameA', this.fileList1[0].fileNameA);
                            formData.append('attachmentA', this.fileList1[0].attachmentA);
                            formData.append('fileUrlB', this.fileList2[0].fileUrlB.split("wwwroot")[1]);
                            formData.append('fileNameB', this.fileList2[0].fileNameB);
                            formData.append('attachmentB', this.fileList2[0].attachmentB);
                            formData.append('fileUrlC', this.fileList3[0].fileUrlC.split("wwwroot")[1]);
                            formData.append('fileNameC', this.fileList3[0].fileNameC);
                            formData.append('attachmentC', this.fileList3[0].attachmentC);
                            formData.append('fileUrlD', this.fileList4[0].fileUrlD.split("wwwroot")[1]);
                            formData.append('fileNameD', this.fileList4[0].fileNameD);
                            formData.append('attachmentD', this.fileList4[0].attachmentD);
                        }
                        MeetingManagementFile(formData).then((response) => {
                            if (response.code == 200 && response.data === null) {
                                this.showMessageA("上传文件成功");
                                this.fileList1 = [];
                                this.fileList2 = [];
                                this.fileList3 = [];
                                this.fileList4 = [];
                                this.queueFormFile = {};
                                this.getList();
                                this.dialogOpen = false;
                            } else if (response.data === '无') {
                                this.showMessageA("已存在,请勿重复添加!!!");
                            } else {
                                this.showMessageA("上传文件失败,请联系信息科!!!");
                            }
                        }).catch((error) => {
                            // 处理错误
                        });
                    }
                }
            });
        },

        /** 文件上传功能限制 */
        // 文件上传-限制文件大小(公共)
        beforeUpload(file) {
            const isLt10M = file.size / 1024 < 50;
            if (!isLt10M) {
                this.$message.error('上传文件过大 ');
                return false;
            }
        },
        // 附件上传-上传数量提示 共选择了
        handleExceed(files, fileList) {
            this.$message.warning(`当前最多选择 10 个文件上传,超出文件最大数量限制`);
        },
        // 附件1上传-文件选取完文件触发事件
        onChange1(file, fileList1) {
            let formData = new FormData();
            formData.append('files[]', file.raw);
            formData.append('fileName', file.name);
            MeetingManagementFileOne(formData).then(res => {
                this.fileList1.push({
                    fileUrlA: res.data.path,
                    fileNameA: res.data.fileName,
                    attachmentA: '附件1会议通知'
                });
            });
        },
        // 附件2上传-文件选取完文件触发事件
        onChange2(file, fileList2) {
            let formData = new FormData()
            formData.append('files[]', file.raw);
            formData.append('fileName', file.name);
            MeetingManagementFileOne(formData).then(res => {
                this.fileList2.push({
                    fileUrlB: res.data.path,
                    fileNameB: res.data.fileName,
                    attachmentB: '附件2签到表'
                })
            });
        },
        // 附件3上传-文件选取完文件触发事件
        onChange3(file, fileList3) {
            let formData = new FormData()
            formData.append('files[]', file.raw);
            formData.append('fileName', file.name);
            MeetingManagementFileOne(formData).then(res => {
                this.fileList3.push({
                    fileUrlC: res.data.path,
                    fileNameC: res.data.fileName,
                    attachmentC: '附件3照片'
                })
            });
        },
        // 附件4上传-文件选取完文件触发事件
        onChange4(file, fileList4) {
            let formData = new FormData()
            formData.append('files[]', file.raw);
            formData.append('fileName', file.name);
            MeetingManagementFileOne(formData).then(res => {
                this.fileList4.push({
                    fileUrlD: res.data.path,
                    fileNameD: res.data.fileName,
                    attachmentD: '附件4课件'
                })
            });
        },
        handlePreview(file) { },

        // 附件查看操作
        handleCommand(command) {
            if (command.action === 'downloadAttachment1' || command.action === 'downloadAttachment2'
                || command.action === 'downloadAttachment3' || command.action === 'downloadAttachment4') {
                // 使用command对象中的信息来处理下载逻辑
                this.downloadAttachment(command.row, command.attachmentName);
            }
        },
        downloadAttachment(row, attachmentName) {
            // 接收附件名称和改行的数据
            if (attachmentName === '附件1会议通知' && row.fileurL1 !== null) {
                ++this.extraFunctionKey;
                this.drawerOpen = true;
                this.srcFileUrl = this.envUrl + row.fileurL1; // 获取文件路由
            } else if (attachmentName === '附件2签到表' && row.fileurL2 !== null) {
                ++this.extraFunctionKey;
                this.drawerOpen = true;
                this.srcFileUrl = this.envUrl + row.fileurL2; // 获取文件路由
            } else if (attachmentName === '附件3照片' && row.fileurL3 !== null) {
                ++this.extraFunctionKey;
                this.drawerOpen = true;
                this.srcFileUrl = this.envUrl + row.fileurL3; // 获取文件路由
            } else if (attachmentName === '附件4课件' && row.fileurL4 !== null) {
                ++this.extraFunctionKey;
                this.drawerOpen = true;
                this.srcFileUrl = this.envUrl + row.fileurL4; // 获取文件路由
            }
        },

        // 首页按钮操作
        indexOne() {
            this.$router.push('/singlePage/qualityCommittee/transfer')
        },

        // 双击弹框关闭提示消息
        handleCloseDra(done) {
            this.$confirm('确认关闭弹框吗')
                .then(_ => {
                    done();
                    this.reset();
                    this.getList();
                })
                .catch(_ => { });
        },

        // 历史记录弹框关闭提示
        handleCloseHis(done) {
            this.$confirm('确认关闭历史记录弹框吗')
                .then(_ => {
                    done();
                    this.reset();
                    this.getList();
                    this.getHistorical();
                })
                .catch(_ => { });
        },

        // 新增弹框关闭提示消息
        handleCloseDia(done) {
            this.$confirm('确认关闭新增弹框吗')
                .then(_ => {
                    done();
                    this.reset();
                    this.getList();
                })
                .catch(_ => { });
        },

        // 双击弹框关闭提示消息
        handleCloseDouble(done) {
            this.$confirm('确认关闭会议弹框吗')
                .then(_ => {
                    done();
                    this.reset();
                    this.getList();
                })
                .catch(_ => { });
        },

        // 重置按钮操作
        reset() {
            this.queueForm = {};
            this.queueFormFile = {};
            this.srcFileUrl = undefined;
        },

        // 单击某一行操作
        handleRowClick(row) {
            this.delForm.fileid = row.fileid;
            this.delForm.filename = row.filename;
            this.delForm.id = row.id;
            this.delForm.yearandmonth = row.yearandmonth;
        },

        // 双击某一行操作
        doubleSelectionChange(row) {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            // 根据FILEID查询会议管理上传文件详情数据
            GetMeetingManagementFileDetail(row).then(res => {
                if (res.data.list.length > 0) {
                    this.queueFormDouble = res.data.list[0];
                    this.queueFormDouble.safeUrl1 = this.envUrl + res.data.list[0].fileurL1;
                    this.queueFormDouble.safeUrl2 = this.envUrl + res.data.list[0].fileurL2;
                    this.queueFormDouble.safeUrl3 = this.envUrl + res.data.list[0].fileurL3;
                    this.queueFormDouble.safeUrl4 = this.envUrl + res.data.list[0].fileurL4;
                }
            }).finally(() => {
                loading.close();
            });
            ++this.extraFunctionKey;
            this.drawOpenDouble = true;
        },

        // 提示信息封装
        showMessageA(message) {
            const h = this.$createElement;
            this.$notify({
                title: '提示信息！',
                message: h('i', { style: 'color: teal' }, message)
            });
        },

        // 自定义高度变化更新高度
        handleResize() {
            this.tableHeight = window.innerHeight; // 更新高度数据
        },
    }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.my-table {
    ::v-deep.el-table--medium .el-table__cell {
        padding: 0;
    }

    ::v-deep.el-table .el-table__header-wrapper th,
    .el-table .el-table__fixed-header-wrapper th {
        word-break: break-word;
        background-color: #4f617238;
        color: #303133;
        height: 30px;
        font-size: 10px;
    }

    ::v-deep.el-table th.el-table__cell>.cell {
        padding: 0;
    }

    ::v-deep.el-table--border .el-table__cell:first-child .cell {
        padding: 0;
    }

    ::v-deep.el-button+.el-button {
        margin-left: 2px;
    }

    ::v-deep.el-table .cell {
        padding: 1px;
    }

    // 滚动条的滑块
    ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: rgb(13, 192, 132);
        border-radius: 1px;
    }
}

.scroll-container {
    height: 550px;
    /* 设置容器的高度 */
}

.unit_input {
    border: none;
    border-bottom: 0px solid rgb(0, 0, 0);
    outline: none;
    width: 160px;
    font-size: 15px;
    font-weight: 100px;
    margin-left: 3px;
}
</style>

<style>
.el-upload-dragger {
    width: 193px;
    height: 136px;
}

h1 {
    /* 标题居中对齐 */
    text-align: center;
}

h3 {
    /* 内容首行缩进两个字符 */
    text-indent: 2em;
}
</style>