import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken,setToken,setAccessToken } from '@/utils/auth'
import { isRelogin } from '@/utils/request'

NProgress.configure({ showSpinner: false })

const whiteList = [
  '/login',
  '/register',
  '/appointmentRegister',
  '/reservationCenter',
  '/appointmentOutpatient',
  '/outpPatologyApply',
  '/ward-appoint',
  '/queueItemDict',
  '/reservationCenterNurseHospital',
  '/oneBed',
  '/singlePage/inspectionResult/register',
  '/singlePage/maternalDataReporting/inquire',
  '/singlePage/emergencyClinic/dispatchRoomPatient',
  '/singlePage/contagion/reportCardReview',
  '/singlePage/positiveReport/inquire',
  '/singlePage/tuberculosisStatement/inquire',
  '/singlePage/parasite/parasiteIndex',
  '/singlePage/pdfSelect',
  '/singlePage/tumorReportCard/index',
  '/singlePage/newbornStatistics/index',
  '/singlePage/refundBank/index',
  '/singlePage/drugEntry/inquire',
  '/singlePage/drugStatement/inquires',
  '/singlePage/waterElectricityFee/index',
  '/singlePage/diagnosedInfo/index',
  '/test',
  '/exchangePlatform/index',
  '/oa/lkoa/newsAudit/index',
  '/oa/lkoa/newsAudit/outerNet',
  '/singlePage/qualityCommittee/transfer',
  '/singlePage/qualityCommittee/systemManagement',
  '/singlePage/qualityCommittee/problemManagement',
  '/singlePage/qualityCommittee/meetingManagement',
  '/singlePage/qualityCommittee/improvementProject',
  '/singlePage/qualityCommittee/frameworkManagement',
  '/singlePage/drugUsageDetails/index',
  '/singlePage/test/index',
  '/singlePage/nurseRefundConfigCenter/index',
  '/singlePage/labTestEmergencyDept/index',
  '/singlePage/queueInfo',
  '/singlePage/examAppointsList',
]

router.beforeEach((to, from, next) => {
  NProgress.start()
  const token = sessionStorage.getItem('token');
  const AccessToken = sessionStorage.getItem('Access-Token');
  if (token) {
    setToken(token);
  }
  if (AccessToken){
    setAccessToken(AccessToken);
  }
  if (getToken()) {
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      if (store.getters.roles.length === 0) {
        isRelogin.show = true
        // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => {
          isRelogin.show = false
          store.dispatch('GenerateRoutes').then(accessRoutes => {
            // 根据roles权限生成可访问的路由表
            router.addRoutes(accessRoutes) // 动态添加可访问路由表
            next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          })
        }).catch(err => {
          store.dispatch('LogOut').then(() => {
            Message.error(err)
            next({ path: '/' })
          })
        })
      } else {
        next()
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${encodeURIComponent(to.fullPath)}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
