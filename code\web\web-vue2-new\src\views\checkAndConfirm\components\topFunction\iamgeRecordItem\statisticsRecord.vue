<template>
  <div class="statistics-master">
    <div class="statistics-form">
      <el-form ref="form" :model="queueForm" :inline="true">
        <el-form-item label="查询时间:" label-width="85px">
          <div class="form-flex">
            <el-date-picker v-model="queueForm.beginDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
            <div class="date-f">-</div>
            <el-date-picker v-model="queueForm.endDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="formSelectClick">提取</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="statistics-item">
      <div class="statistics-title">开单药品统计</div>
      <div class="statistics-table">
        <el-table :data="tableData" border style="width: 65%" max-height="600" highlight-current-row show-summary>
          <el-table-column align="center" prop="visiT_ID" label="门诊/住院" width="100">
            <template slot-scope="scope">
              <div>{{scope.row.visiT_ID === 0?"门诊":"住院"}}</div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="druG_NAME" label="药品名称" width="400"></el-table-column>
          <el-table-column align="center" prop="iteM_SPEC" label="规格" width="300"></el-table-column>
          <el-table-column align="center" prop="counT(*)" label="数量" width="80"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { GetStatisticsInitializeMsg,} from "@/api/checkAndConfirm/imageBillingRecord"
export default {
  name: 'statisticsRecord',
  props: ['queueForm'],
  components: {},
  data() {
    return {
      tableData:[],
    }
  },
  created() {
    this.formSelectClick();
  },
  mounted() {
  },
  methods: {
    formSelectClick(){
      this.tableData = [];
      const loading = this.$loading({
        lock: true,
        text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      GetStatisticsInitializeMsg(this.queueForm.beginDate,this.queueForm.endDate).then(res => {
        if (res.code === 200){
          this.tableData = res.data;
        }
      }).finally(() => {
        loading.close();
      })
    },
  }
}
</script>

<style scoped lang="scss">
.statistics-master{
  .statistics-form{
    margin-top: 2px;
    margin-left: 10px;
    .form-flex {
      display: flex;
    }

    .date-f {
      padding: 0 5px;
    }
    ::v-deep.el-form-item {
      margin-bottom: 0;
    }
    ::v-deep.el-date-editor.el-input, .el-date-editor.el-input__inner {
      width: 135px;
    }
  }
  .statistics-item{
    .statistics-title{
      height: 30px;
      border: 1px solid #00a19b;
      background-color: #185F7D;
      color: #FFFFFF;
      font-size: 20px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }
    .statistics-table{
      ::v-deep.el-table--medium .el-table__cell {
        padding: 3px 0;
      }
      ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
        height: 30px;
        font-size: 14px;
      }
      ::v-deep.el-table th.el-table__cell > .cell {
        padding-left: 2px;
        padding-right: 2px;
      }
      ::v-deep.el-table .cell {
        padding-left: 2px;
        padding-right: 2px;
      }
      ::-webkit-scrollbar {
        width: 11px;
        height: 11px;
      }
      ::-webkit-scrollbar-thumb {
        background-color: #6bcaaf;
        border-radius: 10px;
      }
      ::v-deep.el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
        background-color: #1890FF;
        color: #FFFFFF;
      }
    }
  }
}
</style>
