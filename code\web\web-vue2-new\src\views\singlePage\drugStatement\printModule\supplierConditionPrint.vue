<template>
  <div>
    <el-dialog title="打印预览" top="2vh" :visible.sync="dialogVisible" width="85%" :show-close="true"
               :close-on-press-escape="true" :close-on-click-modal="true"
    >
      <print-components @page-size="totalSizeClick"
                        :print-data="printDatas"
                        :key="printKey"
                        :print-data-list="printDatas.printDataList"
                        :table-title="printDatas.tableTitle"
                        :page-html="printDatas.pageHtml"
                        :total-page="printDatas.totalPage"
                        :total-size="printDatas.totalSize"
                        :merge-data="printDatas.mergeData"
                        :is-merge="printDatas.isMerge"
                        :title-html="printDatas.titleHtml"
                        :bottom-html="printDatas.bottomHtml"
                        :merge-size="printDatas.mergeSize">
      </print-components>
    </el-dialog>
  </div>
</template>

<script>
/**
 * 按医药公司供货查询
 */
import { getDrugStatementTable } from '@/api/singlePage/drugStatement'
import PrintComponents from '../../../../components/print/printComponents.vue'

export default {
  name: 'supplierConditionPrint',
  props: {},
  components: { PrintComponents },
  data() {
    return {
      printDatas: {
        printDataList: [],//打印数据
        tableTitle: [{
          key: 'SUPPLIER',
          title: '公司名称',
          width: 'width: 40%'
        }, {
          key: '西药库',
          title: '西药库',
          width: 'width: 13%'
        }, {
          key: '中成药库',
          title: '中药库',
          width: 'width: 13%'
        }, {
          key: '草药库',
          title: '草药库',
          width: 'width: 13%'
        }, {
          key: '试剂库',
          title: '试剂库',
          width: 'width: 13%'
        }, {
          key: '合计',
          title: '合计',
          width: 'width: 16%'
        }],//标题数据
        pageHtml: '',//是否page同行
        totalPage: 0,//总行数
        totalSize: 17,//打印页每页显示条数
        mergeData: [],//合并数据
        isMerge: true,//是否合并
        titleHtml: '医药公司供货情况查询',//每页title文字（需要带样式）
        bottomHtml: '',//每页底部文字（需要带样式）
        mergeSize: 2,
      },
      printKey: 0,
      dialogVisible: false,
      xyk: [],
      zyk: [],
      cyk: [],
      sjk: [],
      hj: [],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    totalSizeClick(data) {
      ++this.printKey;
      this.printDatas.totalSize = data
      this.chengeRows(data);
    },
    printData(data) {
      getDrugStatementTable(data).then(res => {
        if (res.code === 200) {
          this.tableData = res.data
          this.chengeRows(this.printDatas.totalSize)
          this.dialogVisible = true;
        }
      })
    },
    chengeRows(val) {
      let tableData = this.sliceArr(this.tableData, val) //按照行数将表格数据切割为二维数组
      this.printDatas.printDataList = tableData
      this.printDatas.totalPage = tableData.length //这里拿到的就是总页面数
      tableData.forEach((item, index) => {
        let xyk = 0
        let zyk = 0
        let cyk = 0
        let sjk = 0
        let hj = 0
        item.forEach((i, j) => {
          xyk += i.西药库 //这个就是要计算的列的参数名合计，并且这里是按照每页计算的
          zyk += i.中成药库
          cyk += i.草药库
          sjk += i.试剂库
          hj += i.合计
        })
        // this.$nextTick(()=>{
        //由于vue2监听不到数组的变化，所以这里我直接用$set直接改变数组的值，达到页面数据响应式刷新的结果
        this.$set(this.xyk, index, xyk)
        this.$set(this.zyk, index, zyk)
        this.$set(this.cyk, index, cyk)
        this.$set(this.sjk, index, sjk)
        this.$set(this.hj, index, hj)
        this.$set(this.printDatas.printDataList, index, item)
        // })
      })
      this.printDatas.mergeData = [this.xyk, this.zyk, this.cyk, this.sjk, this.hj]
    },
    sliceArr(array, size) {
      if (array.length === 0) {
        return []
      }
      const numOfChunks = Math.ceil(array.length / size)
      const chunks = new Array(numOfChunks)
      for (let i = 0, j = 0; i < numOfChunks; i++) {
        chunks[i] = array.slice(j, j + size)
        j += size
      }
      return chunks
    }
  }
}
</script>

<style scoped lang="scss">
#supplierConditionPrint {
  margin: 10px 10px 10px 20px;
  font-size: 1.6vw !important;
}

.my-table {
  margin-top: 20px;
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

.my-table th,
.my-table td {
  padding: 6px;
  text-align: center;
}

.my-table th {
  border: 1px solid #000;
  font-weight: bold;
}

.my-table td {
  border: 1px solid #000;
}

.ml-2 {
  margin-left: 8px;
}

.mt-2 {
  margin-top: 8px;
}

.event-button {
  position: fixed;
  /* 使用固定定位 */
  bottom: 600px;
  /* 距离底部20像素 */
  right: 200px;
  width: 80px;
  height: 80px;
  font-size: 40px !important;
  border-radius: 50% !important;

  ::v-deep.el-button--medium.is-circle {
    padding: 40px;
  }

  ::v-deep.el-button--medium {
    font-size: 30px;
  }
}
</style>
