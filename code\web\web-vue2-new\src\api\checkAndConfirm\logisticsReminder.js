import request from "@/utils/request";

/**
 * 物流提醒按钮
 * in_c_data In Varchar2,      --入参json字符串
 *      in_c_data参数格式：
 *    {
 *     "exam_no": "12345678",
 *     "from_dept": "0203",
 *     "to_dept": "04020201",
 *     "name": "张三",
 * 	    "patient_id": "1122334",
 * 	    "device": "胸部正位",
 * 	    "user_name": "1397"
 * }
 * out_v_msg Out Varchar2  --返回json字符串
 */
export function GetLogisticsReminder(data) {
  return request({
    url: "/LogisticsReminderAndDeleteButton/GetLogisticsReminder",
    method: "post",
    data: data,
  });
}

/**
 * 删除按钮
 * @param memo 数据状态   appoints-数据在exam_appints，master-数据在exam_master中
 * @param examNo 申请号
 * @returns {*}
 */
export function DeleteButton(data) {
  return request({
    // url: "/LogisticsReminderAndDeleteButton/DeleteButton?memo=" + memo + "&examNo=" + examNo,
    url: "/LogisticsReminderAndDeleteButton/DeleteButton",
    method: "delete",
    data: data
  });
}
