<template>
  <div>
    <el-select v-model="myData" multiple placeholder="请选择" disabled @change="dictCharge">
      <el-option
        v-for="item in treeData"
        :key="item.depT_CODE"
        :label="item.depT_NAME"
        :value="item.depT_CODE"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import {GetMedicalLaboratoryDeptTree} from "@/api/checkAndConfirm/checkCommon"
export default {
  name: 'medicalDeptTree',
  props: [],
  components: {},
  data() {
    return {
      treeData:[],
      myData:[],
    }
  },
  created() {
    this.getMedicalLaboratoryDeptTree()
    // this.dictMonitor();
    this.dictCreate();
  },
  mounted() {
  },
  methods: {
    dictCreate(){
      this.myData = this.$store.getters.examAuthDeptCodes;
    },
    // dictMonitor(){
    //   this.myData = JSON.parse(localStorage.getItem("deptArray"));
    //   this.deptAssignment();
    // },
    dictCharge(){
      localStorage.setItem('deptArray', JSON.stringify(this.myData));
      this.deptAssignment();
    },
    getMedicalLaboratoryDeptTree(){
      GetMedicalLaboratoryDeptTree().then(res => {
        this.treeData = res.data;
      })
    },
    deptAssignment(){
      this.$store.getters.patient.deptCode = this.myData
    },
  }
}
</script>

<style scoped lang="scss">

</style>
