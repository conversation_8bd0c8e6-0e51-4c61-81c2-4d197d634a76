import request from "@/utils/request";

export function GetCheckConfirmTable(data) {
  return request({
    url: "/ImageBillingRecord/GetOutpInitializeMsg",
    method: "get",
    params: data,
  });
}


export function GetOutpChargeScheduleMsg(rcptNo) {
  return request({
    url: "/ImageBillingRecord/GetOutpChargeScheduleMsg?rcptNo=" + rcptNo,
    method: "get",
  });
}


export function GetOutpDispensingDetailsMsg(adtDay,aiNo) {
  return request({
    url: "/ImageBillingRecord/GetOutpDispensingDetailsMsg?adtDay=" + adtDay + "&aiNo=" +aiNo,
    method: "get",
  });
}

export function GetinInitializeMsg(patientId) {
  return request({
    url: "/ImageBillingRecord/GetinInitializeMsg?patientId=" + patientId,
    method: "get",
  });
}

export function GetStatisticsInitializeMsg(beginDate,endDate) {
  return request({
    url: "/ImageBillingRecord/GetStatisticsInitializeMsg?beginDate=" + beginDate + "&endDate=" +endDate,
    method: "get",
  });
}
