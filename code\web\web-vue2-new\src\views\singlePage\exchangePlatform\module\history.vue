<template>
  <div class="single-master">
    <div class="single-title">发送记录</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="开始时间:">
              <el-date-picker
                @change="getList"
                v-model="queueForm.beginDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                :clearable="false"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker
                @change="getList"
                v-model="queueForm.endDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                :clearable="false"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div class="element-table">
          <el-table v-loading="loading" :data="tableData" style="width: 100%" border :height="(tableHeight - 185)">
            <el-table-column type="index" width="50" align="center"></el-table-column>
            <el-table-column prop="senderTitle" align="center" label="标题"></el-table-column>
            <el-table-column prop="senderName" align="center" label="接收人" width="400">
              <template slot-scope="scope">
                {{scope.row.sends.map(x => x.recipientName).join(",")}}
              </template>
            </el-table-column>
            <el-table-column prop="senderDate" align="center" label="发送日期" width="90"></el-table-column>
            <el-table-column prop="memo" align="center" label="备注" width="160"></el-table-column>
            <el-table-column prop="FIRM_ID" align="center" label="发送信息" width="120">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-s-order" @click="newsOpen(scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="element-drawer">
          <el-drawer
            title="文件信息"
            :visible.sync="drawer"
            size="60%"
          >
            <div style="display: flex;height: 100%;">
              <div style="width: 30%;border-right: 1px solid #DFECFD;height: 100%;">
                <el-scrollbar style="height: 94.7%;overflow-x: hidden;margin-top: 2px;">
                  <div style="height: 30px;padding: 0 3px;" v-for="(item,index) in rowData.sends" :key="index">
                    <div style="display: flex;padding: 2px 5px;border: 1px solid #DFECFD">
                      <div style="display: flex;align-items: center;font-size: 16px;color: #00afff">
                        {{item.recipientName}}
                      </div>
                      <div style="width: 20%"><el-button type="text" size="mini" style="margin-left: 10%" icon="el-icon-delete-solid"
                                                         @click="deleteSendButton(item)">
                        删除
                      </el-button></div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
              <div style="width: 70%;height: 100%;">
                <el-scrollbar style="height: 94.7%;overflow-x: hidden;margin-top: 2px;">
                  <div style="min-height: 30px;padding: 0 3px;" v-for="(item,index) in rowData.files" :key="index">
                    <div style="display: flex;padding: 2px 5px;border: 1px solid #DFECFD">
                      <div style="display: flex;align-items: center;font-size: 18px;color: #00afff">
                        {{item.fileName}}
                      </div>
                      <div style="display: flex;align-items: center;font-size: 12px;padding: 0 20px;color: #00afff;margin-top: 5px;">
                        {{(item.fileSize / 1024 / 1024) > 1? (item.fileSize / 1024 / 1024).toFixed(2) + 'MB' : (item.fileSize / 1024).toFixed(2) + 'KB'  }}
                      </div>
                      <div style="width: 30%"><el-button type="text" size="mini" style="margin-left: 10%" icon="el-icon-s-promotion"
                                                         @click="downloadButton(item)">
                        下载
                      </el-button>
                        <el-button type="text" size="mini" icon="el-icon-delete-solid"
                                   @click="deleteFileButton(item)">
                          删除
                        </el-button></div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>

          </el-drawer>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getSendList,deleteSendById,deleteFileById} from '@/api/singlePage/exchangePlatform'
export default {
  name: 'history',
  props: ['employeeId','tableHeight'],
  components: {},
  data() {
    return {
      queueForm: {
        beginDate: this.formatDateMonth(new Date()),
        endDate: this.formatDate(new Date()),
        userId: this.employeeId,
      },
      loading: true,
      drawer: false,
      tableData: [],
      rowData: {},
    }
  },
  created() {
    this.getList();
  },
  methods: {
    deleteFileButton(row){
      deleteFileById(row.id).then(res => {
        if (res.code === 200){
          this.$message.success("删除成功!!!");
          let data = [];
          this.rowData.files.forEach(x => {
            if (x.id !== row.id){
              data.push(x)
            }
          })
          this.rowData.files = data;
        }
      })
    },
    deleteSendButton(row){
      deleteSendById(row.id).then(res => {
        if (res.code === 200){
          this.$message.success("删除成功!!!");
          let data = [];
          this.rowData.sends.forEach(x => {
            if (x.id !== row.id){
              data.push(x)
            }
          })
          this.rowData.sends = data;
          this.getList();
        }
      })
    },
    downloadButton(row){
      window.open(row.filePath, '_blank')
    },
    newsOpen(row){
      this.rowData = row;
      this.drawer = true;
    },
    getList(){
      this.loading = true;
      getSendList(this.queueForm).then(res => {
        this.tableData = res.data;
        this.loading = false;
      })
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    formatDateMonth(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      return `${year}-${month}-01`
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../../assets/styles/singlePage2";
</style>
