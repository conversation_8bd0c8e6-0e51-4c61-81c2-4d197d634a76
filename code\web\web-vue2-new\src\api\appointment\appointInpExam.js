/*
 * FilePath     : \src\api\appointment\appointInpExam.js
 * Author       : LYF
 * Date         : 2024-07-14 14:31
 * LastEditors  : LYF
 * LastEditTime : 2024-07-21 10:20
 * Description  :
 * CodeIterationRecord:
 */
import request from "@/utils/request";

//获取住院患者检查列表
export function GetPatExamInfo(query) {
  return request({
    url: "/appointInpExam/GetPatExamInfo",
    method: "get",
    params: query,
  });
}

//获取住院患者当前检查预约信息
export function GetPatExamAppointInfo(query) {
  return request({
    url: "/appointInpExam/GetPatExamAppointInfo",
    method: "get",
    params: query,
  });
}

//获取叫号窗口号源汇总
export function GetExamAppointDetailInfo(query) {
  return request({
    url: "/appointInpExam/GetExamAppointDetailInfo",
    method: "get",
    params: query,
  });
}

//住院检查患者检查时间预约
export function patAppointmentExamTime(data) {
  return request({
    url: "/appointInpExam/PatAppointmentExamTime",
    method: "post",
    data: data,
  });
}


//判断当前患者是否有申请信息
export function GetPatApplyExamInfo(query) {
  return request({
    url: "/appointInpExam/GetPatApplyExamInfo",
    method: "get",
    params: query,
  });
}

//获取病区患者检查申请信息
export function GetWardAppointInfo(query) {
  return request({
    url: "/appointInpExam/GetWardAppointInfo",
    method: "get",
    params: query,
  });
}