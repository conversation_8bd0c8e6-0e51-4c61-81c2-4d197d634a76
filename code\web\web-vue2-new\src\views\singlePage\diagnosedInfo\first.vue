<template>
  <div class="single-master">
  
    <div class="element-master">
      <div class="my-table">
        <el-table :data="firstList" style="width: 100%" border :height="tableHeight - 550" highlight-current-row>
          <el-table-column prop="visiT_DATE" align="center" label="就诊日期" :show-overflow-tooltip="true" />
          <el-table-column prop="seriaL_NO" align="center" label="处方号" :show-overflow-tooltip="true">
  
          </el-table-column>
          <el-table-column prop="druG_NAME" align="center" label="药名" :show-overflow-tooltip="true" />
          <el-table-column prop="druG_SPEC" align="center" label="规格" :show-overflow-tooltip="true" />
          <el-table-column prop="firM_ID" align="center" label="厂家" :show-overflow-tooltip="true" />
          <el-table-column prop="amount" align="center" label="数量">
  
          </el-table-column>
          <el-table-column prop="units" align="center" label="单位">
  
          </el-table-column> <el-table-column prop="druG_SPEC" align="center" label="剂量">
  
          </el-table-column> <el-table-column prop="dosage" align="center" label="用量">
  
          </el-table-column> <el-table-column prop="dosagE_UNITS" align="center" label="用量单位">
  
          </el-table-column> <el-table-column prop="administration" align="center" label="途径"
            :show-overflow-tooltip="true">
  
          </el-table-column> <el-table-column prop="perforM_RESULT" align="center" label="皮试结果">
  
          </el-table-column> <el-table-column prop="frequency" align="center" label="频次">
  
          </el-table-column> <el-table-column prop="abidance" align="center" label="用药天数">
  
          </el-table-column><el-table-column prop="charges" align="center" label="实收">
  
          </el-table-column><el-table-column prop="" align="center" label="药局">
  
          </el-table-column><el-table-column prop="" align="center" label="医生说明" :show-overflow-tooltip="true">
  
          </el-table-column><el-table-column prop="" align="center" label="取药属性" :show-overflow-tooltip="true">
  
          </el-table-column><el-table-column prop="" align="center" label="收费" :show-overflow-tooltip="true">
  
          </el-table-column><el-table-column prop="ordeR_DATE" align="center" label="开单时间" :show-overflow-tooltip="true"
            width="80px">
  
          </el-table-column><el-table-column prop="perforM_NURSE_USER" align="center" label="皮试执行人"
            :show-overflow-tooltip="true">
  
          </el-table-column><el-table-column prop="druG_CODE" align="center" label="批示药品批号" :show-overflow-tooltip="true">
  
          </el-table-column><el-table-column prop="doC_NAME" align="center" label="批示执行时间" :show-overflow-tooltip="true">
  
          </el-table-column>
  
        </el-table>
  
      </div>
    </div>
  </div>
</template>

<script>
import {
  GetParasiteList,
  GetParasiteByTestNo,
  GetParasiteByDocName
} from "@/api/singlePage/parasite";
import {
  GetDiagnosedAndBase,
  GetPrescriptionList,
  GetInspectList,
  GetDisposeList,
  GetBloodSugarList,
  GetAssayList
} from "@/api/singlePage/diagnosedInfo";
export default {
  name: 'parasiteIndex',
  props:
  {
    firstList: Array,
  },

  data() {
    return {
      currentTabIndex: '0',
      activeName: 'first',
      queueForm: {
        pageNum: 1,
        pageSize: 10,
        beginDate: this.formatDate(new Date()),
        endDate: this.formatDate(new Date()),
        // beginDate: '2024-01-01',
        // endDate: '2024-01-30'
      },
      baseInfo: [],
      tableDate: [],
      total: 0,
      tableDateTestNo: [],
      tableDateDocName: [],
      titleTestNo: "检验详情信息",
      titleDocName: "医生详情信息",
      testNoDialog: false,
      docNameDialog: false
    }
  },

  created() {
    //this.getList();
    this.handleResize();
  },

  mounted() {
    window.addEventListener('resize', this.handleResize); // 添加监听器
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize); // 移除监听器
  },

  methods: {
    //切换页签
    handleClick(tab, event) {
      this.currentTabIndex = tab.index
      console.log(this.currentTabIndex);

    },
    // 初始化数据
    getList() {
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力导出中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      GetDiagnosedAndBase(this.queueForm).then(res => {
        this.baseInfo = res.data.baseInfo;
        this.tableDate = res.data.diagnosed;
      }).finally(() => {
        loading.close();
      });
    },

    // 点击某一行检验号操作
    opentestNo(row) {
      this.testNoDialog = true;
      GetParasiteByTestNo(row).then(res => {
        this.tableDateTestNo = res.data.listTestNo;
      });
    },

    // 点击某一行医生姓名操作
    opendocName(row) {
      this.docNameDialog = true;
      GetParasiteByDocName(row).then(res => {
        this.tableDateDocName = res.data.listDocName;
      });
    },

    // 序号翻页递增
    indexMethod(index) {
      let nowPage = this.queueForm.pageNum; //当前第几页，根据组件取值即可
      let nowLimit = this.queueForm.pageSize; //当前每页显示几条，根据组件取值即可
      return index + 1 + (nowPage - 1) * nowLimit; // 这里可以理解成一个公式
    },

    // 默认当前时间
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 自定义高度变化更新高度
    handleResize() {
      this.tableHeight = window.innerHeight; // 更新高度数据
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.text-click {
  color: #00afff;
}

:hover.text-click {
  cursor: pointer;
  border-bottom: 1px solid #00afff;
}

.my-table {
  ::v-deep.el-table--medium .el-table__cell {
    padding: 0;
  }

  ::v-deep.el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    word-break: break-word;
    background-color: #f8f8f9;
    color: #515a6e;
    height: 30px;
    font-size: 14px;
  }

  ::v-deep.el-table th.el-table__cell>.cell {
    padding: 0;
  }

  ::v-deep.el-table--border .el-table__cell:first-child .cell {
    padding: 0;
  }

  ::v-deep.el-button+.el-button {
    margin-left: 2px;
  }

  ::v-deep.el-table .cell {
    padding: 1px;
  }

  /* ---el-table滚动条公共样式--- */
  // 滚动条的宽度
  ::v-deep.el-table__body-wrapper::-webkit-scrollbar {
    width: 10px; // 横向滚动条
    height: 10px; // 纵向滚动条 必写
  }

  // 滚动条的滑块
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }
}
</style>