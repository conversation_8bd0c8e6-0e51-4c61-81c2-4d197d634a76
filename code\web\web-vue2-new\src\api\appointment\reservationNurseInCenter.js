import request from "@/utils/request";

export function GetReservationCenterInfo(patientId,visitId,nurseNo) {
  return request({
    url: "/ReservationNurseHospitalCenter/ReservationNurseHospitalCenter?patientId=" + patientId + '&visitId=' +visitId+ '&nurseNo=' +nurseNo,
    method: "get",
  });
}

export function GetReservationCenterInfoTwo(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetReservationCenterInfoTwo",
    method: "post",
    data: data,
  });
}

export function GetUnitOrWindowTree(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetUnitOrWindowTree",
    method: "post",
    data: data,
  });
}

export function GetUnitAndWindowTree(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetUnitAndWindowTree",
    method: "post",
    data:data,
  });
}

export function GetWindowTimeMessage(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetWindowTimeMessage",
    method: "post",
    data:data,
  });
}

export function GetCallQueueList(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetCallQueueList",
    method: "post",
    data:data,
  });
}

export function GetCallQueueMessage(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetCallQueueMessage",
    method: "post",
    data:data,
  });
}

export function DeleteCallQueueByQueueId(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/DeleteCallQueueByQueueId",
    method: "put",
    data:data,
  });
}

export function CallTransfer(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/CallTransfer",
    method: "put",
    data:data,
  });
}

export function RecoverCallQueue(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/RecoverCallQueue",
    method: "put",
    data:data,
  });
}

export function UpdateMark(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/UpdateMark",
    method: "put",
    data:data,
  });
}

export function GetAppointmentTimeList(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetAppointmentTimeList",
    method: "post",
    data: data,
  });
}

export function TimeAppointment(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/TimeAppointment",
    method: "post",
    data: data,
  });
}

export function GetTimeDispose(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetTimeDispose?date=" + data,
    method: "get",
  });
}

export function GetAppointmentListFormUnitTree(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetAppointmentListFormUnitTree?deptCode=" + data,
    method: "get",
  });
}
export function GetAppointmentListFormWindowTree(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetAppointmentListFormWindowTree?unitId=" + data,
    method: "get",
  });
}

export function GetAppointmentList(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetAppointmentList",
    method: "post",
    data: data,
  });
}

//叫号登记相关
export function GetManualInputFormUnitTree(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetManualInputFormUnitTree?deptCode=" + data,
    method: "get",
  });
}
export function GetManualInputFormFormWindowTree(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetManualInputFormFormWindowTree?unitId=" + data,
    method: "get",
  });
}
export function GetManualInputFormPatient(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetManualInputFormPatient?patientId=" + data,
    method: "get",
  });
}
export function SaveManualInputForm(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/SaveManualInputForm",
    method: "post",
    data: data,
  });
}
//住院通知相关
export function GetBeHospitalizedInformMessage(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetBeHospitalizedInformMessage",
    method: "post",
    data:data,
  });
}
export function GetBeHospitalizedInformTemplate(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/GetBeHospitalizedInformTemplate",
    method: "get",
  });
}
export function SendBeHospitalizedInform(data) {
  return request({
    url: "/ReservationNurseHospitalCenter/SendBeHospitalizedInform",
    method: "post",
    data:data,
  });
}
