<template>
  <div :class="{ rightHomeOne: rightOne, rightHomeTwo: rightTwo, rightHomeThree: rightThree }">
    <div class="right-master">
      <div>
        <el-button v-if="buttonStatus.deleteStatus" class="right-margin" @click="deleteClick">删除</el-button>
        <el-button v-else class="right-margin" disabled>删除</el-button>
      </div>

<!--      <div>-->
<!--        <el-button v-if="buttonStatus.projectStatus" class="right-margin" @click="projectClick">项目</el-button>-->
<!--        <el-button v-else class="right-margin" disabled>项目</el-button>-->
<!--      </div>-->
      <div>
        <el-button v-if="buttonStatus.saveStatus" class="right-margin" @click="tableSaveClick">保存</el-button>
        <el-button v-else class="right-margin" disabled>保存</el-button>
      </div>
      <div>
        <el-button v-if="buttonStatus.costBreakdownStatus" class="right-margin" @click="costInquiryClick">费用查询
        </el-button>
        <el-button v-else class="right-margin" disabled>费用查询</el-button>
      </div>
      <div>
        <el-button v-if="buttonStatus.costBreakdownStatus" class="right-margin" @click="sceneCollectFeeClick">现场收费
        </el-button>
        <el-button v-else class="right-margin" disabled>现场收费</el-button>
      </div>
      <div>
        <el-button v-if="buttonStatus.printStatus" class="right-margin" @click="printClick">打印</el-button>
        <el-button v-else class="right-margin" disabled>打印</el-button>
      </div>
      <div>
        <el-button v-if="buttonStatus.printStatus" class="right-margin" @click="printBarCodeClick">打印条码</el-button>
        <el-button v-else class="right-margin" disabled>打印条码</el-button>
      </div>
      <div style="margin-top: 10px;">
        <el-button class="right-margin" @click="queueClick">队列</el-button>
        <!--        <el-button v-else class="right-margin" disabled>队列</el-button>-->
      </div>
      <div>
        <el-button v-if="buttonStatus.appointmentStatus" class="right-margin" @click="queueSelectClick">队列搜索
        </el-button>
        <el-button v-else class="right-margin" disabled>队列搜索</el-button>
      </div>
      <!--      <el-button v-if="buttonStatus.printStatus" class="right-margin">打印条码</el-button>-->
      <div>
        <el-button v-if="buttonStatus.appointmentStatus" class="right-margin" @click="rowAppointment">预约中心
        </el-button>
        <el-button v-else class="right-margin" disabled>预约中心</el-button>
      </div>
      <div>
        <el-button class="right-margin" @click="examArrangeClick">检查安排</el-button>
        <!--        <el-button v-else class="right-margin" disabled>队列</el-button>-->
      </div>
      <div v-if="this.$store.getters.examAuthDeptCodes && this.$store.getters.examAuthDeptCodes.includes('020901')">
        <el-button v-if="buttonStatus.csExamReportStatus" class="right-margin" @click="csExamReportClick">检查报告</el-button>
        <el-button v-else class="right-margin" disabled>检查报告</el-button>
      </div>
      <div style="height: 10px"></div>
      <div>
        <el-button v-if="buttonStatus.noExecuteStatus" class="right-margin" @click="noProjectClick">未执行项目
        </el-button>
        <el-button v-else class="right-margin" disabled>未执行项目</el-button>
      </div>
      <div style="height: 10px"></div>
      <el-button v-if="buttonStatus.costBreakdownStatus" class="right-margin" @click="requestCodeClick">申请单</el-button>
      <div>
        <el-button v-if="buttonStatus.caseHistoryStatusOne" class="right-margin" @click="medicalRecordClick">门诊病历
        </el-button>
        <el-button v-else-if="buttonStatus.caseHistoryStatusTwo" class="right-margin" @click="medicalRecordClick">
          住院病历
        </el-button>
        <el-button v-else class="right-margin" disabled>病历</el-button>
      </div>
      <div>
        <el-button v-if="buttonStatus.returnPremiumStatus" class="right-margin" @click="returnPremiumClick">退费
        </el-button>
        <el-button v-else class="right-margin" disabled>退费</el-button>
      </div>
      <!--      <div>-->
      <!--        <el-button v-if="buttonStatus.logisticsStatus" class="right-margin" @click="logisticsReminderClick">物流提醒</el-button>-->
      <!--        <el-button v-else class="right-margin" disabled>物流提醒</el-button>-->
      <!--      </div>-->
      <div>
        <el-button v-if="buttonStatus.examReportUploadStatus" class="right-margin" @click="reportuploadClick">报告上传
        </el-button>
        <el-button v-else class="right-margin" disabled>报告上传</el-button>
      </div>
      <!--      <div>-->
      <!--        <el-button v-if="buttonStatus.examReportSelectStatus" class="right-margin" @click="reportviewingClick">报告查看</el-button>-->
      <!--        <el-button v-else class="right-margin" disabled>报告查看</el-button>-->
      <!--      </div>-->
      <div v-if="this.$store.getters.empNo === '2407'">
        <el-tooltip class="item" effect="light" placement="left">
          <div slot="content">
            <el-button size="mini" type="primary" @click="confirmPrintTest">确认打印</el-button>
            <el-button size="mini" type="primary" @click="returnPrintTest">退费打印</el-button>
            <el-button size="mini" type="primary" @click="imagePrintTest">影像条码打印</el-button>
          </div>
          <!--          <el-button class="right-margin">测试</el-button>-->
        </el-tooltip>
      </div>

    </div>

    <div>
      <ExamPrint ref="printRef"></ExamPrint>
      <project-information ref="projectInformationRef"></project-information>
      <cost-inquiry ref="costInquiryRef"></cost-inquiry>
      <logistics-reminder ref="logisticsReminderRef"></logistics-reminder>
      <delete-button ref="deleteButtonRef" @delete-success="rightSelect"></delete-button>
      <noProject ref="noProjectRef"></noProject>
      <appointment-queue ref="appointmentQueueRef"></appointment-queue>
      <reportupload ref="reportuploadRef"></reportupload>
      <reportviewing ref="reportviewingRef"></reportviewing>
      <medicalrecord ref="medicalrecordRef"></medicalrecord>
      <return-premium ref="returnPremiumRef" @refund_print="refundPrint"></return-premium>
      <reservation-exam ref="reservationExamTwoRef"></reservation-exam>
      <scene-collect-fee ref="sceneCollectFeeRef"></scene-collect-fee>
      <exam-arrange ref="examArrangeRef"></exam-arrange>
    </div>
  </div>
</template>

<script>
import ExamPrint from './rightFunctionRealization/ExamPrint.vue'
import ProjectInformation from './rightFunctionRealization/projectInformation.vue'
import CostInquiry from './rightFunctionRealization/costInquiry.vue'
import logisticsReminder from './rightFunctionRealization/logisticsReminder'
import deleteButton from './rightFunctionRealization/deleteButton'
import noProject from './rightFunctionRealization/noProject.vue'
import AppointmentQueue from './rightFunctionRealization/appointmentQueue.vue'
import Reportupload from './rightFunctionRealization/reportUpload.vue'
import Reportviewing from './rightFunctionRealization/reportViewing.vue'
import Medicalrecord from './rightFunctionRealization/medicalRecord.vue'
import ReturnPremium from './rightFunctionRealization/returnPremium.vue'
import ReservationExam from './reservationExam.vue'
import SceneCollectFee from './rightFunctionRealization/sceneCollectFee.vue'
import ExamArrange from './rightFunctionRealization/examArrange.vue'

import env from '@/utils/ApiConfig'
/**
 * 右侧功能列表   此页面不接受任何 api调取等，只能采用组件传递方式实现
 */
export default {
  name: 'rightFunction',
  props: ['buttonStatus'],
  components: {
    ExamArrange,
    SceneCollectFee,
    ReservationExam,
    ReturnPremium,
    AppointmentQueue, deleteButton, logisticsReminder,
    CostInquiry, ProjectInformation, ExamPrint,
    noProject, Reportupload, Reportviewing, Medicalrecord
  },
  data() {
    return {
      text: '',
      options: [],
      rightOne: true,
      rightTwo: false,
      rightThree: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      const docEl = document.documentElement,
        docBody = document.body,
        winWidth = docEl.clientWidth || docBody.clientWidth // 获取浏览器的宽度
      if (winWidth <= 1200) {
        this.rightOne = false
        this.rightTwo = false
        this.rightThree = true
      } else if (winWidth <= 1400) {
        this.rightOne = false
        this.rightTwo = true
        this.rightThree = false
      } else {
        this.rightOne = true
        this.rightTwo = false
        this.rightThree = false
      }
    })
    window.addEventListener('keydown', this.handleKeydown)
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleKeydown)
  },
  methods: {
    csExamReportClick(){
      let data = this.$store.getters.rowTable;
      if (data){
        if (window.location.hostname.includes("192.168")){
          let path = "http://*************:10008/exam-report?examNo="+ data.examNo;
          window.open(path, '_blank');
        }else{
          let path = "http://*********:10008/exam-report?examNo="+ data.examNo;
          window.open(path, '_blank');
        }
      }
    },
    handleKeydown(event) {
      // 检查是否按下了Alt+Z
      if (event.altKey && event.key === 's') {
        this.tableSaveClick()
        // 阻止默认行为和事件冒泡
        event.preventDefault()
        event.stopPropagation()
      }
    },
    rowAppointment() {
      let data = this.$store.getters.rowTable
      if (data) {
        let examNos = []
        examNos.push(data.examNo)
        this.$store.commit('SET_APPOINTMENTS_EXAM_NO', examNos)
        this.$refs.reservationExamTwoRef.reservationCenterMonitor(true, 2)
      }

    },
    noProjectClick() {
      this.$refs.noProjectRef.noProjectMonitor(true)
    },
    tableSaveClick() {
      this.$emit('table-save', true)
    },
    printClick() {
      this.$refs.printRef.printRow(this.$store.getters.rowTable)
    },
    examArrangeClick(){
      this.$refs.examArrangeRef.init(true);
    },
    requestCodeClick(){
      let data = this.$store.getters.rowTable;
      if (data){
        if (window.location.hostname.includes("192.168")){
          let path = "http://*************:9001/applicationForm?examNo="+ data.examNo +"&type=1";
          window.open(path, '_blank');
        }else{
          let path = "http://*********:9001/applicationForm?examNo="+ data.examNo +"&type=1";
          window.open(path, '_blank');
        }
      }
    },
    printBarCodeClick(){
      this.$refs.printRef.printRowImg(this.$store.getters.rowTable)
    },
    refundPrint(data) {
      this.$refs.printRef.printOutpReturn(data)
    },
    projectClick() {
      this.$refs.projectInformationRef.projectMonitor(true)
    },
    sceneCollectFeeClick() {
      this.$refs.sceneCollectFeeRef.sceneCollectFeeMonitor(true)
    },
    costInquiryClick() {
      this.$refs.costInquiryRef.openDialog(true)
    },
    //物流提醒按钮
    logisticsReminderClick() {
      this.$refs.logisticsReminderRef.LogisticsReminderButton(this.$store.getters.rowTable)
    },
    //删除按钮
    deleteClick() {
      this.$refs.deleteButtonRef.Delete(this.$store.getters.rowTable)
    },
    //队列按钮
    queueClick() {
      this.$refs.appointmentQueueRef.dialogVerify(true, this.$store.getters.patient.deptCode)
    },
    queueSelectClick() {
      this.$refs.appointmentQueueRef.selectClick()
    },
    // 报告上传按钮
    reportuploadClick() {
      this.$refs.reportuploadRef.uploadVerify(this.$store.getters.rowTable)
    },
    // 报告查看按钮
    reportviewingClick() {
      this.$refs.reportviewingRef.reportView(this.$store.getters.rowTable)
    },
    // 病历查看按钮
    medicalRecordClick() {
      this.$refs.medicalrecordRef.medicalRecordView(this.$store.getters.rowTable)
    },
    rightSelect() {
      this.$emit('right-select', this.$store.getters.patient)
    },
    //退费按钮
    returnPremiumClick() {
      this.$refs.returnPremiumRef.returnPremiumInitVerify(true, this.$store.getters.rowTable)
    },
    //测试按钮
    //1·确认打印测试
    confirmPrintTest() {
      this.$refs.printRef.confirmPrintTest()
    },
    //2·影像打印测试
    imagePrintTest() {
      this.$refs.printRef.imagePrintTest()
    },
    //3·退费打印测试
    returnPrintTest() {
      this.$refs.printRef.returnPrintTest()
    }
  }
}
</script>

<style scoped lang="scss">
.rightHomeTwo {
  .right-master {
    display: flex;
    flex-direction: column;
    align-items: center;

    ::v-deep.el-button--medium {
      padding: 8px 10px;
      font-size: 14px;
      border-radius: 4px;
      min-width: 60px;
    }
  }

  .right-margin {
    margin: 2px;
  }
}

.rightHomeThree {
  .right-master {
    display: flex;
    flex-direction: column;
    align-items: center;

    ::v-deep.el-button--medium {
      padding: 6px 8px;
      font-size: 11px;
      margin-left: 0;
      border-radius: 4px;
      min-width: 60px;
    }
  }

  .right-margin {
    margin: 1px;
  }
}

.rightHomeOne {
  .right-master {
    display: flex;
    flex-direction: column;
    align-items: center;

    ::v-deep.el-button--medium {
      padding: 8px 20px;
      font-size: 14px;
      margin-left: 10px;
      border-radius: 4px;
      min-width: 110px;
    }
  }

  .right-margin {
    margin: 2px;
  }
}

@media screen and (max-height: 660px) {
  ::v-deep.el-button--medium {
    padding: 5px 8px !important;
    font-size: 10px !important;
  }
}
</style>
