<template xmlns:el-col="http://www.w3.org/1999/html">
  <div class="home" :style="bodyHeight">
    <!--    dialog详情信息-->
    <div class="my-dialog">
      <!--      <el-dialog :visible.sync="userDialogButton" width="70%">-->
      <!--      </el-dialog>-->
      <el-drawer :visible.sync="userDialogButton" size="70%" style="min-height: 700px">
        <!--        顶部人员信息-->
        <div class="MyUser">
          <div class="My-el-avatar" v-if="userButton">
            <el-avatar :src="manImages" v-if="appointmentParticulars.user.sex === '男'" size="small"></el-avatar>
            <el-avatar :src="woManImages" v-else-if="appointmentParticulars.user.sex === '女'" size="small"></el-avatar>
            <el-avatar :src="manImages" v-else size="small"></el-avatar>
          </div>
          <div class="my-user-text">
            <span class="my-text" style="font-weight: 1000">{{
                appointmentParticulars.user.name
              }}</span>
            <span class="my-text">/</span>
            <span class="my-text" style="font-weight: 1000">{{
                appointmentParticulars.user.sex
              }}</span>
            <span class="my-text">/</span>
            <span class="my-text" style="font-weight: 1000">{{
                appointmentParticulars.user.age
              }}</span>
            <span class="my-text">/</span>
            <span class="my-text" style="color: #1cbbb4">ID号:</span>
            <span class="my-text" style="color: red">{{
                appointmentParticulars.user.patienT_ID
              }}</span>
            <span class="my-text">/</span>
            <span class="my-text" style="color: #1cbbb4">住院次数:</span>
            <span class="my-text" style="color: #1c84c6">{{
                appointmentParticulars.user.visiT_ID
              }}</span>
            <span class="my-text">/</span>
            <span class="my-text" style="color: #1cbbb4">就诊日期:</span>
            <span class="my-text" style="color: #2c8d54">{{
                appointmentParticulars.user.visiT_DATA
              }}</span>
          </div>
        </div>
        <!--        中间 检查列表、申请单内容-->
        <div class="my-drawer">
          <!--        检查列表-->
          <div class="my-drawer-table">
            <div class="my-header-text" :style="findSize.size3">检查列表</div>
            <el-table ref="multipleTable2" :data="appointmentParticulars.exams" size="mini" border style="width: 100%">
              <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                  <el-button icon="" size="mini" type="text" @click="chart(scope.row.id)">检查闭环，
                  </el-button>
                  <el-button icon="" size="mini" type="text" @click="handlePrint(scope.row)">打印，
                  </el-button>
                  <el-button icon="" size="mini" type="text" @click="chargeback(scope.row)">撤销
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="exaM_CLASS_NAME" label="主类"></el-table-column>
              <el-table-column align="center" prop="exaM_SUBCLASS_NAME" label="子类"></el-table-column>
              <el-table-column align="center" prop="description" label="项目名称"></el-table-column>
              <el-table-column align="center" prop="bedsidE_FLAG" label="床旁">
                <template slot-scope="scope">
                    <el-tag type="danger" v-if="scope.row.bedsidE_FLAG === 'Y'">是</el-tag>
                    <el-tag v-else-if="scope.row.bedsidE_FLAG === 'N'">否</el-tag>
                    <el-tag v-else>否</el-tag>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="appointmenT_DATE" label="日期"></el-table-column>
              <el-table-column align="center" prop="appointmenT_TIME" label="预约时间"></el-table-column>
              <el-table-column align="center" prop="positioN_NAME" label="选择部位"></el-table-column>
              <el-table-column align="center" prop="executE_DEPT_NAME" label="执行科室"></el-table-column>
              <el-table-column align="center" prop="money" label="金额"></el-table-column>
            </el-table>
          </div>
          <!--        申请单内容-->
          <div class="my-drawer-in">
            <div class="my-header-text" :style="findSize.size3">申请单内容</div>
            <el-scrollbar style="height: 96%">
              <span class="my-sketch-span">1·检查目的</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="4" placeholder="请输入内容"
                          v-model="appointmentParticulars.user.inspectioN_PURPOSE"
                >
                </el-input>
              </div>
              <span class="my-sketch-span">2·症状</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="4" placeholder="请输入内容"
                          v-model="appointmentParticulars.medicalRecord.clinSymp"
                >
                </el-input>
              </div>

              <span class="my-sketch-span">3·体征</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="4" placeholder="请输入内容"
                          v-model="appointmentParticulars.medicalRecord.physSign"
                >
                </el-input>
              </div>
              <span class="my-sketch-span">4·临床诊断</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="4" placeholder="请输入内容"
                          v-model="appointmentParticulars.medicalRecord.clinDiag"
                >
                </el-input>
              </div>
              <span class="my-sketch-span">5·其他诊断</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="4" placeholder="请输入内容"
                          v-model="appointmentParticulars.medicalRecord.relevantDiag"
                >
                </el-input>
              </div>
              <span class="my-sketch-span">6·相关化验结果</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="4" placeholder="请输入内容"
                          v-model="appointmentParticulars.medicalRecord.relevantLabTest"
                >
                </el-input>
              </div>
            </el-scrollbar>
          </div>
          <!--                底部 医生签名、金额-->
          <div class="my-drawer-text">
            <div class="my-drawer-text-son">
              金额: {{ appointmentParticulars.user.suM_MONEY }}
            </div>
          </div>
        </div>
      </el-drawer>
      <!--        新弹框   展示流程图-->
      <el-drawer :visible.sync="chartBut" size="30%">
        <div>
          <span style="color: black; margin-left: 10%; font-size: large">检查流程详情图</span>
          <hr style="width: 80%"/>
          <el-timeline :reverse="false" class="timeline">
            <el-timeline-item class="timeLineItem" v-for="(flow, index) in chartData" :key="index" :color="flow.color"
                              :timestamp="flow.taskName"
            >
              <span>{{ flow.execution }}</span>
              <div class="dates">
                {{ flow.createDate }}
              </div>
              {{ flow.userName }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-drawer>

      <!--        新弹框   展示流程图-->
      <el-drawer :visible.sync="chartBut" size="30%">
        <div>
          <span style="color: black; margin-left: 10%; font-size: large">检查流程详情图</span>
          <hr style="width: 80%"/>
          <el-timeline :reverse="false" class="timeline">
            <el-timeline-item class="timeLineItem" v-for="(flow, index) in chartData" :key="index" :color="flow.color"
                              :timestamp="flow.taskName"
            >
              <span>{{ flow.execution }}</span>
              <div class="dates">
                {{ flow.createDate }}
              </div>
              {{ flow.userName }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-drawer>

      <!-- 弹出框 展示打印界面 -->
      <el-drawer :visible.sync="chartPrint" size="60%">
        <div :style="findSize.size2" style="margin-top: 10%" id="printDiv">
          <span style="color: black; font-size: large" class="center-text">河南宏力医院住院{{
              printDate.examClass
            }}申请单</span>
          <div style="
              display: flex;
              justify-content: space-around;
              align-items: flex-end;
            "
          >
            <div style="margin-top: 2%; width: 67%">
              <span style="margin-left: 0">住院号：{{ printDate.hospitalizedId }}</span>
              <span style="margin-left: 2%">病人ID：{{ printDate.patientId }}</span>
              <span style="margin-left: 2%">申请号：{{ printDate.examNo }}</span>
              <span style="margin-left: 2%">病区：{{ printDate.bedDeptName }}</span>
            </div>
            <div>
              <span slot="footer" class="dialog-footer" style="float: right">
                <div style="display: flex; flex-direction: column;font-size: 12px">
                  <vue-qr :text="printDate.qrImageUrl" :size="50" :margin="1000000" :whiteMargin="true"></vue-qr>
                </div>
              </span>
            </div>
          </div>
          <hr style="width: 95%"/>
          <div style="margin-top: 2%">
            <span style="margin-left: 6%">姓名：{{ printDate.patientName }}</span>
            <span style="margin-left: 4%">性别：{{ printDate.sex }}</span>
            <span style="margin-left: 4%">年龄：{{ printDate.age }}</span>
            <span style="margin-left: 4%">科别：{{ printDate.deptName }}</span>
            <span style="margin-left: 4%">床号：{{ printDate.bedNumber }}</span>
            <span style="margin-left: 4%">身份：{{ printDate.identity }}</span>
          </div>
          <hr style="width: 95%"/>
          <div style="min-height: 50px; margin-top: 15px">
            <span style="margin-left: 10%">症状：{{ printDate.symptom }}</span>
          </div>
          <div style="min-height: 50px; margin-top: 15px">
            <span style="
                margin-left: 10%;
                word-wrap: break-word;
                overflow-wrap: break-word;
              "
            >体征：{{ printDate.sign }}</span>
            <span></span>
          </div>
          <div style="min-height: 50px; margin-top: 15px">
            <span style="margin-left: 10%">临床诊断：{{ printDate.clinicalDiagnosis }}</span>
          </div>
          <div style="min-height: 50px;display: flex; margin-top: 15px">
            <div style="width: 60%;float: left;margin-left: 10%">检查项目：<span style="font-size: 12px;">{{
                printDate.examClassName
              }}</span></div>
            <div style="width: 30%;">检查部位：<span style="font-size: 12px;">{{ printDate.positionName }}</span></div>
          </div>
          <div style="min-height: 50px; margin-top: 15px;display: flex;" >
            <span style="width: 60%;float: left;margin-left: 10%">注意事项：{{ printDate.mattersNeedAttention }}</span>
            <div style="width: 30%;">床旁：
              <span style="font-size: 12px;" v-if="printDate.bedsideFlag === 'Y'">是</span>
              <span style="font-size: 12px;" v-else-if="printDate.bedsideFlag === 'N'">否</span>
              <span style="font-size: 12px;" v-else>否</span>
            </div>
          </div>
          <div style="margin-top: 2%">
            <span style="margin-left: 6%">申请医师：{{ printDate.applyDoctorName }}</span>
            <span style="margin-left: 4%">申请时间：{{ printDate.createDate }}</span>
            <span style="margin-left: 4%">检查位置：{{ printDate.position }}</span>
          </div>
          <hr style="width: 95%"/>
        </div>
        <el-button type="primary" round @click="printClick" style="
            margin-left: 80%;
            margin-bottom: 30%;
            border-radius: 50px;
            height: 80px;
          "
        >点击打印
        </el-button>
      </el-drawer>

      <!-- 批量打印 -->
      <el-drawer :visible.sync="chartAllPrint" size="60%">
        <div id="printDiv">
          <div v-for="item in printAllDate" :key="item.id" :style="findSize.size2" style="margin-top: 10%">
            <span style="color: black; font-size: large" class="center-text">河南宏力医院住院{{
                item.examClass
              }}申请单</span>
            <div style="
                display: flex;
                justify-content: space-around;
                align-items: flex-end;
              "
            >
              <div style="margin-top: 2%; width: 65%">
                <span style="margin-left: 0">住院号：{{ item.hospitalizedId }}</span>
                <span style="margin-left: 2%">病人ID：{{ item.patientId }}</span>
                <span style="margin-left: 2%">申请号：{{ item.examNo }}</span>
                <span style="margin-left: 2%">病区：{{ item.bedDeptName }}</span>
              </div>
              <div>
                <span slot="footer" class="dialog-footer" style="float: right">
                  <div style="display: flex; flex-direction: column;font-size: 12px;">
                    <vue-qr :text="item.qrImageUrl" :size="50" :margin="1000000" :whiteMargin="true"></vue-qr>
                  </div>
                </span>
              </div>
            </div>
            <hr style="width: 95%"/>
            <div style="margin-top: 2%">
              <span style="margin-left: 4%">姓名：{{ item.patientName }}</span>
              <span style="margin-left: 2%">性别：{{ item.sex }}</span>
              <span style="margin-left: 2%">年龄：{{ item.age }}</span>
              <span style="margin-left: 2%">科别：{{ item.deptName }}</span>
              <span style="margin-left: 2%">床号：{{ item.bedNumber }}</span>
              <span style="margin-left: 2%">身份：{{ item.identity }}</span>
            </div>
            <hr style="width: 95%"/>
            <div style="min-height: 50px; margin-top: 15px">
              <span style="margin-left: 10%">症状：{{ item.symptom }}</span>
            </div>
            <div style="min-height: 50px; margin-top: 15px">
              <span style="
                  margin-left: 10%;
                  word-wrap: break-word;
                  overflow-wrap: break-word;
                "
              >体征：<span style="font-size: 12px;">{{ item.sign }}</span></span>
              <span></span>
            </div>
            <div style="min-height: 50px; margin-top: 15px">
              <span style="margin-left: 10%">临床诊断：<span style="font-size: 12px;">{{
                  item.clinicalDiagnosis
                }}</span></span>
            </div>
            <div style="min-height: 50px;margin-top: 15px">
              <div style="width: 60%;float: left;margin-left: 10%">检查项目：<span style="font-size: 12px;">{{
                  item.examClassName
                }}</span></div>
              <div style="width;: 30%;">检查部位：<span style="font-size: 12px;">{{ item.positionName }}</span></div>
            </div>
            <div style="min-height: 50px; margin-top: 15px;display: flex;" >
              <span style="width: 60%;float: left;margin-left: 10%">注意事项：{{ item.mattersNeedAttention }}</span>
              <div style="width: 30%;">床旁：
                <span style="font-size: 12px;" v-if="item.bedsideFlag === 'Y'">是</span>
                <span style="font-size: 12px;" v-else-if="item.bedsideFlag === 'N'">否</span>
                <span style="font-size: 12px;" v-else>否</span>
              </div>
            </div>
            <div style="margin-top: 2%">
              <span style="margin-left: 4%">申请医师：{{ item.applyDoctorName }}</span>
              <span style="margin-left: 2%">申请时间：{{ item.createDate }}</span>
              <span style="margin-left: 2%">检查位置：{{ item.position }}</span>
            </div>
            <hr style="width: 95%"/>
          </div>
        </div>
        <el-button class="pdm-header" type="primary" round @click="printClick" style="
            margin-left: 80%;
            margin-bottom: 30%;
            border-radius: 50px;
            height: 80px;
          "
        >批量打印
        </el-button>
      </el-drawer>
    </div>
    <!--      左侧table展示-->
    <div class="MyTable">
      <div class="TableText">
        {{ tableText }}
      </div>
      <div class="MyForm1">
        <el-form :model="queryForm" size="mini">
          <el-form-item label-width="" label="检查名称:">
            <el-select style="width: 55%" v-model="pageQuery.examClassName" filterable placeholder="请选择"
                       @change="getExamSelectTreeList" :clearable="true"
            >
              <el-option v-for="item in examClassNameList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="" label="检查项目:">
            <el-select style="width: 55%" v-model="pageQuery.examSubClassName" filterable placeholder="请选择"
                       @change="getExamSelectTreeList" :clearable="true"
            >
              <el-option v-for="item in examSubClassNameList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="" label="检查方法:">
            <el-select style="width: 55%" v-model="pageQuery.pattern" filterable placeholder="请选择" :clearable="true">
              <el-option v-for="item in patternLists" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="my-table">
        <el-scrollbar style="height: 92%; overflow-x: hidden">
          <el-row class="my-table-col" v-for="(item, index) in registerListPage" :key="index">
            <el-col :span="6" class="my-table-el-col" :style="findSize.size2">
              <span>{{ item.name }}</span>
            </el-col>
            <el-col :span="9" class="my-table-el-col" :style="findSize.size2">
              <span>{{ item.appointmentDate }}</span>
            </el-col>
            <el-col :span="9" class="my-table-el-col" :style="findSize.size2">
              <span>{{ item.appointmentTime }}</span>
            </el-col>
            <el-col :span="24" v-for="(items, index) in item.projects" :key="index" class="my-table-el-col">
              <span @click="getExamParticulars(item.id)" class="my-table-text" :style="findSize.size2">{{
                  items.description
                }}</span>
            </el-col>
            <div style="margin-left: 70%">
              <el-tooltip effect="dark" content="详情列表" placement="bottom">
                <el-button icon="el-icon-tickets" type="text" @click="getExamParticulars(item.id)">
                </el-button>
              </el-tooltip>
              <el-tooltip effect="dark" content="批量打印" placement="bottom">
                <el-button icon="el-icon-printer" type="text" @click="printAllClick(item)" class="pringAll"
                           style="margin-left: 10%"
                ></el-button>
              </el-tooltip>
            </div>
          </el-row>
        </el-scrollbar>
        <div class="my-table-page">
          <!--          <el-pagination-->
          <!--            layout="prev, pager, next"-->
          <!--            :total="total"-->
          <!--            :limit.sync="pageQuery.pageSize"-->
          <!--            :page.sync="pageQuery.pageNum"-->
          <!--            v-show="total > 0"-->
          <!--            @pagination="getExamAppointmentRegisterList">-->
          <!--          </el-pagination>-->
          <pagination v-show="total > 0" :limit.sync="pageQuery.pageSize" :page.sync="pageQuery.pageNum" :total="total"
                      :pager-count="5" layout="prev, pager, next" @pagination="getExamAppointmentRegisterList"
          />
        </div>
      </div>
    </div>

    <!--    中间检查项目-->
    <div class="MyCentre">
      <!--      顶部用户信息展示-->
      <div class="MyUser">
        <div class="My-el-avatar" v-if="userButton">
          <el-avatar :src="manImages" v-if="users.sex === '男'" size="small"></el-avatar>
          <el-avatar :src="woManImages" v-else-if="users.sex === '女'" size="small"></el-avatar>
          <el-avatar :src="manImages" v-else size="small"></el-avatar>
        </div>
        <div class="my-user-text">
          <span class="my-text" style="font-weight: 1000">{{
              users.userName
            }}</span>
          <span class="my-text">/</span>
          <span class="my-text" style="font-weight: 1000">{{ users.sex }}</span>
          <span class="my-text">/</span>
          <span class="my-text" style="font-weight: 1000">{{ users.age }}</span>
          <span class="my-text">/</span>
          <span class="my-text" style="color: #1cbbb4">ID号:</span>
          <span class="my-text" style="color: red">{{ users.patientId }}</span>
          <span class="my-text">/</span>
          <span class="my-text" style="color: #1cbbb4">住院次数:</span>
          <span class="my-text" style="color: #1c84c6">{{
              users.visitId
            }}</span>
          <span class="my-text">/</span>
          <span class="my-text" style="color: #1cbbb4">就诊日期:</span>
          <span class="my-text" style="color: #2c8d54">{{
              users.treatmentDate
            }}</span>

          <el-tooltip effect="dark" content="医嘱列表" placement="bottom">
            <el-button style="float: right; margin-right: 50px" icon="el-icon-notebook-2" size="medium" type="text"
                       @click="getAdviceList"
            >
            </el-button>
          </el-tooltip>

          <el-tooltip effect="dark" content="检查预约" placement="bottom">
            <el-button style="float: right; margin-right: 50px" icon="el-icon-date" size="medium" type="text"
                       @click="handExamAppoint"
            >
            </el-button>
          </el-tooltip>
        </div>
      </div>

      <!--      检查项目树结构展示-->
      <div class="MyTree">
        <div class="my-header-text" :style="findSize.size3" style="text-align: left; margin-left: 2%">
          检查分类
        </div>
        <div class="">
          <el-input style="width: 100%" placeholder="输入关键字进行过滤" v-model="treeSearch">
          </el-input>
        </div>
        <div class="" style="height: 95%" :style="findSize.size2">
          <el-scrollbar style="height: 97%; overflow-x: hidden" :style="findSize.size2">
            <el-tree :style="findSize.size2" :highlight-current="true" :data="examTree" :props="defaultProps"
                     node-key="label" :filter-node-method="filterNode" @node-click="getCheckedNodes" ref="tree"
            >
            </el-tree>
          </el-scrollbar>
        </div>
      </div>

      <!--      中间检查项目多选-->
      <div class="MyExam">
        <div class="MyExam1">
          <div class="my-header-text" :style="findSize.size3">检查项目</div>
          <el-scrollbar style="height: 91%">
            <el-checkbox-group v-model="patternSetList">
              <el-descriptions :column="size.size1" size="small" direction="horizontal" border :contentStyle="CS"
                               :label-style="LS"
              >
                <el-descriptions-item v-for="(item, index) in patternList" :key="index" contentStyle="" align="center">
                  <el-checkbox style="float: left; color: #00afff" :label="item.label" :disabled="item.disabled"
                               @change="getCheckBox(item, item)"
                  >
                    <div>{{ item.label }}</div>
                    <div style="display: flex">{{ item.value }}</div>
                  </el-checkbox>
                  <div class="myExam1-text" v-if="cdssInit === '1'" @click="aiButton(item)">
                    <img class="myExam1-text-img" :src="answerQuestionsImg" alt=""/>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </el-checkbox-group>
          </el-scrollbar>
        </div>
        <div class="MyExam2">
          <div class="my-header-text" :style="findSize.size3">
            已选择项目列表
          </div>
          <div class="label-table" :style="findSize.size3">
            <el-table ref="multipleTable" :data="patternDetailsList" size="mini" :height="tableHeight" border
                      style="width: 100%"
            >
              <el-table-column align="center" label="操作" :width="tables.width1">
                <template slot-scope="scope">
                  <el-button icon="" size="mini" type="text" @click="deleteAppointmentTime(scope.row)">删除
                  </el-button>
                  <!-- <el-button
                    icon=""
                    size="mini"
                    type="text"
                    @click="AppointmentTime(scope.row)"
                    >预约</el-button
                  > -->
                </template>
              </el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="pattern" label="项目名称"
                               :width="tables.width2"
              >
                <template slot-scope="scope">
                  <div style="line-height: 12px !important;">{{ scope.row.pattern }}</div>
                  <div style="line-height: 12px !important;">{{ scope.row.patternCode }}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="attention" label="注意事项" :show-overflow-tooltip="true" :width="tables.width5">
                <template slot-scope="scope">
                  <div @click="handleEditAttention(scope.row)" style="cursor: pointer;">
                    {{ scope.row.attention }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="deptName" :show-overflow-tooltip="true" label="执行科室"
                               :width="tables.width5"
              >
                <template slot-scope="scope">
                  <div v-if="scope.row.deptList.length > 1">
                    <el-select v-model="scope.row.deptCode" @change="deptSelect(scope.row)" filterable>
                      <el-option v-for="item in scope.row.deptList" :key="item.value" :label="item.label"
                                 :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </div>
                  <div v-else>{{ scope.row.deptName }}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="appointmentTime" label="是否床旁" :width="tables.width3">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.bedsideFlag" filterable
                             placeholder="请选择"
                  >
                    <el-option v-for="item in cpDict" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" label="部位" :width="tables.width4">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.positionCode" @change="positionSelect(scope.row)" filterable
                             placeholder="请选择"
                  >
                    <el-option v-for="item in positionData" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="MyExam3">
          <div class="my-header-text" :style="findSize.size3">注意事项</div>
          <el-scrollbar style="height: 80%">
            <div v-for="(item, index) in patternDetailsList" :key="item.patternCode">
              <div :style="findSize.size2" v-if="item.attention != null">
                {{ index + 1 }}： {{ item.pattern }} -> {{ item.attention }}
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
      <!--      选择时间 dialog对话框-->
      <div class="time-select-dialog">
        <el-dialog :visible.sync="timeButton" width="80%">
          <div class="my-header-text" :style="findSize.size3">预约时间选择</div>
        </el-dialog>
      </div>

      <!--      右侧申请主诉-->
      <div class="MySketch">
        <div class="my-header-text" :style="findSize.size3">
          申请单内容
          <el-tooltip effect="dark" content="病历列表" placement="bottom">
            <el-button style="margin-right: -20px" icon="el-icon-s-claim" size="medium" type="text"
                       @click="getCseHistoryList"
            >
            </el-button>
          </el-tooltip>
        </div>
        <el-alert type="info" description="上方申请单内容旁的按钮,可快速修改患者门诊病历信息" show-icon>
        </el-alert>
        <el-scrollbar style="height: 86.5%">
          <span class="my-sketch-span">1·检查目的</span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="4" placeholder="请输入内容" v-model="users.inspectionPurpose">
            </el-input>
          </div>
          <span class="my-sketch-span">2·症状 <i style="color: red" :style="findSize.size3">*</i></span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="4" placeholder="请输入内容" v-model="users.clinSymp">
            </el-input>
          </div>

          <span class="my-sketch-span">3·体征 <i style="color: red" :style="findSize.size3">*</i></span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="4" placeholder="请输入内容" v-model="users.physSign">
            </el-input>
          </div>
          <span class="my-sketch-span">4·临床诊断
            <i style="color: red" :style="findSize.size3">*</i></span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="4" placeholder="请输入内容" v-model="users.clinDiag">
            </el-input>
          </div>
          <span class="my-sketch-span">5·其他诊断</span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="4" placeholder="请输入内容" v-model="users.relevantDiag">
            </el-input>
          </div>
          <span class="my-sketch-span">6·相关化验结果</span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="4" placeholder="请输入内容" v-model="users.relevantLabTest">
            </el-input>
          </div>
        </el-scrollbar>
      </div>
      <!--      最底部-->
      <div class="my-bottom">
        <div class="mu-bottom-text" v-if="bottomButton">
          金额: {{ money }}
          <el-button class="my-bottom-button" type="success" size="mini" @click="submitVerify">发送</el-button>
        </div>
      </div>

      <!--      医嘱dialog 弹窗列表-->
      <div class="my-dialog">
        <el-drawer :visible.sync="adviceButton" title="医嘱列表" size="50%" style="min-height: 700px">
          <el-table ref="multipleTable2" :style="findSize.size1" :data="adviceList" size="mini" border
                    style="width: 100%"
                    height="650"
          >
            <el-table-column align="center" prop="ordeR_NO" label="编号" width="40"></el-table-column>
            <el-table-column align="center" prop="ordeR_TEXT" label="医嘱名称"></el-table-column>
            <el-table-column align="center" prop="ordeR_CLASS" label="状态" width="70"></el-table-column>
            <el-table-column align="center" prop="starT_DATE_TIME" label="开单日期" width="140"></el-table-column>
          </el-table>
          <el-alert title="注意：" type="info"
                    description="此处不可修改医嘱任何信息,只提供查询,如需要操作,请到对应功能处操作!!!" show-icon
          >
          </el-alert>
        </el-drawer>
      </div>
      <!--      门诊病历dialog 弹窗列表-->
      <div class="my-dialog">
        <el-dialog title="病历选项" :visible.sync="cseHistoryButton" width="60%">
          <div class="my-dialog-cseHistory">
            <el-scrollbar style="height: 550px; width: 55%; overflow-x: hidden">
              <el-menu class="el-menu-vertical-demo" style="width: 100%" v-for="item in cseHistoryList"
                       :key="item.visitId"
              >
                <el-menu-item @click="cseHistoryClick(item)" :index="item.visitId.toString()">
                  <div class="el-menu-vertical-demo-item">
                    <span>{{ item.treatmentDate }}</span>
                    <span>{{ item.deptName }}</span>
                    <span>{{ item.doctorName }}</span>
                  </div>
                </el-menu-item>
              </el-menu>
            </el-scrollbar>
            <div style="width: 50%">
              <span class="my-sketch-span">1·症状</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="8" v-model="cseHistory.medHistory">
                </el-input>
              </div>

              <span class="my-sketch-span">2·体征</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="8" v-model="cseHistory.bodyExam">
                </el-input>
              </div>
              <span class="my-sketch-span">3·临床诊断</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="8" v-model="cseHistory.diagDesc">
                </el-input>
              </div>
            </div>
          </div>
          <div class="my-dialog-cseHistory-button">
            <el-button type="success" @click="cseHistoryButtonSucess">确定</el-button>
            <el-button type="danger" @click="cseHistoryButton = false">
              取消
            </el-button>
          </div>
        </el-dialog>
      </div>
      <div class="my-dialog">
        <el-dialog title="特殊人群选项" :visible.sync="specialStatus" width="40%">
          <el-form :model="specialForm" :rules="specialRules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="特殊选项：" prop="items">
              <el-select style="width: 260px;" v-model="specialForm.itemList" multiple placeholder="可进行多项选择"
                         @change="specialChage"
              >
                <el-option
                  v-for="item in specialItem"
                  :key="item.value"
                  :label="item.label"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <div style="display: flex;justify-content: center;align-items: center">
                <el-button style="width: 200px" type="primary" @click="submitExamApp">发送/开单</el-button>
              </div>
            </el-form-item>
          </el-form>
        </el-dialog>
      </div>
      <!-- 患者预约检查页面 从下往上打开 -->
      <div class="exam-appoint">
        <el-drawer title="患者预约检查" :visible.sync="appointDrawerVisible" :direction="appointDirection" size="70%">
          <appoint :patientInfo="patientInfo" :index="Math.random()"/>
        </el-drawer>
      </div>

      <!-- 病理检查申请单页面 从右往左打开 -->
      <div class="exam-bl">
        <el-drawer title="患者病理检查申请" :visible.sync="pathologyDrawerVisible" :direction="pathologyDirection"
                   size="60%"
        >
          <pathology :patientInfo="patientInfo" :users="users" :examItemName="examItemName" :index="Math.random()"
                     @close-drawer="pathologyDrawerVisible = false" @get-list="getExamAppointmentRegisterList"
          />
        </el-drawer>
      </div>

      <!-- 病理打印单页面 从右往左打开 -->
      <div class="exam-bl">
        <el-drawer title="患者病理打印单" :visible.sync="pathologyPrintDrawerVisible" size="100%">
          <pathologyPrint @close-drawer="pathologyPrintDrawerVisible = false" @get-list="getExamAppointmentRegisterList"
                          :index="Math.random()" :examNo="examNo"
          />
        </el-drawer>
      </div>
    </div>
  </div>
</template>

<script>
import {
  GetExamPatternParticularsItem,
  GetExamPositionItems,
  GetExamTree,
  GetExamProjectParticularsList,
  GetExamPatternParticulars,
  SaveExamAppointmentRegister,
  GetExamAppointmentRegisterList,
  GetExamSelectTreeList,
  GetExamParticularsById,
  GetExamFlowChart,
  GetExamHospitalizedPrint,
  GetExamHospitalizedPrintAll,
  Chargeback,
  GetAdviceList,
  GetCseHistoryList,
  GetAiVerifyData,
  VerifyItemCodeMomoData,
  GetSpecialItemList
} from '@/api/appointment/register'
import {VerifyCdssCntrol,VerifyCdssInit} from '@/api/checkAndConfirm/checkCommon'
import { GetPatApplyExamInfo } from '@/api/appointment/appointInpExam'
import vueQr from 'vue-qr'
import man from '@/assets/icons/svg/man.png'
import woMan from '@/assets/icons/svg/woman.png'
import print from 'print-js'
import answerQuestions from '@/assets/icons/answerQuestions1.png'
//引入检查预约组件
import appoint from './appointTime.vue'

//引入病理检查申请组件
import pathology from './appointPathology.vue'

// 引入病理打印界面
import pathologyPrint from './pathologyPrint.vue'
import Log from '../monitor/job/log.vue'

export default {
  directives: {
    print
  },
  name: 'register',
  components: {
    //VueBarcode
    appoint,
    vueQr,
    pathology,
    pathologyPrint
  },
  data() {
    return {
      //预约检查抽屉
      appointDrawerVisible: false,
      appointDirection: 'btt',
      cpDict:[{ value: 'N', label: '否', },{value: 'Y',label: '是'}],
      specialItem: [],
      specialList: [],
      specialForm: {
        itemList: []
      },
      specialRules: {
        items: [{ required: true, message: '请选择特殊检查选项至少1个', trigger: 'change' }]
      },
      specialStatus: false,
      //病理检查申请抽屉
      pathologyDrawerVisible: false,
      // 病理打印抽屉
      pathologyPrintDrawerVisible: false,
      pathologyDirection: 'rtl',
      //点击选择的检查项目名称
      examItemName: undefined,
      examNo: undefined,
      patientInfo: {
        patientId: undefined,
        visitId: undefined,
        empNo: undefined
      },
      applyDate: '',
      applyTime: '',
      answerQuestionsImg: answerQuestions,
      positionBut: false,
      total: 0,
      vCs: {},
      chartData: [],
      chartBut: false,
      chartPrint: false,
      chartAllPrint: false,
      tableHeight: '180px',
      CS: {
        'text-align': 'center', //文本居中
        'word-break': 'break-all', //过长时自动换行
        'font-weight': '400',
        'font-size': '16px',
        float: 'left',
        width: '100%'
      },
      LS: {
        color: '#000',
        'text-align': 'center',
        'font-weight': '0',
        'font-size': '0px',
        'min-width': '-100px',
        'word-break': 'keep-all'
      },
      userDialogButton: false,
      bottomButton: false,
      TreeTrue: true,
      screenWidth: 0,
      screenHeight: '800px',
      manImages: man,
      numberNo: 0,
      woManImages: woMan,
      tableText: '预约检查记录',
      begetterExamName: '',
      sonExamName: '',
      ApplyTimeDate: '',
      rowNo: 0,
      timeSetList: '',
      timeButton: false,
      forenoonTimeList: [],
      afternoonTimeList: [],
      weeHoursTimeList: [],
      morningTimeList: [],
      noonTimeList: [],
      atDuskTimeList: [],
      nightTimeList: [],
      examTreeTimeList: [],
      examTree: [],
      options: [],
      money: undefined,
      bufferPatternList: [],
      patternList: [],
      patternSetList: [],
      queryPattern: [],
      patternDetailsList: [],
      userButton: true,
      treeSearch: '',
      defaultProps: {
        children: 'children',
        label: 'sexAttr'
      },
      users: {
        userName: '',
        sex: '',
        age: '',
        patientId: '',
        bed: '',
        treatmentDate: '',
        visitId: '',
        costType: '',
        diagnose: ''
      },
      queryForm: {
        examClassName: '',
        examSubClassName: '',
        description: ''
      },
      querySubmit: {
        users: undefined,
        patternDetails: undefined,
        visitNo: undefined,
        sumMoney: undefined,
        type: undefined,
        attention: undefined
      },
      query: {
        patientId: '',
        visitId: '',
        empNo: '',
        data: []
      },
      registerListPage: [],
      pageQuery: {
        pageNum: 1,
        pageSize: 10,
        patientId: '',
        examClassName: '',
        examSubClassName: '',
        pattern: ''
      },
      examClassNameList: [],
      examSubClassNameList: [],
      patternLists: [],
      appointmentParticulars: {
        user: [],
        exams: [],
        medicalRecord: []
      },
      positionData: [],
      bodyHeight: '',
      tables: {
        width1: '',
        width2: '',
        width3: '',
        width4: '',
        width5: ''
      },
      findSize: {
        size1: undefined,
        size2: undefined,
        size3: undefined
      },
      size: {
        size1: undefined,
        size2: undefined
      },
      printDate: {},
      printAllDate: [],
      datePiclerData: {},
      verifyAiData: [],
      sonId: '',
      type: '',
      patientId: '',
      visitId: '',
      adviceList: [],
      adviceButton: false,
      cseHistoryList: [],
      cseHistoryButton: false,
      cseHistory: {},
      huimei: {
        isOpen: false,
        url: '',
        hospitalGuid: '01',
        hospitalName: '河南宏力医院',
        autherKey: 'DA12FB821147938CA09641D3B51365C5'
      },
      autherEntity: {
        autherKey: 'DA12FB821147938CA09641D3B51365C5',
        userGuid: '',
        serialNumber: '',
        doctorGuid: '',
        doctorName: '',
        department: '',
        hospitalGuid: '1',
        hospitalName: '河南宏力医院',
        customEnv: '1',
        flag: 'm'
      },
      beInHospitalEntity: {
        userGuid: '',
        serialNumber: '',
        patientName: '',
        doctorGuid: '',
        doctorName: '',
        caseNo: '',
        admissionTime: '',
        inpatientDepartment: '',
        inpatientArea: '',
        pageSource: 4,
        currentBedCode: '',
        inpatientDepartmentId: '',
        triggerSource: 1,
        patientInfo: {
          gender: '',
          birthDate: '',
          age: '',
          ageType: '',
          maritalStatus: '',
          pregnancyStatus: 0
        },
        medicalOrders: [
          {
            orderId: '',
            doctorGuid: '',
            timelinessFlag: 1,
            orderClass: 1,
            orderType: 2,
            orderCode: '',
            orderContent: '',
            dosage: '1.000',
            unit: '次',
            frequency: '立即',
            dosageForm: '',
            pathway: '',
            specification: '',
            createTime: '',
            executeTime: '',
            stopTime: '',
            orderFlag: '',
            doctorName: '',
            description: '',
            payType: '',
            quantity: '',
            orderPlanStopTime: '',
            orderCancelTime: '',
            orderGroupId: ''
          }
        ]
      },
      cdssConorl: '0',
      cdssInit: '0',
    }
  },
  methods: {
    getVerifyCdssCntrol(){
      VerifyCdssCntrol().then(res => {
        if (res.code === 200){
          //data = 1 校验，不等于1 不校验
          this.cdssConorl = res.data;
        }
      })
    },
    handleEditAttention(row) {
      this.$prompt('请输入注意事项', '注意事项', {
        confirmButtonText: '确定',
        inputValue: row.attention
      }).then(({ value }) => {
        row.attention = value;
      }).catch(() => {
        // 用户取消编辑
      });
    },
    /**
     * 患者检查时间预约
     * @param {*} item
     */
    async handExamAppoint() {
      //todo:判断当前患者有没有已开发未确认未预约的检查申请信息
      await GetPatApplyExamInfo(this.patientInfo).then((response) => {
        if (response.data) {
          this.appointDrawerVisible = true
        } else {
          this.$modal.msgError('该患者暂未检查申请信息！请核实！')
        }
      })
    },

    async sexVerifyAi(bean, label) {
      return new Promise((resolve) => {
        if (this.cdssConorl === '1'){
          mayson.ai(bean, (data, warnlevel) => {
            const verifyItem = {
              warnlevel: warnlevel,
              label: label
            };
            this.verifyAiData.push(verifyItem);
            console.log("进入卡控校验,结果:",warnlevel)
            resolve(warnlevel); // 关键：通过 resolve 返回结果
          });
        }else{
          resolve(this.cdssConorl);
        }

      });
    },
    aiButton(item) {
      const loading = this.$loading({
        lock: true,
        text: '知识库正在努力加载中,请稍后!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      if (
        item.label !== '' ||
        item.label !== undefined ||
        item.label !== null
      ) {
        let ca = {
          no: ++this.numberNo,
          begetterExamName: this.begetterExamName,
          sonExamName: this.sonExamName,
          pattern: item.label
        }
        GetExamPatternParticularsItem(ca)
          .then((res) => {
            this.knowledgeBase(res.data)
            loading.close()
          })
          .finally((t) => {
            loading.close()
          })
      } else {
        loading.close()
      }
    },
    //惠美初始化方法
    init(autherEntity) {
      HM.maysonLoader(autherEntity, function(mayson) {
        // mayson.closeMaysonPro();
        mayson.setDrMaysonConfig('m', 1)
        window.mayson = mayson
        mayson.ai()
      })
    },
    //惠美初始化方法加载
    createInit(userGuid, serialNumber, doctorGuid, doctorName, department) {
      this.autherEntity.userGuid = userGuid
      this.autherEntity.serialNumber = serialNumber
      this.autherEntity.doctorGuid = doctorGuid
      this.autherEntity.doctorName = doctorName
      this.autherEntity.department = department
      this.init(this.autherEntity)
    },
    //惠美知识库参数查看
    knowledgeBase(item) {
      let data = {
        name: item.pattern,
        type: '12',
        id: item.patternCode,
        customEnv: '2',
        isActicleDetail: '1'
      }
      mayson.openArticleDetail(data)
    },
    //惠美医嘱开立
    ordersOpen(item) {
      if (this.cdssInit === '1'){
        try {
          // mayson.closeMaysonPro();
          mayson.ai(item)
        } catch (e) {
          console.log(e.message)
        }
      }
    },
    //病历确定事件
    cseHistoryButtonSucess() {
      let cas = this.cseHistory
      this.users.clinSymp = cas.medHistory
      this.users.physSign = cas.bodyExam
      this.users.clinDiag = cas.diagDesc
      this.cseHistory = {}
      this.cseHistoryButton = false
    },
    //病历单词点击事件
    cseHistoryClick(item) {
      this.cseHistory = item
    },
    //获取门诊病历列表
    getCseHistoryList() {
      GetCseHistoryList(this.patientId, this.type).then((res) => {
        this.cseHistoryList = res.data
        this.cseHistoryButton = true
        let us = this.users
        this.cseHistory.medHistory = us.clinSymp
        this.cseHistory.bodyExam = us.physSign
        this.cseHistory.diagDesc = us.clinDiag
      })
    },
    //医嘱列表
    getAdviceList() {
      GetAdviceList(this.patientId, this.visitId, this.type).then((res) => {
        this.adviceList = res.data
        this.adviceButton = true
      })
    },
    //医嘱作废
    chargeback(row) {
      this.$confirm('确定要作废当前《' + row.description + '》检查项目吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          let data = {
            id: row.id,
            type: this.querySubmit.type,
            user: this.users
          }
          Chargeback(data).then((res) => {
            this.$message({
              type: 'success',
              message: res.message
            })
            this.ordersOpen(res.data)
            this.getExamParticularsById(this.sonId)
            this.getExamAppointmentRegisterList()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          })
        })
    },
    //预约事件处理
    datePicker() {
      let a = this.ApplyTimeDate
      this.ApplyTimeDate = a.toLocaleDateString().toString()
      this.AppointmentTime(this.datePiclerData)
    },
    //部位处理
    positionSelect(row) {
      let positionCode = row.positionCode
      let positionData = this.positionData
      positionData.forEach(function(item, index) {
        if (positionCode === item.value) {
          row.positionName = item.label
          return
        }
      })
    },
    //执行科室处理
    deptSelect(row) {
      let deptCode = row.deptCode
      let deptData = row.deptList
      deptData.forEach(function(item, index) {
        if (deptCode === item.value) {
          row.deptName = item.label
          return
        }
      })
    },
    //获取检查部位下拉选
    getPositionTree() {
      GetExamPositionItems().then((res) => {
        this.positionData = res.data
      })
    },
    //获取检查项目下拉树结构
    getTree() {
      GetExamTree(this.query).then((res) => {
        let user = res.data.user
        this.examTree = res.data.treeList
        this.query.deptCode = user.deptCode
        this.users = user
        VerifyCdssInit().then(res => {
          if (res.code === 200){
            this.cdssInit = res.data;
            if (res.data === '1'){
              this.createInit(
                user.patientId,
                user.patientNumber,
                user.doctorCode,
                user.doctorName,
                user.deptName
              )
            }
          }
        })
        this.userButton = true
        if (
          user.clinSymp === null ||
          user.physSign === null ||
          user.clinDiag === null
        ) {
          this.$alert(
            `申请单内容自动提取异常，请点击确定后手动选择：症状、体征、诊断等内容`,
            '数据异常提示',
            {
              confirmButtonText: '确定',
              callback: (action) => {
                this.getCseHistoryList()
              }
            }
          )
        }
      })
    },
    //左侧检查项目 树结构点击 回调
    getCheckedNodes() {
      this.begetterExamName = ''
      this.sonExamName = ''
      //1·获取点击节点详情
      let v = this.$refs.tree.getCurrentNode()
      // 2·判断节点点击是否为空
      if (v !== null) {
        let begetterExamName = v.value
        let sonExamName = v.label
        if (begetterExamName !== null && sonExamName !== null) {
          // 3·如果两者都不为null证明是子节点点击    如果是父节点点击，什么都不做
          this.begetterExamName = begetterExamName
          this.sonExamName = sonExamName
          //  4·接口获取检查项目信息
          GetExamProjectParticularsList(
            this.begetterExamName,
            this.sonExamName
          ).then((res) => {
            this.patternList = res.data
          })
        }
      }
    },
    // Todo 如有需要  增加删除提示，
    //删除已选择项目列表
    deleteAppointmentTime(row) {
      /**
       * 1·根据 删除按钮 传递的row  删除指定数据
       */
      let c = this.patternDetailsList
      let index1 = undefined
      let index2 = undefined
      //记录 相等的 item 下标
      c.forEach(function(item, index) {
        if (row.no === item.no) {
          index1 = index
        }
      })
      //如果 记录的下标符合条件  进行数据处理
      if (index1 !== undefined) {
        //删除指定数据
        delete c[index1]
        //数据判空
        let nweArray = []
        c.forEach(function(item, index) {
          if (item !== null) {
            nweArray.push(item)
          }
        })
        this.patternDetailsList = nweArray
        this.bufferPatternList = nweArray
        //2·处理玩 数据后  处理多选框的数据
        let patternSetList1 = []
        let patternSetList2 = this.patternSetList
        patternSetList2.forEach(function(item, index) {
          if (item === row.pattern) {
            index2 = index
          }
        })
        if (index2 !== undefined) {
          delete patternSetList2[index2]
        }
        //如果不重新赋值   数据不完整
        patternSetList2.forEach(function(item, index) {
          if (item !== null) {
            patternSetList1.push(item)
          }
        })
        this.patternSetList = patternSetList1

        //3· 删除查询参数 列表
        let index3 = undefined
        let queryPattern1 = []
        let queryPattern2 = this.queryPattern
        queryPattern2.forEach(function(item, index) {
          if (item.no === row.no) {
            index3 = index
          }
        })
        //删除
        if (index3 !== undefined) {
          delete queryPattern2[index3]
        }
        //如果不重新赋值   数据不完整
        queryPattern2.forEach(function(item, index) {
          if (item !== null) {
            queryPattern1.push(item)
          }
        })
        this.queryPattern = queryPattern1
      }
      this.tableDataProcessing()
      this.$nextTick(() => {
        this.$refs['multipleTable'].doLayout()
      })
    },
    //点击预约时间处理类
    AppointmentTime(row) {
      this.datePiclerData = row
      // 打开 时间选择弹出框
      this.timeButton = true
      //缓存 本条数据的 编号，选择时间后 赋值使用
      this.rowNo = row.no
      // 如果本条数据有 时间相关数据  进行回显
      this.timeSetList = row.appointmentTime
    },
    //选择预约时间后的回调
    setAppointmentTime: function(label) {
      /**
       * 选择时间后
       * 1·对指定表单数据 赋值
       * 2·buffer缓存本次数据，下次新点击报告如果有新增的话 将原有的时间  放入指定的 数据中
       */
      let date = this.ApplyTimeDate
      let no = this.rowNo
      let c = []
      //判断no 是否一致  如果一致 将选择的时间放入 时间字段中
      this.patternDetailsList.forEach(function(item, index) {
        if (item.no === no) {
          item.appointmentTime = label
          item.appointmentDate = date
        }
        c.push(item)
      })
      let bo = false
      //判断 选择项目中有无冲突时间，如果有，进行提醒
      c.forEach(function(item, index) {
        if (item.no !== no) {
          if (
            (item.appointmentTime !== item.appointmentTime) !== undefined ||
            item.appointmentTime !== ''
          ) {
            if (item.appointmentTime === label) {
              bo = true
            }
          }
        }
      })
      if (bo) {
        this.$message({
          message:
            '警告: ' +
            label +
            '当前时间点已有预约项目,可能会检查冲突,请仔细核查!',
          type: 'warning',
          duration: 10000,
          showClose: true
        })
      }
      //将处理的结果重新赋值给 表单绑定数据
      this.patternDetailsList = c
      //将处理的结果 放入缓冲集合中  下次新点击报告如果有新增的话 将原有的时间  放入指定的 数据中
      this.bufferPatternList = c
      //关闭弹出框
      this.timeButton = false
      //单选框绑定值清楚
      this.timeSetList = ''

      //动态刷新 table表格数据
      this.$nextTick(() => {
        this.$refs['multipleTable'].doLayout()
      })
    },
    //table 数据处理类
    tableDataProcessing() {
      /**
       * 1·将 table现有数据 和 buffer缓冲数据 进行比对
       * 2·比对成功后  现有table进行 appointmentTime 赋值    操作
       */
      let buffer = this.bufferPatternList
      if (buffer !== null) {
        //如果缓冲区不等null   进行数据配对
        let c = []
        let patternDetailsList = this.patternDetailsList
        if (patternDetailsList !== null) {
          patternDetailsList.forEach(function(item, index) {
            let array = buffer.filter((t) => item.no === t.no)
            if (array.length > 0) {
              item.appointmentTime = array[0].appointmentTime
              item.positionName = array[0].positionName
              item.positionCode = array[0].positionCode
            }
            c.push(item)
          })
        }

        this.bufferPatternList = c
        this.patternDetailsList = c
        this.$nextTick(() => {
          this.$refs['multipleTable'].doLayout()
        })
      }
      let money = 0
      if (this.patternDetailsList.length > 0) {
        this.patternDetailsList.forEach(function(item, index) {
          money += item.money
        })
        this.bottomButton = true
      } else {
        money = 0
        this.bottomButton = false
      }

      this.money = money
    },
    setItemDisabled(label, item) {
      console.log(this.patternSetList)
      const index = this.patternSetList.findIndex(
        (patternSet) => patternSet === label
      )
      index != -1 && this.patternSetList.splice(index, 1)
    },
    getSpecialItemList() {
      GetSpecialItemList().then(res => {
        this.specialItem = res.data
      })
    },
    //3· 多选框点击事件
    getCheckBox(label, item) {
      if (item.examClass === '病理' && item.examSubClass !== '显微摄影') {
        this.examItemName = item.label
        this.pathologyDrawerVisible = true
      } else {
        let ca = {
          no: ++this.numberNo,
          begetterExamName: this.begetterExamName,
          sonExamName: this.sonExamName,
          pattern: label.label,
          patternCode: label.value,
        }
        if (item.label !== '' || item.label !== undefined || true) {
          let data = {
            user: this.users,
            exam: ca
          }
          GetAiVerifyData(data).then((res) => {
            this.sexVerifyAi(res.data, label).then((res) => {
              console.log(res)
              if (res === 1) {
                this.setItemDisabled(label, item)
              } else {
                //2·获取 多选框绑定值，已方便计算手动维护数据那些有，那些没有
                let patternSetList = this.patternSetList
                // 查询参数维护集合
                let patternList = this.queryPattern
                // 下标集合，方便记录多选框中没有的下标值 最终数据处理
                let noIndex = []
                patternList.push(ca)
                //3·自定义 集合循环判断  多选框patternSetList中是否包含本次patternList中数据
                //如果没有 noIndex 中记录下标
                patternList.forEach(function(item, index) {
                  if (patternSetList.includes(item.pattern)) {
                  } else {
                    noIndex.push(index)
                  }
                })
                // 4·如果下标集合中 有负责条件的进行处理
                // 集合到排序后  patternList删除下标不符合的集合
                if (noIndex.length > 0) {
                  noIndex.sort().reverse()
                  noIndex.forEach(function(item, index) {
                    delete patternList[item]
                  })
                }
                //5·数据处理完毕后，创建新集合
                //重新赋值给 查询集合 this.queryPattern
                let patternListTwo = []
                patternList.forEach(function(item, index) {
                  if (item !== null) {
                    patternListTwo.push(item)
                  }
                })
                //6· 重新赋值
                this.queryPattern = patternListTwo
                //7· 查询数据
                this.getExamPatternParticulars(item)
              }
            })
          })
        }
      }
    },
    //获取 检查项目详情信息（金额、注意事项等。）
    getExamPatternParticulars(item) {
      this.query.data = this.queryPattern
      GetExamPatternParticulars(this.query).then((res) => {
        this.patternDetailsList = res.data
        //数据比对
        this.tableDataProcessing()
        //获取后  看当前是否有备注信息
        this.verifyItemCodeMomoData(item.value)
      })
    },
    verifyItemCodeMomoData(data) {
      VerifyItemCodeMomoData(data).then(res => {
      })
    },
    //数据提交按钮
    submitVerify() {
      let user = this.users
      if (
        user.clinSymp === null ||
        user.physSign === null ||
        user.clinDiag === null
      ) {
        this.$confirm('申请单内容不完善，是否要继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.verifySpecial()
          })
          .catch(() => {
            this.getCseHistoryList()
          })
      } else {
        this.verifySpecial()
      }
    },
    submitExamApp() {
      const loading = this.$loading({
        lock: true,
        text: '休息一下,数据正在保存中(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      let ta = this.patternDetailsList
      let attention = ''
      ta.forEach(function(item, index) {
        if (item.attention != null) {
          attention +=
            index + 1 + '：  ' + item.pattern + '  ->  ' + item.attention + ','
        }
      })
      this.querySubmit = {
        users: this.users,
        patternDetails: this.patternDetailsList,
        sumMoney: this.money,
        attention: attention,
        special: this.specialList
      }
      // console.log(this.querySubmit)
      // loading.close();
      // return;
      this.querySubmit.type = this.$route.query && this.$route.query.type
      SaveExamAppointmentRegister(this.querySubmit)
        .then((res) => {
          if (res.code === 200) {
            this.$message.success(res.message)
            this.getExamAppointmentRegisterList()
            this.bufferPatternList = []
            this.patternDetailsList = []
            this.patternSetList = []
            this.patternList = []
            this.specialList = []
            this.specialStatus = false
            this.ordersOpen(res.data)
            loading.close()
          }
        })
        .catch((res) => {
          loading.close()
        })
    },
    verifySpecial() {
      this.$confirm('请确认当前患者是否是特殊人员, 如是,请点击确认按钮,进行特殊人群备注?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '否/发送',
        type: 'warning'
      }).then(() => {
        this.specialList = []
        this.specialForm.itemList = []
        this.specialStatus = true
      }).catch(() => {
        this.submitExamApp()
      })
    },
    specialChage(data) {
      this.specialList = []
      data.forEach(x => {
        this.specialList.push({
          value: x.value,
          label: x.label
        })
      })
    },
    //树结构筛选
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    //获取预约检查记录分页信息
    getExamAppointmentRegisterList() {
      GetExamAppointmentRegisterList(this.pageQuery).then((res) => {
        this.registerListPage = res.data.data
        this.total = res.data.total
      })
    },
    //获取预约查询 下拉书选择详情
    getExamSelectTreeList() {
      this.getExamAppointmentRegisterList()
      GetExamSelectTreeList(this.pageQuery).then((res) => {
        this.patternLists = res.data.projects
        this.examClassNameList = res.data.classNames
        this.examSubClassNameList = res.data.subClassNames
      })
    },
    //预约记录dialog对话框
    getExamParticulars(id) {
      this.getExamParticularsById(id)
    },
    //获取预约记录详情细腻
    getExamParticularsById(id) {
      this.sonId = id
      GetExamParticularsById(id)
        .then((res) => {
          this.appointmentParticulars = {
            user: res.data.user,
            exams: res.data.exams,
            medicalRecord: res.data.medicalRecord
          }
          this.userDialogButton = true
        })
        .catch(() => {
          this.userDialogButton = false
        })
    },
    //查询重置
    pageResults() {
      this.pageQuery = {
        pageNum: 1,
        pageSize: 10,
        examClassName: '',
        examSubClassName: '',
        pattern: ''
      }
      this.getExamSelectTreeList()
      this.getExamAppointmentRegisterList()
    },
    timeCreate() {
      let date = new Date()
      this.ApplyTimeDate = date.toLocaleDateString().toString()
    },
    //流程图回调
    chart(id) {
      GetExamFlowChart(id).then((res) => {
        this.chartData = res.data
        this.chartBut = true
      })
    },
    // 打印按钮操作
    handlePrint(row) {
      if (row.exaM_CLASS_NAME === '病理' && row.exaM_SUBCLASS_NAME !== '显微摄影') {
        this.examNo = row.exaM_NO
        this.pathologyPrintDrawerVisible = true
      } else {
        GetExamHospitalizedPrint(row.id, this.querySubmit.type).then((res) => {
          let url =
            'https://5g.honlivhpit.com/exam-appoint?patientId=' +
            this.patientId +
            '&type=2&visitId=' +
            this.visitId
          this.printDate = res.data
          this.printDate.qrImageUrl = url
        })
        this.chartPrint = true
      }
    },
    // 批量打印操作
    printAllClick(item) {
      if (item.projects[0].exaM_CLASS_NAME !== '病理' && item.projects[0].exaM_SUBCLASS_NAME !== '显微摄影') {
        const id = item.id
        GetExamHospitalizedPrintAll(id, this.querySubmit.type).then((res) => {
          let data = res.data
          let url =
            'https://5g.honlivhpit.com/exam-appoint?patientId=' +
            this.patientId +
            '&type=2&visitId=' +
            this.visitId
          data.forEach(function(item, index) {
            item.qrImageUrl = url
          })
          this.printAllDate = data
        })
        this.chartAllPrint = true
      } else {
        if (item.projects.length > 0) {
          this.examNo = item.projects[0].exaM_NO
          this.pathologyPrintDrawerVisible = true
        }
      }
    },
    // 打印
    printClick() {
      const style = '@page {margin:0 10mm};' //打印时去掉眉页眉尾
      printJS({
        printable: 'printDiv',
        type: 'html',
        header: '',
        targetStyles: ['*'],
        style,
        scanStyle: false
      })
    }
  },
  mounted() {
    this.$nextTick(() => {
      const bodyStyle = document.body.style, // 获取body节点样式
        htmlStyle = document.getElementsByTagName('html')[0].style, // 获取html节点样式
        docEl = document.documentElement,
        docBody = document.body,
        winWidth = docEl.clientWidth || docBody.clientWidth, // 获取浏览器的宽度
        WinHeight = docEl.clientHeight || docBody.clientHeight // 获取浏览器的高
      this.bodyHeight = 'height:' + WinHeight + 'px'
      bodyStyle.minWidth = '1014px'
      bodyStyle.minHeight = '768px'
      htmlStyle.minHeight = '768px'
      htmlStyle.minWidth = '1014px'
      this.tables = {
        width1: winWidth / 20,
        width2: winWidth / 8.5,
        width3: winWidth / 17,
        width4: winWidth / 12.8,
        width5: winWidth / 9.3
      }
      if (winWidth <= 1240) {
        this.tableHeight = '180px'
        this.size = {
          size1: 1,
          size2: 2
        }
        this.findSize = {
          size1: 'font-size: 10px !important;',
          size2: 'font-size: 12px !important;',
          size3: 'font-size: 14px !important;'
        }
      } else if (winWidth <= 1500) {
        this.tableHeight = '225px'
        this.size = {
          size1: 1,
          size2: 3
        }
        this.findSize = {
          size1: 'font-size: 12px !important;',
          size2: 'font-size: 14px !important;',
          size3: 'font-size: 16px !important;'
        }
      } else {
        this.tableHeight = '225px'
        this.size = {
          size1: 2,
          size2: 4
        }
        this.findSize = {
          size1: 'font-size: 14px !important;',
          size2: 'font-size: 16px !important;',
          size3: 'font-size: 18px !important;'
        }
      }
    })
  },
  watch: {
    treeSearch(val) {
      this.$refs.tree.filter(val)
    },
    verifyAiData(newVal, oldVal) {
      let th = this
      newVal.forEach(function(x, index) {
        if (x.warnlevel === 1) {
          th.verifyAiData = th.verifyAiData.filter((t) => t.label !== x.label)
          th.patternDetailsList = th.patternDetailsList.filter(
            (t) => t.pattern !== x.label
          )
          th.patternSetList = th.patternSetList.filter((t) => t !== x.label)
          th.queryPattern = th.queryPattern.filter(
            (t) => t.pattern !== x.label
          )
        }
      })
    }
  },
  computed: {
    scrollerHeight: function() {
      return window.innerHeight - 250 + 'px'
    }
  },
  created() {
    this.query = {
      patientId: this.$route.query && this.$route.query.patientId,
      visitId: this.$route.query && this.$route.query.visitId,
      empNo: this.$route.query && this.$route.query.empNo,
      visitNo: this.$route.query && this.$route.query.visitNo
    }

    this.patientInfo.patientId =
      this.$route.query && this.$route.query.patientId
    this.patientInfo.visitId = this.$route.query && this.$route.query.visitId
    this.patientInfo.empNo = this.$route.query && this.$route.query.empNo

    this.pageQuery.patientId = this.query.patientId
    this.type = this.$route.query && this.$route.query.type
    this.patientId = this.$route.query && this.$route.query.patientId
    this.visitId = this.$route.query && this.$route.query.visitId
    this.querySubmit.type = this.$route.query && this.$route.query.type
    this.querySubmit.visitNo = this.$route.query && this.$route.query.visitNo
    this.timeCreate()
    this.getExamSelectTreeList()
    this.getExamAppointmentRegisterList()
    this.getTree()
    this.getPositionTree()
    this.getSpecialItemList()
    this.getVerifyCdssCntrol();

    // this.query.patientId = this.$route.query && this.$route.query.patientId;
    // this.query.visitId = this.$route.query && this.$route.query.visitId;
    // this.query.empNo = this.$route.query && this.$route.query.empNo;
  }
}
</script>

<style scoped>
.bg-image-button {
  background-image: url("../../assets/images/appoint.png");
  /* 替换为你的图片路径 */
  background-size: cover;
  /* 背景图片覆盖整个按钮 */
  background-position: center;
  /* 背景图片居中 */
  border: none;
  /* 移除边框 */
  /* 其他样式，如宽度、高度、边距等 */
  width: 25px;
  height: 25px;
}

.home {
  padding: 0;
  border: 2px solid #b0b1b0;
  min-width: 1014px;
  min-height: 768px;
  position: relative;
  margin: 0 auto;
}

.home::after {
  content: "";
  clear: both;
  display: block;
}

.time-select {
  margin-top: 5px;
  height: 300px;
  background: #ffffff;

  ::v-deep.el-radio-button--medium .el-radio-button__inner {
    font-size: 14px;
    border-radius: 0;
  }

  ::v-deep.el-radio-button__orig-radio:disabled + .el-radio-button__inner {
    color: black;
  }

  div {
    margin-top: 0.5%;
  }

  .select-one {
    height: 15%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .select-one-type {
    }

    .select-one-source {
      width: 40%;

      ::v-deep.el-radio-button__orig-radio:disabled:checked + .el-radio-button__inner {
        background-color: #1890ff;
        color: #ffffff;
      }
    }
  }

  .select-two {
    min-height: 15%;
    max-height: 36%;
  }

  .select-two-one {
    ::v-deep.el-radio-button--medium .el-radio-button__inner {
      font-size: 18px !important;
      border-radius: 0;
      font-weight: 600;
    }
  }

  .select-three {
    height: 15%;
  }

  .select-four {
    height: 50%;
    display: flex;

    :hover {
      cursor: pointer;
      color: #ffffff;
      box-shadow: 0 0 0 0 grey;
      transform: scale(1.1);
      background: #1890ff !important;
    }

    .four-item {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      text-align: center;
      width: 10%;
      height: 80%;
      background: #edf4fc;
      margin: 1%;

      .item-son {
        transform: scale(1) !important;
      }
    }
  }

  .select-five {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5%;
  }
}

.TableText {
  background-color: #1c84c6;
  color: #f2f2f2;
  height: 3%;
  font-size: 16px;
  margin: 0 auto;
  text-align: center;
}

.MyTable {
  width: 18%;
  border: 1px solid saddlebrown;
  float: left;
  height: 99%;
}

.my-table-col {
  border: 1px solid #00a19b;
  float: left;
  width: 100%;
}

my-advice-button {
  float: right;
  margin-right: 30px;
}

.my-table-text {
  font-size: 12px;
  color: #3a5fcd;
  font-family: PingFang SC;
}

.my-table-text:hover {
  color: #1e47a1 !important;
  border-bottom: 1px solid #1e47a1;
  cursor: pointer;
}

.my-table-el-col {
  font-size: 12px;
}

.my-table-page {
  /*width: 60%;*/
}

.my-table-button {
  margin-left: 80%;
  margin-top: -13%;
  float: right;
  margin-right: 3%;
  position: relative;
}

.MyForm1 {
  margin-top: 2%;
  margin-left: 5%;
  margin-bottom: 10px;
  height: 11%;
}

.my-table {
  margin-left: 1%;
  border: 1px solid #00afff;
  height: 84%;
}

::v-deep .el-form-item--mini.el-form-item {
  margin-bottom: 2px;
}

/deep/ .el-input--mini .el-input__inner {
  height: 26px;
  line-height: 28px;
}

::v-deep .el-input {
  width: 70%;
}

/deep/ .el-checkbox {
  font-size: 10px !important;
}

::v-deep .el-form-item__label {
  font-size: 12px;
  padding: 0px 5px 0 0;
}

.MyCentre {
  float: left;
  width: 82%;
  height: 99.5%;
}

.MyUser {
  border: 1px solid slateblue;
  width: 99%;
  height: 5.5%;
  margin-top: 0.2%;
}

.My-el-avatar {
  margin-left: 1%;
  margin-outside: 1%;
  margin-top: 0.1%;
  float: left;
}

.my-user-text {
  margin-top: 0.5%;
}

.my-text {
  margin-left: 1%;
}

.MyTree {
  margin-left: 1%;
  margin-top: 1%;
  border: 1px solid darkslateblue;
  width: 20%;
  height: 92%;
  float: left;
}

.MyExam {
  margin-top: 1%;
  float: left;
  border: 1px solid midnightblue;
  margin-left: 1%;
  width: 49%;
  height: 86%;
}

.my-header-text {
  border: 1px solid #f9f9fa;
  background-color: #f9f9fa;
  text-align: center;
  font-size: 14px;
  letter-spacing: 10px;
  color: #1b2947;
  font-weight: bolder;
}

.MyExam1 {
  height: 48%;
  width: 100%;
  border: 1px solid black;
}

.myExam1-text {
  float: left;
  margin-left: 20px;
  margin-top: 1px;
  margin-right: 5px;
}

.myExam1-text-img {
  width: 17px;
}

.myExam1-text:hover {
  cursor: pointer;
  transform: scale(1.6);
}

.MyExam2 {
  height: 32%;
  width: 100%;
  border: 1px solid black;

  ::v-deep.el-table .cell {
    line-height: 12px;
  }
}

.MyExam3 {
  height: 20%;
  width: 100%;
  border: 1px solid black;
}

.MySketch {
  margin-top: 1%;
  float: left;
  border: 1px solid midnightblue;
  margin-left: 1%;
  width: 28%;
  height: 86%;
}

.my-sketch-span {
  font-size: 12px;
  font-weight: bolder;
  margin-left: 2%;
}

.my-sketch-input {
}

.my-bottom {
  float: right;
  width: 77%;
  height: 6%;
  border: 1px solid #5ac725;
  margin-right: 1%;
  margin-top: 2px;
  text-align: center;
}

.mu-bottom-text {
  font-size: 32px;
  color: red;
  font-weight: 700;
}

.my-bottom-button {
  margin-left: 23%;
  width: 15%;
  height: 5%;
  font-size: 26px !important;
}

.my-drawer {
  margin-top: 0.1%;
  border: 1px solid #5ac725;
  height: 90%;
  width: 99%;
}

.my-drawer-table {
  width: 68%;
  height: 85%;
  border: 1px solid #3a5fcd;
  float: left;
  position: relative;
}

.my-drawer-in {
  margin-left: 1%;
  width: 31%;
  height: 85%;
  border: 1px solid #3a5fcd;
  float: left;
  position: relative;
}

.my-drawer-text {
  margin-top: 0.4%;
  width: 100%;
  height: 14.3%;
  border: 1px solid #3a5fcd;
  float: left;
}

.my-drawer-text-son {
  margin-top: 3.5%;
  margin-left: 25%;
  color: red;
  font-weight: bolder;
  font-size: 32px;
}

::v-deep.is-horizontal {
  display: none !important;
}

::v-deep.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #00afff;
}

::v-deep.el-textarea {
  position: relative;
  width: 95%;
  vertical-align: bottom;
  font-weight: 1500;
  font-size: 12px;
  margin-left: 1.5%;
  border: 1px solid #00afff;
  border-radius: 4px;
}

/deep/ .el-scrollbar__wrap {
  overflow-x: hidden !important;
  height: 98%;
}

/deep/ .el-checkbox__label {
  display: inline-grid;
  font-size: 12px !important;
  white-space: pre-line;
  word-wrap: break-word;
  line-height: 20px;
}

/deep/ .el-textarea.is-disabled .el-textarea__inner {
  background-color: #ffffff;
  border-color: #e4e7ed;
  color: black;
  cursor: not-allowed;
}

/deep/ .pagination-container {
  background: #fff;
  padding: 0px !important;
  height: 0px;
  margin-top: 0px;
}

/deep/ .el-pagination .el-select .el-input .el-input__inner {
  padding-right: 0px;
  border-radius: 0px;
}

/deep/ .el-pagination {
  font-size: 10px;
  white-space: nowrap;
  color: #303133;
  font-weight: 600;
  float: left;
  position: relative;
}

/deep/ .el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next,
.el-pagination.is-background .el-pager li {
  margin: 0px 1px;
  background-color: #f4f4f5;
  color: #606266;
  min-width: 10px;
  border-radius: 2px;
}

/deep/ .el-pager li {
  padding: 0 4px;
  min-width: 10px !important;
  background: #ffffff;
  vertical-align: top;
  display: inline-block;
  font-size: 10px;
  height: 27px;
  line-height: 28px;
  cursor: pointer;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  text-align: center;
  margin: 0;
}

.el-pagination .btn-prev .el-icon,
.el-pagination .btn-next .el-icon {
  min-width: 10px !important;
  display: block;
  font-size: 12px;
  font-weight: bold;
}

/deep/ .el-drawer__header {
  margin-bottom: 0px;
  text-align: center;
  background-color: #9feae3;
  color: black;
  font-weight: 800;
  font-size: 18px;
  padding: 5px;
}

::v-deep.el-message__content {
  padding: 0;
  font-size: 24px;
  line-height: 1;
}

/deep/ .el-tree-node__label {
  font-size: 12px;
}

/deep/ .el-tree-node__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 22px;
  cursor: pointer;
}

/deep/ .el-transfer-panel__item .el-checkbox__input {
  position: absolute;
  top: 8px;
  /* left: 12px; */
  margin-left: -45px;
}

/deep/ .el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label span {
  position: absolute;
  right: 15px;
  color: #909399;
  font-size: 12px;
  font-weight: normal;
  top: 10px;
}

/deep/ .el-transfer-panel__body {
  height: 450px;
}

/deep/ .el-input {
  width: 86%;
}

/deep/ .el-table .el-table__header-wrapper th {
  word-break: break-word;
  background-color: #f8f8f9;
  color: #515a6e;
  height: 20px;
  font-size: 10px;
}

/deep/ .el-table--border .el-table__cell:first-child .cell {
  padding-left: 0;
}

/deep/ .el-table--mini .el-table__cell {
  padding: 1px;
}

/deep/ .el-table--mini {
  font-size: 10px;
}

/deep/ .el-input--medium .el-input__inner {
  height: 30px;
  line-height: 36px;
  font-size: 12px;
  text-align: center;
}

/deep/ .el-input__inner {
  padding: 0;
}

/deep/ .el-input__icon {
  width: 12px;
}

/deep/ .el-table .cell {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  line-height: 23px;
  padding: 0;
}

/deep/ .el-button--medium {
  padding: 5px 0px;
  font-size: 20px;
  border-radius: 4px;
}

/deep/ .el-button + .el-button {
  margin-left: 1px;
  margin-right: 1px;
}

/deep/ .el-button--mini {
  padding: 0;
  font-size: 10px;
  border-radius: 1px;
}

::v-deep.el-select-dropdown__item {
  font-size: 12px;
  padding: 0px 20px;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #606266;
  height: 24px;
  line-height: 34px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
}

/deep/ .el-input--medium .el-input__icon {
  line-height: 25px;
}

.my-dialog-cseHistory {
  display: flex;
}

.el-menu-vertical-demo-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

/deep/ .el-dialog__header {
  margin-bottom: -25px;
  text-align: center;
  background-color: #9feae3;
  color: black;
  font-weight: 800;
  font-size: 18px;
  padding: 5px;
}

.my-dialog-cseHistory-button {
  display: flex;
  justify-content: space-evenly;
}

/deep/ .el-menu-item.is-active {
  color: black;
}

/*/deep/.el-input--mini .el-input__inner {*/
/*  height: 28px;*/
/*  line-height: 28px;*/
/*}*/
/*::v-deep.el-row {*/
/*  position: absolute;*/
/*  -webkit-box-sizing: border-box;*/
/*  box-sizing: border-box;*/
/*}*/
</style>
<style lang="scss">
//日期选择器
.el-date-picker {
  width: 33%;
}

.my-dialog-2 {
}

.el-date-picker .el-picker-panel__content {
  width: 90%;
}

.timeLineItem {
  position: relative;

  span {
    position: absolute;
    margin-left: -55px;
    margin-top: 2px;
  }
}

.date-my {
}

.timeline {
  margin-left: 30%;
  margin-top: 10%;
}

.center-text {
  display: flex;
  justify-content: center;
  align-items: center;
}

.pdm-header {
  position: fixed;
  bottom: 1px;
  right: 50px;
}
</style>
