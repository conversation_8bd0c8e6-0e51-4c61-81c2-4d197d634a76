<template>
  <div class="in-master">
    <div class="in-form">
      <el-form ref="form" :model="queueForm" :inline="true">
        <el-form-item label="ID号：">
          <el-input v-model="queueForm.patientId" style="width: 120px;"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="formSelectClick">提取</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="in-item">
      <div>
        <div class="in-title">医嘱信息</div>
        <div class="in-table">
          <el-table :data="orderMaster" border style="width: 100%" height="220" highlight-current-row>
            <el-table-column align="center" prop="patienT_ID" label="ID号" width="95"></el-table-column>
            <el-table-column align="center" prop="visiT_ID" label="住院次数" width="80"></el-table-column>
            <el-table-column align="center" prop="ordeR_NO" label="医嘱号" width="65"></el-table-column>
            <el-table-column align="center" prop="ordeR_TEXT" label="项目名称" width="300"></el-table-column>
            <el-table-column align="center" prop="ordeR_CODE" label="项目代码" width="150"></el-table-column>
            <el-table-column align="center" prop="administration" label="途径" width="90"></el-table-column>
            <el-table-column align="center" prop="" label="计价属性" width="80"></el-table-column>
            <el-table-column align="center" prop="dosage" label="剂量" width="55"></el-table-column>
            <el-table-column align="center" prop="frequency" label="计量单位" width="90"></el-table-column>
            <el-table-column align="center" prop="enteR_DATE_TIME" label="开单时间" width="145"></el-table-column>
            <el-table-column align="center" prop="frequency" label="频次" width="65"></el-table-column>
            <el-table-column align="center" prop="orderinG_DEPT_NAME" label="开单科室" width="100"></el-table-column>
            <el-table-column align="center" prop="doctor" label="医生" width="80"></el-table-column>
            <el-table-column align="center" prop="ordeR_STATUS" width="80" label="状态" >
              <template slot-scope="scope">
                <div v-if="scope.row.ordeR_STATUS === '1'">新开</div>
                <div v-else-if="scope.row.ordeR_STATUS === '2'">执行</div>
                <div v-else-if="scope.row.ordeR_STATUS === '3'">停止</div>
                <div v-else-if="scope.row.ordeR_STATUS === '4'">作废</div>
                <div v-else-if="scope.row.ordeR_STATUS === '5'">医生保存</div>
                <div v-else-if="scope.row.ordeR_STATUS === '6'">医生提交</div>
                <div v-else-if="scope.row.ordeR_STATUS === '7'">医生停止</div>
                <div v-else-if="scope.row.ordeR_STATUS === '8'">医生作废</div>
              </template>
            </el-table-column  >
            <el-table-column align="center" prop="lasT_PERFORM_DATE_TIME" width="145" label="摆药时间" ></el-table-column>
          </el-table>
        </div>
      </div>
      <div>
        <div class="in-title">收费记录</div>
        <div class="in-table">
          <el-table :data="chargeMaster" border style="width: 100%" height="220" highlight-current-row>
            <el-table-column align="center" prop="patienT_ID" label="ID号" width="95"></el-table-column>
            <el-table-column align="center" prop="visiT_ID" label="住院次数" width="80"></el-table-column>
            <el-table-column align="center" prop="iteM_NAME" label="项目名称" width="300"></el-table-column>
            <el-table-column align="center" prop="iteM_CODE" label="项目代码" width="150"></el-table-column>
            <el-table-column align="center" prop="iteM_SPEC" label="规格" width="200"></el-table-column>
            <el-table-column align="center" prop="amount" label="数量" width="75" ></el-table-column>
            <el-table-column align="center" prop="units" label="单位" width="75"></el-table-column>
            <el-table-column align="center" prop="ordereD_BY_NAME" label="开单科室" width="140"></el-table-column>
            <el-table-column align="center" prop="performeD_BY_NAME" label="执行科室" width="140"></el-table-column>
            <el-table-column align="center" prop="charges" label="金额"  width="100"></el-table-column>
            <el-table-column align="center" prop="operatoR_NO_NAME" label="计费人" width="120" ></el-table-column>
            <el-table-column align="center" prop="doctoR_USER_NAME" label="医生" width="120"></el-table-column>
          </el-table>
        </div>
      </div>
      <div>
        <div class="in-title">摆药记录</div>
        <div class="in-table">
          <el-table :data="dispenseMedicine" border style="width: 100%" height="260" highlight-current-row>
            <el-table-column align="center" prop="dispensarY_NAME" label="药房" width="80"></el-table-column>
            <el-table-column align="center" prop="druG_NAME" label="药品名称" width="300"></el-table-column>
            <el-table-column align="center" prop="dispensinG_DATE_TIME" label="摆药时间"  width="160"></el-table-column>
            <el-table-column align="center" prop="ordereD_BY_NAME" label="申请科室" width="130"></el-table-column>
            <el-table-column align="center" prop="druG_SPEC" label="规格" width="130"></el-table-column>
            <el-table-column align="center" prop="druG_UNITS" label="单位" width="80"></el-table-column>
            <el-table-column align="center" prop="firM_ID" label="厂家"  width="130"></el-table-column>
            <el-table-column align="center" prop="dispensE_AMOUNT" label="摆药数量"  width="65"></el-table-column>
            <el-table-column align="center" prop="dispensinG_PROVIDER" label="摆药医师"  width="90"></el-table-column>
            <el-table-column align="center" prop="charges" label="金额" width="100"></el-table-column>
            <el-table-column align="center" prop="chargE_INDICATOR" label="计价标识"  width="85"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { GetinInitializeMsg,} from "@/api/checkAndConfirm/imageBillingRecord"
export default {
  name: 'inRecord',
  props: ['queueForm'],
  components: {},
  data() {
    return {
      orderMaster: [],
      chargeMaster: [],
      dispenseMedicine: [],
    }
  },
  created() {
    this.formSelectClick();
  },
  mounted() {
  },
  methods: {
    formSelectClick(){
      const loading = this.$loading({
        lock: true,
        text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      GetinInitializeMsg(this.queueForm.patientId).then(res => {
        if (res.code === 200){
          this.orderMaster = res.data.orderMaster
          this.chargeMaster = res.data.chargeMaster
          this.dispenseMedicine = res.data.dispenseMedicine
        }
      }).finally(() => {
        loading.close();
      })
    },
  }
}
</script>

<style scoped lang="scss">
.in-master{
  border: 1px solid #00a19b;
  border-radius: 10px;
  .in-form{
    margin-top: 2px;
    margin-left: 10px;
    .form-flex {
      display: flex;
    }

    .date-f {
      padding: 0 5px;
    }
    ::v-deep.el-form-item {
      margin-bottom: 0;
    }
  }
  .in-item{
    .in-title{
      height: 30px;
      border: 1px solid #00a19b;
      background-color: #185F7D;
      color: #FFFFFF;
      font-size: 20px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }
    .in-table{
      ::v-deep.el-table--medium .el-table__cell {
        padding: 3px 0;
      }
      ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
        height: 30px;
        font-size: 14px;
      }
      ::v-deep.el-table th.el-table__cell > .cell {
        padding-left: 2px;
        padding-right: 2px;
      }
      ::v-deep.el-table .cell {
        padding-left: 2px;
        padding-right: 2px;
      }
      ::-webkit-scrollbar {
        width: 11px;
        height: 11px;
      }
      ::-webkit-scrollbar-thumb {
        background-color: #6bcaaf;
        border-radius: 10px;
      }
      ::v-deep.el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
        background-color: #1890FF;
        color: #FFFFFF;
      }
    }
  }
}
</style>
