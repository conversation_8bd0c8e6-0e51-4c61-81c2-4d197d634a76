<template>
  <div>
    <div class="result-master">
      <div class="result-title">检查时间：</div>
      <div class="result-text">{{result.REPORT_DATE_TIME}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">病人姓名：</div>
      <div class="result-text">{{result.NAME}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">性 别：</div>
      <div class="result-text">{{result.SEX}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">联系电话：</div>
      <div class="result-text">{{result.PHONE_NUMBER_HOME}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">家庭住址：</div>
      <div class="result-text">{{result.MAILING_ADDRESS}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">费 别：</div>
      <div class="result-text">{{result.CHARGE_TYPE}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">病 人 ID：</div>
      <div class="result-text">{{result.PATIENT_ID}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">出生日期：</div>
      <div class="result-text">{{result.DATE_OF_BIRTH}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">检 查 号：</div>
      <div class="result-text">{{result.EXAM_NO}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">检查项目：</div>
      <div class="result-text">{{result.EXAM_CLASS}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">检查子类：</div>
      <div class="result-text">{{result.EXAM_SUB_CLASS}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">临床诊断：</div>
      <div class="result-text">{{result.CLIN_DIAG}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">检查诊断：</div>
      <div class="result-text">{{result.DESCRIPTION}}</div>
    </div>
    <div class="result-master">
      <div class="result-title">开单医生：</div>
      <div class="result-text">{{result.REQ_PHYSICIAN}}</div>
    </div>
  </div>
</template>

<script>
import {getTuberculosisStatementParticulars} from "@/api/singlePage/tuberculosisStatement"
export default {
  name: 'tuberculosResult',
  props: ['rowData', 'maxHeight'],
  components: {},
  data() {
    return {
      size: '',
      result: {},
    }
  },
  created() {
    this.getTuberculosisStatementPart();
  },
  mounted() {
  },
  methods: {
    getTuberculosisStatementPart(){
      getTuberculosisStatementParticulars(this.rowData.EXAM_NO).then(res => {
        this.result = res.data;
      })
    },
  }
}
</script>

<style scoped lang="scss">
.result-master {
  border: 1px solid #3A71A8;
  color: black;
  display: flex;
  min-height: 40px;

  .result-title {
    font-size: 18px;
    width: 30%;
    border-right: 1px solid #3A71A8;
    background-color: #EFF3FB;
    display: flex;
    align-items: center;
    padding: 0.3em;
  }

  .result-text {
    font-size: 16px;
    width: 70%;
    display: flex;
    align-items: center;
    padding: 0.3em;
  }
  @media screen and (max-width: 1250px) {
    .result-title {
      font-size: 17px;
    }

    .result-text {
      font-size: 15px;
    }
  }
  @media screen and (max-width: 1100px) {
    .result-title {
      font-size: 16px;
    }

    .result-text {
      font-size: 14px;
    }
  }
  @media screen and (max-width: 600px) {
    .result-title {
      font-size: 15px;
    }

    .result-text {
      font-size: 13px;
    }
  }
}
</style>
