<template>
    <div :style="maxHeight">
        <el-scrollbar style="height: 98%; width: 100%; overflow-x: hidden">
            <!-- 新生儿详情信息 -->
            <el-descriptions class="margin-top" title="新生儿明细" :column="3" :size="size" border>
                <el-descriptions-item>
                    <template slot="label">
                        临时姓名
                    </template>
                    <span class="span">
                        {{ sonData.name }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        性别
                    </template>
                    <span class="span">
                        {{ sonData.sex }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        民族
                    </template>
                    <span class="span">
                        {{ sonData.natioN_OF_FATHER }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        出生时间
                    </template>
                    <span class="span">
                        {{ sonData.datE_OF_BIRTH }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        健康状况
                    </template>
                    <span class="span">
                        {{ sonData.healtH_STATUS }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        分娩结果
                    </template>
                    <span class="span">
                        {{ sonData.birtH_QUOMODO }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        出生孕周
                    </template>
                    <span class="span">
                        {{ sonData.gestatioN_WEEK }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        体重
                    </template>
                    <span class="span">
                        {{ sonData.weight }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        身长
                    </template>
                    <span class="span">
                        {{ sonData.height }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        分娩方式
                    </template>
                    <span class="span">
                        {{ sonData.birtH_RESULT }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        疫苗1
                    </template>
                    <span class="span">
                        {{ sonData.drug1 }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        疫苗2
                    </template>
                    <span class="span">
                        {{ sonData.drug2 }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        母亲姓名
                    </template>
                    <span class="span">
                        {{ sonData.namE_OF_MOTHER }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        母亲年龄
                    </template>
                    <span class="span">
                        {{ sonData.agE_OF_MOTHER }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        母亲身份证
                    </template>
                    <span class="span">
                        {{ sonData.iD_NO_OF_MOTHER }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        父亲姓名
                    </template>
                    <span class="span">
                        {{ sonData.namE_OF_FATHER }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        父亲年龄
                    </template>
                    <span class="span">
                        {{ sonData.agE_OF_FATHER }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        父亲身份证
                    </template>
                    <span class="span">
                        {{ sonData.iD_NO_OF_FATHER }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        出生证号
                    </template>
                    <span class="span">
                        {{ sonData.birtH_CERTIFICATION_NO }}
                    </span>
                </el-descriptions-item>
                <el-descriptions-item>
                    <template slot="label">
                        家庭住址
                    </template>
                    <span class="span">
                        {{ montherData1.mailinG_ADDRESS }}
                    </span>
                </el-descriptions-item>
            </el-descriptions>
            <div class="span">
                分娩结果提示->A:足月,B:早产
            </div>
            <div class="span">
                分娩方式提示->A:剖腹产,B:顺产,C:产钳,D:胎吸,E:稀氏法,F:邓氏法,G:人工剥离,
            </div>
            <!-- 母亲住院信息 -->
            <div style="margin-top: 20px;">
                <el-descriptions class="margin-top" title="母亲住院信息" :column="3" :size="size" border>
                    <el-descriptions-item>
                        <template slot="label">
                            病人ID
                        </template>
                        <span class="span">
                            {{ sonData.patienT_ID_OF_MOTHER }}
                        </span>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            姓名
                        </template>
                        <span class="span">
                            {{ sonData.namE_OF_MOTHER }}
                        </span>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            民族
                        </template>
                        <span class="span">
                            {{ sonData.natioN_OF_MOTHER }}
                        </span>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            入院时间
                        </template>
                        <span class="span">
                            {{ montherData1.admissioN_DATE_TIME }}
                        </span>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            出院时间
                        </template>
                        <span class="span">
                            {{ montherData1.dischargE_DATE_TIME }}
                        </span>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            主治医师
                        </template>
                        <span class="span">
                            {{ montherData1.attendinG_DOCTOR }}
                        </span>
                    </el-descriptions-item>
                </el-descriptions>
                <el-descriptions class="margin-top" :column="1" :size="size" border>
                    <el-descriptions-item>
                        <template slot="label">
                            诊断
                        </template>
                        <span class="span">
                            {{ montherData2.diagnosiS_DESC }}
                        </span>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            检查类别
                        </template>
                        <span class="span">
                            {{ montherData3.exaM_CLASS }}
                        </span>
                    </el-descriptions-item>
                    <el-descriptions-item>
                        <template slot="label">
                            检查结果
                        </template>
                        <span class="span">
                            {{ montherData3.cliN_DIAG }}
                        </span>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
        </el-scrollbar>
    </div>
</template>

<script>
import {
    GetNewbornDetail
} from "@/api/singlePage/newbornStatistics";
export default {
    name: 'dialogOne',
    // 继承父组件弹框中的高度
    props: ['rowData', 'maxHeight'],
    data() {
        return {
            size: '',
            queueForm: {},
            sonData: {},
            montherData1: {},
            montherData2: {},
            montherData3: {}
        }
    },

    mounted() { },

    created() {
        this.getDetail();
    },

    methods: {
        getDetail() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            this.queueForm.patientId = this.rowData.patienT_ID;// 新生儿患者ID
            this.queueForm.patientIdMonther = this.rowData.patienT_ID_OF_MOTHER;// 对应母亲的患者ID
            this.queueForm.visitId = this.rowData.visiT_ID_OF_MOTHER;// 对应母亲的住院次数
            GetNewbornDetail(this.queueForm).then(res => {
                // 新生儿详情信息
                if (res.data.listSon.length > 0) {
                    this.sonData = res.data.listSon[0];
                }
                // 新生儿对应的母亲详情信息
                if (res.data.listMonther1.length > 0) {
                    this.montherData1 = res.data.listMonther1[0];
                }
                if (res.data.listMonther2.length > 0) {
                    this.montherData2 = res.data.listMonther2[0];
                }
                if (res.data.listMonther3.length > 0) {
                    this.montherData3 = res.data.listMonther3[0];
                }
            }).finally(() => {
                loading.close();
            });
        },

        // 提示信息封装
        showMessageA(message) {
            const h = this.$createElement;
            this.$notify({
                title: '提示信息！',
                message: h('i', { style: 'color: teal' }, message)
            });
        },
    }
}
</script>

<style>
.span {
    color: red;
}
</style>