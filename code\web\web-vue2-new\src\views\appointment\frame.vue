<template>
  <div>
    <el-row>
      <el-col :span="5" :offset="1">
        <br />
        <!--      检查项目树结构展示-->
        <div class="MyTree">
          <div
            class="my-header-text"
            style="text-align: left; margin-left: 2%;"
          >
            <el-tag>检查项目</el-tag>
          </div>
          <!-- <div class="">
            <el-input placeholder="输入关键字进行过滤" v-model="treeSearch"></el-input>
          </div> -->
          <div
            class="head-container down-tree"
            :style="{
              height: scrollerHeight,
              overflow: 'auto',
            }"
          >
            <el-tree
              :highlight-current="true"
              :data="examTree"
              :props="defaultProps"
              node-key="label"
              :default-expand-all="false"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              @node-click="getCheckedNodes"
              ref="tree"
            ></el-tree>
          </div>
        </div>
      </el-col>
      <el-col :span="18">
        <br />
        <el-row>
          <el-col :span="2" :offset="1">
            <el-tag>
              项目：{{ class_name == undefined ? '全部' : class_name }}
            </el-tag>
          </el-col>
          <el-col :span="2" :offset="2">
            <el-tag v-if="subclass_name">子项目：{{ subclass_name }}</el-tag>
          </el-col>
          <el-col :span="3" :offset="1">
            <el-button
              size="mini"
              plain
              type="success"
              @click="toAdd"
              v-if="subclass_name"
            >
              新增
            </el-button>
          </el-col>
        </el-row>

        <el-table
          :data="frameList"
          style="width: 100%; font-size: 15px;"
          stripe
          height="800"
          :header-cell-style="{ 'text-align': 'center', height: '64px' }"
          :cell-style="{ 'text-align': 'center' }"
        >
          <el-table-column
            :show-overflow-tooltip="true"
            label="序号"
            align="center"
            min-width="20"
          >
            <template slot-scope="scop">
              {{ scop.$index + 1 }}
            </template>
          </el-table-column>

          <el-table-column
            :show-overflow-tooltip="true"
            prop="exam_class_name"
            label="项目"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            prop="exam_subclass_name"
            label="子项目"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            prop="time_frame"
            label="分配时段"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            prop="max"
            label="分配人数"
          ></el-table-column>

          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                size="small"
                plain
                type="warning"
                @click="toUpdown(scope.row)"
              >
                修改
              </el-button>
              <el-button
                size="small"
                plain
                type="danger"
                @click="toDelete(scope.row)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <!-- 添加或者修改 -->
    <el-dialog
      :visible.sync="addDetailDialog"
      width="800"
      append-to-body
      size="50%"
    >
      <el-form :model="detailModel" label-width="85px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分配工作量" prop="orderNum">
              <el-input-number
                v-model="detailModel.max"
                controls-position="right"
                :min="1"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="选择时段">
              <el-select
                clearable
                v-model="detailModel.id"
                placeholder=""
                style="width: 39%;"
              >
                <el-option
                  v-for="item in frameInfo"
                  :key="item.id"
                  :value="item.id"
                  :label="item.timE_FRAME"
                >
                  {{ item.timE_FRAME }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button plain @click="setDetail">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  GainAppointment,
  GetFrameDetail,
  DeleFrameDetail,
  GetFrameInfo,
  SetFrameDetail,
} from '@/api/appointment/frame'
export default {
  data() {
    return {
      detailModel: {
        MAX: 20,
        EXAM_CLASS_NAME: undefined,
        EXAM_SUBCLASS_NAME: undefined,
        TIME_ID: undefined,
      },
      addDetailDialog: false,
      class_name: undefined,
      subclass_name: undefined,

      treeSearch: '',
      begetterExamName: '',
      sonExamName: '',
      defaultProps: {
        children: 'children',
        label: 'label',
      },

      examTree: [],
      frameList: [],
      frameInfo: [],
    }
  },

  computed: {
    scrollerHeight: function () {
      return window.innerHeight - 200 + 'px'
    },
  },

  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
  },

  created() {
    if (!this.$route.query.dept_code || !this.$route.query.EmpNo) {
      this.$message({
        message: '参数错误',
        type: 'error',
      })
      return
    }
    this.dept_code = this.$route.query.dept_code
    this.EmpNo = this.$route.query.EmpNo
    this.gainFrameInfo()
    this.getTree()
  },
  mounted() {},
  methods: {
    gainFrameInfo() {
      let param = {}
      GetFrameInfo(param).then((res) => {
        this.frameInfo = res.data
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    gainDetail() {
      let param = {
        class_name: this.class_name,
        subclass_name: this.subclass_name,
      }

      GetFrameDetail(param).then((res) => {
        if (res.code == 200) {
          this.frameList = res.data
          // this.$message({
          //   message: res.message,
          //   type: 'success',
          // })
        }
      })
    },
    //左侧检查项目 树结构点击 回调
    getCheckedNodes() {
      //1·获取点击节点详情
      let v = this.$refs.tree.getCurrentNode()
      // 2·判断节点点击是否为空

      if (v !== null) {
        if (v.value !== null && v.label !== null) {
          // 3·子节点点击
          this.subclass_name = v.label
          this.class_name = v.value
        }
        if (v.value === null && v.label !== null) {
          //父节点
          this.class_name = v.label
          this.subclass_name = undefined
        }
        this.gainDetail()
      }
    },
    //获取检查项目下拉树结构
    getTree() {
      let query = {
        EmpNo: this.EmpNo,
        dept_code: this.dept_code,
      }
      console.log(query)
      GainAppointment(query).then((res) => {
        this.examTree = res.data.treeList
      })
    },
    toDelete(row) {
      let param = {
        detail_id: row.detail_id,
      }
      DeleFrameDetail(param).then((res) => {
        if (res.code == 200) {
          this.gainDetail()
          //   this.$message({
          //     message: res.message,
          //     type: 'success',
          //   })
        }
      })
    },
    toUpdown(row) {
      this.detailModel = row
      this.addDetailDialog = true
    },
    toAdd() {
      this.detailModel = {}
      this.detailModel.EXAM_SUBCLASS_NAME = this.subclass_name
      this.detailModel.EXAM_CLASS_NAME = this.class_name
      this.addDetailDialog = true
    },
    setDetail() {
      if (!this.detailModel.id) {
        this.$message({
          message: '请选择时段信息',
          type: 'error',
        })
        return
      }
      this.detailModel.EXAM_SUBCLASS_NAME = this.subclass_name
      this.detailModel.EXAM_CLASS_NAME = this.class_name
      this.detailModel.EMP_NO = this.$route.query.EmpNo
      console.log(this.detailModel)
      SetFrameDetail(this.detailModel).then((res) => {
        this.gainDetail()
        this.addDetailDialog = false
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.down-tree {
  height: 680px;
  display: block;
  overflow-y: scroll;
}
</style>
