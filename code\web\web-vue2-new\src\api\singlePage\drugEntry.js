import request from '@/utils/request2'

export function getDrugEntryList(data) {
  return request({
    url: '/singlePage/drugEntry/inquire',
    method: 'post',
    data: data
  })
}

export function getDrugEntryParticulars(documentNo,type) {
  return request({
    url: '/singlePage/drugEntry/particulars/' + documentNo + '/' + type,
    method: 'post',
  })
}

export function saveDrugEntry(data) {
  return request({
    url: '/singlePage/drugEntry/save',
    method: 'post',
    data: data
  })
}
