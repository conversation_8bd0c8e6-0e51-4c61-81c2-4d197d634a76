<template>
  <div>
    <el-radio-group v-model="groupData" @change="triageMonitor">
      <el-radio :label="1">全部</el-radio>
      <el-radio :label="2">未分诊</el-radio>
      <el-radio :label="3">已分诊</el-radio>
    </el-radio-group>
    <div style="margin-top: 10px;">
      <el-radio-group v-model="diagData" @change="diagMonitor">
        <el-radio :label="1">全部</el-radio>
        <el-radio :label="2">有诊断</el-radio>
        <el-radio :label="3">无诊断</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'triageParticulars',
  props: [],
  components: {},
  data() {
    return {
      groupData: '',
      diagData: '',
    }
  },
  created() {
    this.groupData = this.$store.getters.patient.type;
    this.diagData = this.$store.getters.patient.diagType;
  },
  mounted() {
  },
  methods: {
    triageMonitor(data){
      this.$store.getters.patient.type = data;
    },
    diagMonitor(data){
      this.$store.getters.patient.diagType = data;
    },
  }
}
</script>

<style scoped lang="scss">

</style>
