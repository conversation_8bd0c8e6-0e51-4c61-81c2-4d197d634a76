<template>
  <div class="single-master">
    <div class="single-title">传染病报告卡审核</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="开始时间:">
              <el-date-picker v-model="queueForm.beginDate" type="date" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker v-model="queueForm.endDate" type="date" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="ID号:">
              <el-input v-model="queueForm.patientId" placeholder="请输入患者id" clearable></el-input>
            </el-form-item>
            <el-form-item label="患者姓名:">
              <el-input v-model="queueForm.patientName" placeholder="请输入患者姓名" clearable></el-input>
            </el-form-item>
            <el-form-item label="报卡状态:">
              <el-select v-model="queueForm.status" clearable filterable placeholder="请选择">
                <el-option v-for=" (item, index) in optionsStatus " :key="item.index" :label="item.value"
                  :value="item.label" />
              </el-select>
            </el-form-item>
            <el-form-item label="临床/医技:">
              <el-select v-model="queueForm.reportCard" clearable filterable placeholder="请选择">
                <el-option v-for=" (item, index) in options " :key="item.index" :label="item.value" :value="item.label" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <div class="element-button">
                <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
                <el-button type="primary" icon="el-icon-delete" @click="delRowdata">删除</el-button>
                <export-excel excelName="传染病登记本数据导出" :excelData="excelDate || []" :columnMap="columnMap">
                  <el-button type="primary" icon="el-icon-folder-opened" slot="trigger"
                    style="margin-left: 10%;">导出</el-button>
                </export-excel>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div>
          <table width="100%" border="0" cellpadding="0" cellspacing="1">
            <h2 class="centered">河南宏力医院</h2>
            <h3 class="centered">传染病登记本</h3>
          </table>
        </div>
        <div class="element-table">
          <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 275" highlight-current-row
            @cell-dblclick="doubleSelectionChange" @row-click="handleRowClick" :row-class-name="rowClassName">
            <el-table-column type="index" :index="indexMethod" align="center" />
            <el-table-column prop="creatE_DATE" align="center" label="日期" :formatter="formatterTime" width="90px" />
            <el-table-column prop="patienT_NAME" align="center" label="患者姓名" />
            <el-table-column prop="parenT_NAME" align="center" label="家长姓名" />
            <el-table-column prop="sex" align="center" label="性别">
              <template slot-scope="scope">
                <span v-if="scope.row.sex == '1'">男</span>
                <span v-else>女</span>
              </template>
            </el-table-column>
            <el-table-column prop="iD_NO" align="center" label="身份证号" width="160px" />
            <el-table-column prop="datE_OF_BIRTH" align="center" label="出生日期" :formatter="formatterTime" width="90px" />
            <el-table-column prop="uniT_OF_WORK" align="center" label="工作单位" width="200px" />
            <el-table-column prop="region" align="center" label="病人属于">
              <template slot-scope="scope">
                <span v-if="scope.row.region == '1'">本县区</span>
                <span v-if="scope.row.region == '2'">本市其他县区</span>
                <span v-if="scope.row.region == '3'">本省其他地市</span>
                <span v-if="scope.row.region == '4'">外省</span>
                <span v-if="scope.row.region == '5'">港澳台</span>
                <span v-if="scope.row.region == '6'">待定</span>
              </template>
            </el-table-column>
            <el-table-column prop="phonE_NUMBER" align="center" label="联系电话" width="100px" />
            <el-table-column prop="mailinG_ADDRESS" align="center" label="现住址" width="250px" />
            <el-table-column prop="occupation" align="center" label="职业" width="150px">
              <template slot-scope="scope">
                <span v-if="scope.row.occupation == '1'">幼托儿童</span>
                <span v-if="scope.row.occupation == '2'">散居儿童</span>
                <span v-if="scope.row.occupation == '3'">学生（大中小学）</span>
                <span v-if="scope.row.occupation == '4'">教师</span>
                <span v-if="scope.row.occupation == '5'">保育员及保姆</span>
                <span v-if="scope.row.occupation == '6'">餐饮食品业</span>
                <span v-if="scope.row.occupation == '7'">商业服务</span>
                <span v-if="scope.row.occupation == '8'">医务人员</span>
                <span v-if="scope.row.occupation == '9'">工人</span>
                <span v-if="scope.row.occupation == '10'">民工</span>
                <span v-if="scope.row.occupation == '11'">农民</span>
                <span v-if="scope.row.occupation == '12'">牧民</span>
                <span v-if="scope.row.occupation == '13'">渔（船）民</span>
                <span v-if="scope.row.occupation == '14'">干部职员</span>
                <span v-if="scope.row.occupation == '15'">离退人员</span>
                <span v-if="scope.row.occupation == '16'">家务及待业</span>
                <span v-if="scope.row.occupation == '17'">其他</span>
                <span v-if="scope.row.occupation == '18'">不详</span>
              </template>
            </el-table-column>
            <el-table-column prop="deseasE_TYPE_1" align="center" label="病例分类" width="120px">
              <template slot-scope="scope">
                <span v-if="scope.row.deseasE_TYPE_1 == '1'">疑似病例</span>
                <span v-if="scope.row.deseasE_TYPE_1 == '2'">临床诊断病例</span>
                <span v-if="scope.row.deseasE_TYPE_1 == '3'">确认病例</span>
                <span v-if="scope.row.deseasE_TYPE_1 == '4'">确诊病例</span>
              </template>
            </el-table-column>
            <el-table-column prop="onseT_DATE" align="center" label="发病日期" :formatter="formatterTime" width="90px" />
            <el-table-column prop="diagnosiS_DATE" align="center" label="诊断日期" :formatter="formatterTime" width="90px" />
            <el-table-column prop="deatH_DATE" align="center" label="死亡日期" :formatter="formatterTime" width="90px" />
            <el-table-column prop="carD_TYPE" align="center" label="类别">
              <template slot-scope="scope">
                <span v-if="scope.row.carD_TYPE == '1'">初次报告</span>
                <span v-if="scope.row.carD_TYPE == '2'">订正报告</span>
              </template>
            </el-table-column>
            <el-table-column prop="d" align="center" label="诊断" width="250px" />
            <el-table-column prop="doctoR_NAME" align="center" label="报告医师" />
            <el-table-column prop="deptname" align="center" label="报告科室" width="250px" />
            <el-table-column prop="creatE_DATE" align="center" label="填卡时间" :formatter="formatterTime" width="90px" />
            <el-table-column prop="remarks" align="center" label="备注" width="100px" />
            <el-table-column prop="status" align="center" label="报卡状态">
              <template slot-scope="scope">
                <span v-if="scope.row.status == '1'">待审核</span>
                <span v-if="scope.row.status == '2'">审核通过</span>
                <span v-if="scope.row.status == '3'">驳回修改</span>
              </template>
            </el-table-column>
            <el-table-column prop="checK_USER_NAME" align="center" label="审核人" />
            <el-table-column prop="checK_DATE" align="center" label="审核时间" :formatter="formatterTime" width="90px">
              <template slot-scope="scope">
                <!-- 只有当status为'2'时，也就是审核通过时，才显示审核时间 -->
                <span v-if="scope.row.status == '2'">{{ formatDate(scope.row.checK_DATE) }}</span>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div>
            <pagination v-show="total > 0" :limit.sync="queueForm.pageSize" :page.sync="queueForm.pageNum" :total="total"
              @pagination="getList" :page-sizes="[100, 500, 1000]" />
          </div>
        </div>
        <!-- DialogOne弹框 -->
        <el-dialog :visible.sync="dialogOneOpen" :title="title" width="90%">
          <dialog-one @success-listener="listenerMethod" :row-data="rowData"
            :max-height="'height:' + (tableHeight - 250) + 'px'" :key="extraFunctionKey" />
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import {
  GetReportCardReviewClinical,
  GetReportCardReviewTechnology,
  DelReportCardReview
} from "@/api/singlePage/reportCardReview";
import DialogOne from './components/dialogOne.vue' // 组件引用
import ExportExcel from "@/components/excel/exportExcel";
export default {
  name: 'reportCardReview',
  props: [],
  components: {
    DialogOne,
    ExportExcel
  },
  data() {
    return {
      queueForm: {
        pageNum: 1,
        pageSize: 100,
        beginDate: this.getThreeDaysAgo(),
        endDate: this.getToday(),
        patientId: undefined,
        reportCard: "临床科室报卡"
      },
      tableDate: [],
      total: 0,
      tableHeight: undefined,
      excelDate: [],
      rowData: {},
      dialogOneOpen: false,
      title: '中华人民共和国传染病报告卡',
      extraFunctionKey: 0,
      delData: {},
      columnMap: [
        {
          label: "日期",
          key: "creatE_DATE",
        },
        {
          label: "患者姓名",
          key: "patienT_NAME",
        },
        {
          label: "家长姓名",
          key: "parenT_NAME",
        },
        {
          label: "性别",
          key: "seX1",
        },
        {
          label: "身份证号",
          key: "iD_NO",
        },
        {
          label: "出生日期",
          key: "datE_OF_BIRTH",
        },
        {
          label: "工作单位",
          key: "uniT_OF_WORK",
        },
        {
          label: "病人属于",
          key: "regioN1",
        },
        {
          label: "联系电话",
          key: "phonE_NUMBER",
        },
        {
          label: "现住址",
          key: "currenT_ADDRESS",
        },
        {
          label: "职业",
          key: "occupatioN1",
        },
        {
          label: "病例分类",
          key: "deseasE_TYPE_1_1",
        },
        {
          label: "发病日期",
          key: "onseT_DATE",
        },
        {
          label: "诊断日期",
          key: "diagnosiS_DATE",
        },
        {
          label: "死亡日期",
          key: "deatH_DATE",
        },
        {
          label: "类别",
          key: "carD_TYPE1",
        },
        {
          label: "诊断",
          key: "zd",
        },
        {
          label: "报告医师",
          key: "doctoR_NAME",
        },
        {
          label: "报告科室",
          key: "hospitaL_NAME",
        },
        {
          label: "填卡时间",
          key: "creatE_DATE",
        },
        {
          label: "备注",
          key: "remarks",
        },
        {
          label: "报卡状态",
          key: "statuS1",
        },
        {
          label: "审核人",
          key: "checK_USER_NAME",
        },
        {
          label: "审核时间",
          key: "creatE_DATE",
        }
      ],
      options: [{
        value: '临床科室报卡',
        label: '临床科室报卡'
      }, {
        value: '医技科室报卡',
        label: '医技科室报卡'
      }],
      optionsStatus: [{
        value: '待审核',
        label: '1'
      }, {
        value: '审核通过',
        label: '2'
      }, {
        value: '驳回修改',
        label: '3'
      }],
      bodyStatus: false,
      selectedRowId: null, // 存储点击行的索引
    }
  },

  created() {
    this.emp_no = this.$route.query && this.$route.query.emp_no;
    this.getList();
    this.handleResize();
  },

  mounted() {
    this.selectedRowId = localStorage.getItem('selectedRowIdx');
    window.addEventListener('resize', this.handleResize); // 添加监听器
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize); // 移除监听器
  },

  methods: {
    // 初始化数据
    getList() {
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      if (this.queueForm.reportCard === '临床科室报卡') {
        GetReportCardReviewClinical(this.queueForm).then(res => {
          this.tableDate = res.data.list;
          this.excelDate = res.data.excelList;
          this.total = res.data.total[0].total;
        }).finally(() => {
          loading.close();
        });
      } else {
        if (this.queueForm.reportCard === '医技科室报卡') {
          GetReportCardReviewTechnology(this.queueForm).then(res => {
            this.tableDate = res.data.listTechnology;
            this.excelDate = res.data.excelListTechnology;
            this.total = res.data.totalTechnology[0].total;
          }).finally(() => {
            loading.close();
          });
        } else {
          const h = this.$createElement;
          this.$notify({
            title: '提示',
            message: h('i', { style: 'color: teal' }, '请选择临床/医技'),
          })
          loading.close();
        }
      }
    },

    // 删除按钮操作
    delRowdata() {
      if (this.delData.patientId === undefined) {
        const h = this.$createElement;
        this.$notify({
          title: '提示信息',
          message: h('i', { style: 'color: teal' }, '请选择要删除的某一列!'),
        })
      } else {
        if (this.delData.status === '2') {
          const h = this.$createElement;
          this.$notify({
            title: '提示信息',
            message: h('i', { style: 'color: teal' }, '报卡处于审核通过状态!不允许删除!'),
          })
        } else {
          let patientId = this.delData.patientId;
          let serialNo = this.delData.serialNo;
          this.$modal.confirm('确认要删除姓名为: ' + this.delData.patientName + ' 的报卡么？删除后无法恢复!')
            .then(function () {
              return DelReportCardReview(patientId, serialNo);
            })
            .then(() => {
              this.getList();
              this.$modal.msgSuccess("删除数据成功");
            })
            .catch(() => { });
        }
      }
    },

    // 监听弹框事件,如果收到子组件传递的参数,则调用该方法
    listenerMethod(data) {
      this.dialogOneOpen = !data;
      this.getList();
    },

    // 单击某一行操作
    handleRowClick(row) {
      // 更新选中行的ID，并保存到本地存储
      this.selectedRowId = row.iD_NO; // 假设每行数据都有一个唯一的id属性
      localStorage.setItem('selectedRowIdx', this.selectedRowId);
      this.delData.serialNo = row.seriaL_NO;
      this.delData.patientId = row.patienT_ID;
      this.delData.patientName = row.patienT_NAME;
      this.delData.status = row.status;
    },

    rowClassName({ row }) {
      // 如果当前行的ID与选中行的ID相同，返回选中样式类名
      return row.iD_NO === this.selectedRowId ? '.selected-row' : 'selected-row';
    },

    // 双击某一行操作
    doubleSelectionChange(row) {
      this.rowData = row;
      ++this.extraFunctionKey;
      this.dialogOneOpen = true;
    },

    // 序号翻页递增
    indexMethod(index) {
      let nowPage = this.queueForm.pageNum; //当前第几页，根据组件取值即可
      let nowLimit = this.queueForm.pageSize; //当前每页显示几条，根据组件取值即可
      return index + 1 + (nowPage - 1) * nowLimit; // 这里可以理解成一个公式
    },

    //获取当前日期
    getToday() {
      let myDate = new Date();
      let nowY = myDate.getFullYear();
      let nowM = myDate.getMonth() + 1;
      let nowD = myDate.getDate();
      let endTime =
        nowY +
        "-" +
        (nowM < 10 ? "0" + nowM : nowM) +
        "-" +
        (nowD < 10 ? "0" + nowD : nowD); //当前日期
      return endTime;
    },

    // 获取三十天之前的日期
    getThreeDaysAgo() {
      let myDate = new Date();
      let lw = new Date(myDate - 1000 * 60 * 60 * 24 * 30); //最后一个数字可改，最后一个数就是往前多少天的意思
      let lastY = lw.getFullYear();
      let lastM = lw.getMonth() + 1;
      let lastD = lw.getDate();
      let beginTime =
        lastY +
        "-" +
        (lastM < 10 ? "0" + lastM : lastM) +
        "-" +
        (lastD < 10 ? "0" + lastD : lastD); //三十天之前日期
      return beginTime;
    },

    // table中不显示时分秒
    formatterTime(row, column) {
      let data = row[column.property]
      return /\d{4}-\d{1,2}-\d{1,2}/g.exec(data)
    },

    // template中设定时间不显示时分秒
    formatDate(date) {
      if (typeof date === 'string') {
        // 如果传入的是字符串，先转换为日期对象
        date = new Date(date);
      }
      // 使用 toISOString() 方法获取 YYYY-MM-DD 格式的字符串
      // 然后用 substring() 方法截取日期部分
      return date.toISOString().substring(0, 10);
    },

    // 自定义高度变化更新高度
    handleResize() {
      this.tableHeight = window.innerHeight; // 更新高度数据
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.centered {
  // 水平居中
  text-align: center;
  // 垂直居中
  vertical-align: middle;
}

.selected-row {

  ::v-deep.el-table__body tr.current-row>td.el-table__cell,
  .el-table__body tr.selection-row>td.el-table__cell {
    background-color: #1890FF;
    color: #FFFFFF;
  }
}
</style>
