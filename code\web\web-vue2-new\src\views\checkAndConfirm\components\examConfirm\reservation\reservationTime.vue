<template>
  <div class="reservation-time-home" :class="{homeThree: styleData.button.three,homeTwo: styleData.button.two}">
    <div class="time-select">
      <div class="select-one">
        <div class="select-one-type">
          <el-radio-group v-model="selectDate.timeType" @change="getTimeData">
            <el-radio-button disabled label="时间类型："></el-radio-button>
            <el-radio-button label="凌晨"></el-radio-button>
            <el-radio-button label="早晨"></el-radio-button>
            <el-radio-button label="上午"></el-radio-button>
            <el-radio-button label="中午"></el-radio-button>
            <el-radio-button label="下午"></el-radio-button>
            <el-radio-button label="傍晚"></el-radio-button>
            <el-radio-button label="晚上"></el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="select-one-source">
        <el-radio-group disabled v-model="selectDate.source">
          <el-radio-button disabled label="患者来源："></el-radio-button>
          <el-radio-button label="门诊"></el-radio-button>
          <el-radio-button label="住院"></el-radio-button>
          <el-radio-button label="体检"></el-radio-button>
        </el-radio-group>
      </div>
      <!--            检查项目-->
      <div class="select-two">
        <el-radio-group v-model="rowDate.examItem">
          <el-radio-button disabled label="检查项目："></el-radio-button>
          <el-radio-button class="two-top" :label="rowDate.examItem"></el-radio-button>
        </el-radio-group>
      </div>
      <!--            预约地点-->
      <div class="select-five">
        <div class="select-two" v-if=" selectDate.thisUnitId !== null && selectDate.thisUnitId !== '' && selectDate.thisUnitId !== undefined">
          <el-radio-group v-model="selectDate.thisUnitName" @change="unitIdDisplay">
            <el-radio-button disabled label="预约地点："></el-radio-button>
            <span v-for="item in selectDate.unitTree" :key="item.value">
              <el-tooltip class="item" effect="light" placement="top">
                <div slot="content">
                  <el-button size="mini" type="success" @click="callQueueSelect(item)">详情搜索</el-button>
                </div>
                <el-radio-button class="two-top" :label="item.label"></el-radio-button>
              </el-tooltip>
            </span>
          </el-radio-group>
        </div>
        <!--              窗口选择-->
        <div class="select-two select-two-one" :style="styleData.right.twoSource"
             v-if="selectDate.thisWindowId !== null && selectDate.thisWindowId !== '' && selectDate.thisWindowId !== undefined ">
          <el-radio-group v-model="selectDate.thisWindowName" @change="windowIdDisplay">
            <el-radio-button disabled label="预约窗口："></el-radio-button>
            <span v-for="item in selectDate.windowTree" :key="item.value">
               <el-radio-button class="two-top" :label="item.label"></el-radio-button>
            </span>
          </el-radio-group>
        </div>
      </div>
      <!--            日期选择-->
      <div class="select-three">
        <el-radio-group v-model="selectDate.thisTime" @change="getTimeData">
          <el-tooltip class="item" effect="light" placement="top">
            <div slot="content"><el-button size="mini" type="success" @click="timeUpdateBut">更多日期</el-button>
            </div>
            <el-radio-button disabled label="日期选择："></el-radio-button>
          </el-tooltip>
          <span v-for="item in selectDate.timeList" :key="item.index">
            <el-radio-button :label="item"></el-radio-button>
          </span>
        </el-radio-group>
      </div>
<!--      时间列表-->
      <div>
        <div class="select-four" v-if="applyDate === selectDate.thisTime">
          <div class="four-item" @click="timeAppointment(item)" v-for="item in timeData" :key="item.index"
               :style="styleData.right.time.width"
               :class="{
                 four:(!item.status || item.surplus === 0) &&!item.indicate &&item.value !== applyTime,
                  five: item.indicate && item.value !== applyTime,
                  one:(applyTime !== null ||applyTime !== undefined ||applyTime !== '') &&item.value === applyTime,}">
            <div class="item-son">{{ item.value }}</div>
            <div class="item-son">可预约{{ item.surplus }}个</div>
            <div class="item-son">已预约{{ item.sum }}个</div>
            <div class="item-son">总预约{{ item.numbers }}个</div>
          </div>
        </div>
        <div class="select-four" v-else>
          <div class="four-item" @click="timeAppointment(item)" v-for="item in timeData" :key="item.index" :style="styleData.right.time.width">
            <div class="item-son">{{ item.value }}</div>
            <div class="item-son">可预约{{ item.surplus }}个</div>
            <div class="item-son">已预约{{ item.sum }}个</div>
            <div class="item-son">总预约{{ item.numbers }}个</div>
          </div>
        </div>
      </div>
    </div>

    <div class="dialog-home">
      <el-dialog :visible.sync="timeUpdateButton" title="预约日期调整" width="50%">
        <div style="display: flex; justify-content: space-around">
          <el-date-picker v-model="timeThisTime" type="date" format="yyyy-MM-dd" placeholder="选择日期" @change="timeUpdateDisplay">
          </el-date-picker>
        </div>
        <div class="time-selectTwo">
          <el-radio-group v-model="timeThisTime">
            <el-radio-button disabled label="日期选择："></el-radio-button>
            <span v-for="item in timeThisList" :key="item.index">
                  <el-radio-button disabled :label="item"></el-radio-button>
                </span>
          </el-radio-group>
        </div>
        <div style="display: flex; justify-content: center; margin-top: 35px">
          <el-button type="primary" round @click="timeSaveDisplay">确定</el-button>
          <el-button type="danger" round @click="timeUpdateButton = false">取消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  GetUnitAndWindowTree,
  GetAppointmentList,
  GetUnitOrWindowTree,
  GetTimeDispose,
  TimeAppointment,} from "@/api/checkAndConfirm/reservationExam"
export default {
  name: 'reservationTime',
  props: ['styleData'],
  components: {},
  data() {
    return {
      selectDate: {},
      rowDate:{},
      textTime: '',
      applyDate: '',
      applyTime: '',
      timeData: [],
      timeUpdateButton: false,
      timeThisTime: '',
      timeThisList: [],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    callQueueSelect(item){
      let data = {
        patient: this.rowDate.patientId,
        unitId: item.value,
        unitName: item.label,
      }
      this.$emit('queue-select',data);
    },
    //时间预约
    timeAppointment(data){
      let myData = this.selectDate;
      let rowData = this.rowDate;
      if (data.surplus === 0) {
        this.$msgbox.alert(
            '<div style="font-size: 18px !important;color: red">' +
            "当前时间点预约人员已满,请选择其他时间点预约!!!" + "</div>",
            "系统提示",
            {
              confirmButtonText: "确定",
              type: "warning",
              dangerouslyUseHTMLString: true,
            }).then(() => {}).catch(() => {});
        return;
      }
      if (rowData.resultStatus === "4") {
        this.$msgbox.alert(
            '<div style="font-size: 18px !important;color: red">' +
            "当前项目已结束,不要随意更改数据!!!" + "</div>",
            "系统提示",
            {
              confirmButtonText: "确定",
              type: "warning",
              dangerouslyUseHTMLString: true,
            }).then(() => {}).catch(() => {});
        return;
      }
      if (!data.status) {
        this.$msgbox.alert(
            '<div style="font-size: 18px !important;color: red">' +
            "当前选择时间已过,请选择其他时间预约!!!" + "</div>",
            "系统提示",
            {
              confirmButtonText: "确定",
              type: "warning",
              dangerouslyUseHTMLString: true,
            }).then(() => {}).catch(() => {});
        return;
      }
      this.$msgbox.alert(
          '<div style="font-size: 18px !important;">' +
          '<div style="display: flex"><div style="width: 30%;font-size: 18px;padding-left: 10%;">姓名：</div><div style="font-size: 16px;color: black;">' +
            rowData.name +
          "</div></div>" +
          '<div style="display: flex"><div style="width: 30%;font-size: 18px;padding-left: 5%;">检查号：</div><div style="font-size: 16px;color: black;">' +
            rowData.examNo +
          "</div></div>" +
          '<div style="display: flex"><div style="width: 30%;font-size: 18px;">检查项目：</div><div style="font-size: 16px;color: black;">' +
            rowData.examItem +
          "</div></div>" +
          '<div style="display: flex"><div style="width: 30%;font-size: 18px;">预约日期：</div><div style="font-size: 16px;color: black;">' +
           myData.thisTime +
          "</div></div>" +
          '<div style="display: flex"><div style="width: 30%;font-size: 18px;">预约时间：</div><div style="font-size: 16px;color: black;">' +
           data.value +
          "</div></div>" +
          '<div style="display: flex"><div style="width: 30%;font-size: 18px;margin-top: 10px;">叫号单元：</div><div style="font-size: 32px;color: black;margin-top: 10px;">' +
           myData.thisUnitName +
          "</div></div>" +
          '<div style="display: flex"><div style="width: 30%;font-size: 18px;margin-top: 10px;">叫号窗口：</div><div style="font-size: 32px;color: black;margin-top: 10px;">' +
           myData.thisWindowName +
          "</div></div>" +
          '<div style="font-size: 24px;margin-top: 10px;color: red">请确认以上信息是否正确!!!</div>' +
          "</div>",
          "系统提示",
          {
            confirmButtonText: "确定",
            type: "warning",
            dangerouslyUseHTMLString: true,
          }).then(() => {
            let saveModel = {
              applyDeptCode: rowData.applyDeptCode,
              examNo: rowData.examNo,
              patientId: rowData.patientId,
              patientName: rowData.name,
              sex: rowData.sex,
              visitId: rowData.visitId,
              empNo: this.$store.getters.empNo,
              appointmentTime: data.value,
              timeId: data.id,
              appointmentDate: myData.thisTime,
              examClassName: rowData.examClass,
              examSubClassName: rowData.examSubClass,
              description: rowData.examItem,
              descriptionCode: rowData.examItemCode,
              money: rowData.costs,
              executeDeptCode: rowData.executeDeptCode,
              executeDeptName: rowData.executeDeptName,
              attention: rowData.attention,
              positionName: rowData.examPositionName,
              positionCode: rowData.examPositionCode,
              unitId: myData.thisUnitId,
              windowId: myData.thisWindowId,
            }
        TimeAppointment(saveModel).then((res) => {
          if (res.code === 200) {
            let msg = ""
            if (res.data){
              this.$msgbox.alert(
                '<div style="font-size: 36px !important; text-align: center;font-weight: 800">队列号</div>' +
                '<div style="font-size: 72px !important; text-align: center;margin-top: 50px;">' +
                res.data + "</div>",
                "系统提示",
                {
                  confirmButtonText: "确定",
                  type: "warning",
                  dangerouslyUseHTMLString: true,
                })
                .then(() => {
                });
            }
            this.$emit('save-success',this.selectDate);
            this.$message.success("预约成功");
          }
        });
      }).catch(() => {
          this.$message.info("已取消预约");
        });
    },
    //时间提交处理
    timeSaveDisplay() {
      this.selectDate.thisTime = this.timeThisTime;
      this.selectDate.timeList = this.timeThisList;
      this.timeThisTime = "";
      this.timeThisList = [];
      this.getTimeData();
      this.timeUpdateButton = false;
    },
    timeUpdateDisplay(){
      let date = this.timeThisTime;
      this.timeThisTime = this.$moment(date).format("YYYY-MM-DD");
      GetTimeDispose(this.timeThisTime).then(res => {
        this.timeThisList = res.data.timeList;
      })
    },
    //日期修改按钮
    timeUpdateBut(){
      let data = this.selectDate;
      this.timeThisTime = data.thisTime;
      this.timeThisList = data.timeList;
      this.timeUpdateButton = true;
    },
    //table 点击接收值：rowData
    receiveRowTable(data){
      this.rowDate = data;
      let tableData = this.$store.getters.reservationTable;
      let unitData = {
        table: data,
        thisUnitId: tableData.thisUnitId,
        thisWindowId: tableData.thisWindowId,
        thisTimeType: tableData.timeType,
        thisDate: tableData.thisDate,
      }
      GetUnitAndWindowTree(unitData).then(res => {
        let resData = res.data;
        tableData.timeType = resData.timeType;
        tableData.thisUnitId = resData.unitId;
        tableData.thisUnitName = resData.unitName;
        tableData.thisWindowId = resData.windowId;
        tableData.thisWindowName = resData.windowName;
        tableData.unitTree = resData.unitTree;
        tableData.windowTree = resData.windowTree;
        this.selectDate = tableData;
        this.getTimeData();
      })
    },
    //table 初始化接收值：tableData
    createReservationTime(data){
      this.selectDate = data;
      this.rowDate = this.$store.getters.reservationRowTable;
      this.getTimeData();
      this.getCurrentDate();
    },
    //unitId 选择
    unitIdDisplay(){
      let data = this.selectDate;
      let unitId = "";
      let unitName = data.thisUnitName;
      let unitTree = data.unitTree;
      unitTree.forEach(t => {
        if (t.label === unitName){
          unitId = t.value;
        }
      })
      this.selectDate.thisUnitId = unitId;
      this.getUnitOrWindowTree(unitId);
    },
    //windowId 选择
    windowIdDisplay(){
      let data = this.selectDate;
      let windowId = "";
      let windowName = data.thisWindowName;
      let windowTree = data.windowTree;
      windowTree.forEach(t => {
        if (t.label === windowName){
          windowId = t.value;
        }
      })
      this.selectDate.thisWindowId = windowId;
      this.getTimeData();
    },
    //windowId 接口获取
    getUnitOrWindowTree(unitId){
      let rowData = this.rowDate;
      let formData = {
        unitId: unitId,
        itemCode: rowData.examItemCode,
      }
      GetUnitOrWindowTree(formData).then(res => {
        let data = res.data;
        this.selectDate.thisWindowId = data.thisWindowId;
        this.selectDate.thisWindowName = data.thisWindowName;
        this.selectDate.windowTree = data.windowTree;
        this.getTimeData();
      })
    },
    //获取时间信息
    getTimeData(){
      let data = this.selectDate;
      let formData = {
        table: this.rowDate,
        TimeType: data.timeType,
        unitId: data.thisUnitId,
        windowId: data.thisWindowId,
        appointmentDate: data.thisTime,
      }
      this.setTable(data);
      GetAppointmentList(formData).then(res => {
        this.timeData = res.data;
      })
    },
    //时间获取
    getCurrentDate() {
      const today = new Date();
      const year = today.getFullYear();
      let month = today.getMonth() + 1; // 月份是从0开始的
      let day = today.getDate();
      // 格式化月份和日期，不足两位前面补0
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;
      this.applyDate = `${year}-${month}-${day}`;
    },
    setTable(data){
      this.$store.commit('SET_RESERVATION_TABLE', data)
    },
  }
}
</script>

<style scoped lang="scss">
.homeTwo{
  ::v-deep.el-radio-button--medium .el-radio-button__inner {
    padding: 10px 17px !important;
    font-size: 14px;
    border-radius: 0;
  }
}
.homeThree{
  ::v-deep.el-radio-button--medium .el-radio-button__inner {
    padding: 10px 15px !important;
    font-size: 14px;
    border-radius: 0;
  }
}
.reservation-time-home{
  ::v-deep.el-radio-button--medium .el-radio-button__inner {
    font-size: 14px;
    border-radius: 0;
  }
  .time-select {
    margin-top: 5px;
    height: 300px;
    background: #ffffff;

    ::v-deep.el-radio-button__orig-radio:disabled
    + .el-radio-button__inner {
      color: black;
    }

    div {
      margin-top: 0.5%;
    }

    .select-one {
      height: 15%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .select-one-type {
      }
    }

    .select-one-source {

      ::v-deep.el-radio-button__orig-radio:disabled:checked
      + .el-radio-button__inner {
        background-color: #1890ff;
        color: #ffffff;
      }
    }

    .select-two {
      min-height: 15%;
      max-height: 36%;
    }

    .select-two-one {
      ::v-deep.el-radio-button--medium .el-radio-button__inner {
        font-size: 18px !important;
        border-radius: 0;
        font-weight: 600;
      }
    }

    .select-three {
      height: 15%;
    }

    .select-four {
      height: 50%;
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      :hover {
        cursor: pointer;
        color: #ffffff;
        box-shadow: 0 0 0 0 grey;
        transform: scale(1.1);
        background: #1890ff !important;
      }

      .four-item {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        text-align: center;
        width: 10%;
        height: 120px;
        background: #edf4fc;
        margin: 1%;

        .item-son {
          transform: scale(1) !important;
        }
      }

    }
    .one {
      cursor: pointer;
      color: #ffffff;
      box-shadow: 0 0 0 0 grey;
      transform: scale(1.1);
      background: #1890ff !important;
      z-index: 1;
    }

    .four {
      color: #ffffff;
      background: red !important;
      z-index: 99;
    }

    .five {
      color: #ffffff;
      background: #20b2aa !important;
      z-index: 2;
    }

    .select-five {
      display: flex;
      justify-content: space-between;
      margin-top: 0.5%;
    }
  }

  .dialog-home{
    .time-selectTwo {
      margin-top: 25px;

      ::v-deep.el-radio-button__orig-radio:disabled
      + .el-radio-button__inner {
        color: black;
      }

      ::v-deep.el-radio-button__orig-radio:checked + .el-radio-button__inner {
        color: #ffffff;
        background-color: #1890ff;
        border-color: #1890ff;
        -webkit-box-shadow: -1px 0 0 0 #1890ff;
        box-shadow: -1px 0 0 0 #1890ff;
      }
    }
  }
}
</style>
