<template>
  <div>
    <el-dialog
      :visible.sync="refundInquiryStatus"
      :title="title"
      width="85%"
    >
      <div class="refund-form">
        <el-form ref="form" :model="queueForm" :inline="true">
          <el-form-item label="查询时间:" label-width="85px">
            <div class="form-flex">
              <el-date-picker v-model="queueForm.beginDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
              <div class="date-f">-</div>
              <el-date-picker v-model="queueForm.endDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="dataInitialize(queueForm)">提取数据</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="refund-table">
        <el-table :data="tableData" border style="width: 100%" max-height="600" highlight-current-row>
          <el-table-column align="center" prop="patienT_ID" label="患者ID" width="90"></el-table-column>
          <el-table-column align="center" prop="name" label="姓名" width="75"></el-table-column>
          <el-table-column align="center" prop="rcpT_NO" label="流水号" width="140" ></el-table-column>
          <el-table-column align="center" prop="iteM_NAME" label="项目"></el-table-column>
          <el-table-column align="center" prop="appoinT_NO" label="申请号" width="80"></el-table-column>
          <el-table-column align="center" prop="costs" label="金额" width="85"></el-table-column>
          <el-table-column align="center" prop="creator" label="操作员" width="85"></el-table-column>
          <el-table-column align="center" prop="createtime" label="操作时间" width="145"></el-table-column>
          <el-table-column align="center" prop="reC_STATUS" label="状态" width="65">
            <template slot-scope="scope">
              <div v-if="scope.row.reC_STATUS === '2'">已退费</div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="chargE_INDICATOR" fixed="right" label="操作" width="120">
            <template slot-scope="scope">
              <el-button v-if="scope.row.reC_STATUS !== '2'" type="danger" @click="revocation(scope.row)">撤销退费</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {GetRefundInquiryInfoMsg} from "@/api/checkAndConfirm/refundInquiry"
export default {
  name: 'refundInquiry',
  props: [],
  components: {},
  data() {
    return {
      title: '门诊退费查询',
      refundInquiryStatus: false,
      tableData: [],
      queueForm: {},
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    init(status){
      if (status){
        let queueForm = this.$store.getters.patient
        this.queueForm = queueForm;
        this.dataInitialize(queueForm);
      }
    },
    dataInitialize(data){
      this.tableData = [];
      GetRefundInquiryInfoMsg(data).then(res => {
        if (res.code === 200){
          this.tableData = res.data;
          this.refundInquiryStatus = true;
        }
      })
    },
    revocation(row){
      this.$msgbox.alert(
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">退费模块开发中 </div>',
          "系统提示",
          {
            confirmButtonText: "确定",
            type: "warning",
            dangerouslyUseHTMLString: true,
          }).then(() => {});
    },
  }
}
</script>

<style scoped lang="scss">
.refund-form{
  margin-top: 2px;
  margin-left: 10px;
  .form-flex {
    display: flex;
  }

  .date-f {
    padding: 0 5px;
  }
  ::v-deep.el-date-editor.el-input, .el-date-editor.el-input__inner {
    width: 135px;
  }
  ::v-deep.el-form-item {
    margin-bottom: 0;
  }
}
.refund-table{
  margin-top: 10px;
  ::v-deep.el-table--medium .el-table__cell {
    padding: 3px 0;
  }
  ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
    height: 30px;
    font-size: 14px;
  }
  ::v-deep.el-table th.el-table__cell > .cell {
    padding-left: 2px;
    padding-right: 2px;
  }
  ::v-deep.el-table .cell {
    padding-left: 2px;
    padding-right: 2px;
  }
  ::-webkit-scrollbar {
    width: 11px;
    height: 11px;
  }
  ::-webkit-scrollbar-thumb {
    background-color: #6bcaaf;
    border-radius: 10px;
  }
  ::v-deep.el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
    background-color: #1890FF;
    color: #FFFFFF;
  }
}
</style>
