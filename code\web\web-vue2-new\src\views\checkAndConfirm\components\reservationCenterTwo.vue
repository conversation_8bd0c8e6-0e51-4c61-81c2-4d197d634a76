<template>
  <div class="home" :style="myStyle.home.minHeight">
    <div class="home-index">
      <div class="home-left">
        <div class="left-user-title" :style="myStyle.left.user.title">
          患者基础信息
        </div>
        <div class="left-user" :style="myStyle.left.user.height">
          <div class="user-info" :style="myStyle.left.user.info">
            <div class="user-img">
              <el-avatar :src="manImg" v-if="myData.user.sex === '男'" :size="myStyle.left.user.img"></el-avatar>
              <el-avatar :src="woManImg" v-else-if="myData.user.sex === '女'" :size="myStyle.left.user.img"></el-avatar>
              <el-avatar :src="manImg" v-else :size="myStyle.left.user.img"></el-avatar>
            </div>
            <div class="user-msg">
              <div class="user-msg-1" :style="myStyle.left.user.size">
                <span>姓名：{{ myData.user.name }}</span>
                <span>ID：{{ myData.user.patientId }}</span>
              </div>
              <div class="user-msg-1" :style="myStyle.left.user.size">
                <span>性别：{{ myData.user.sex }}</span>
                <span>年龄：{{ myData.user.dateOfBirths }}</span>
              </div>
              <div class="user-msg-2" :style="myStyle.left.user.size">
                <span>就诊日期：{{ myData.user.appointmentDate }}</span>
              </div>
            </div>
          </div>
          <div class="user-exam" :style="myStyle.left.user.exam">
            <span>检查号：{{ myData.user.examNo }}</span>
            <span>住院次数：{{ myData.user.visitId }}</span>
          </div>
        </div>
        <div class="left-case" :style="myStyle.left.case.height">
          <!--          项目列表-->
          <div class="my-header-text" :style="myStyle.left.case.title">
            项目列表
          </div>
          <!--          项目列表-->
          <el-scrollbar style="height: 90%">
            <div v-if="myStyle.left.case.table.one">
              <div class="case-table" @click="tableItemDisplay(item)" v-for="item in myData.table" :key="item.index"
                   :class="{tableOne: item.color === '1',tableTwo: item.color === '2',tableThree: item.color === '3',tableZero: item.color === '0',}"
              >
                <el-tooltip effect="light" placement="right">
                  <div slot="content">
                    <div class="time-table-img" @click="AiButton(item)">
                      <img :src="answerQuestionsImg" :style="myStyle.right.img" alt=""/>
                    </div>
                  </div>
                  <div class="case-table-item" :style="myStyle.left.case.table.height">
                    <div>检查项目：
                      <span :style="myStyle.left.case.table.size1">
                        {{ item.examItem }}
                        <span v-if="item.status === '4'">(已做)</span>
                        <span v-else-if="item.status === '2'">(已确认)</span>
                        <span v-else-if=" item.status === null && item.appointmentTime !== null">(已预约)</span>
                        <span v-else>(未预约)</span>
                      </span>
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      检查位置：{{ item.examPositionName }}
                    </div>
                    <div style="display: flex" :style="myStyle.left.case.table.size2">
                      <div :style="myStyle.left.case.table.width2">
                        申请科室：{{ item.applyDeptName }}
                      </div>
                      <div>申请医师：{{ item.applyDoctorName }}</div>
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      申请时间：{{ item.createDate }}
                    </div>
                    <div :style="myStyle.left.case.table.size2" style="display: flex">
                      <div :style="myStyle.left.case.table.width2">
                        预约日期：{{ item.appointmentDate }}
                      </div>
                      <div>预约时间：{{ item.appointmentTime }}</div>
                    </div>
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div v-if="myStyle.left.case.table.two">
              <div class="case-table" @click="tableItemDisplay(item)" v-for="item in myData.table" :key="item.index"
                   :class="{tableOne: item.color === '1',tableTwo: item.color === '2',tableThree: item.color === '3',tableZero: item.color === '0',}"
              >
                <el-tooltip effect="light" placement="right">
                  <div slot="content">
                    <div class="time-table-img" @click="AiButton(item)">
                      <img :src="answerQuestionsImg" :style="myStyle.right.img" alt=""/>
                    </div>
                  </div>
                  <div class="case-table-item" :style="myStyle.left.case.table.height">
                    <div>检查项目：<span :style="myStyle.left.case.table.size1">{{ item.examItem }}
                        <span v-if="item.status === '4'">(已做)</span>
                        <span v-else-if="item.status === '2'">(已确认)</span>
                        <span v-else-if=" item.status === null &&item.appointmentTime !== null ">(已预约)</span>
                        <span v-else>(未预约)</span>
                      </span>
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      检查位置：{{ item.examPositionName }}
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      申请科室：{{ item.applyDeptName }}
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      申请医师：{{ item.applyDoctorName }}
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      申请时间：{{ item.createDate }}
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      预约日期：{{ item.appointmentDate }}
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      预约时间：{{ item.appointmentTime }}
                    </div>
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div v-if="myStyle.left.case.table.three">
              <div class="case-table" @click="tableItemDisplay(item)" v-for="item in myData.table" :key="item.index"
                   :class="{tableOne: item.color === '1',tableTwo: item.color === '2',
                  tableThree: item.color === '3',tableZero: item.color === '0',}"
              >
                <el-tooltip effect="light" placement="right">
                  <div slot="content">
                    <div class="time-table-img" @click="AiButton(item)">
                      <img :src="answerQuestionsImg" :style="myStyle.right.img" alt=""/>
                    </div>
                  </div>
                  <div class="case-table-item" :style="myStyle.left.case.table.height">
                    <div>
                      检查项目：<span :style="myStyle.left.case.table.size1">{{ item.examItem }}
                        <span v-if="item.status === '4'">(已做)</span>
                        <span v-else-if="item.status === '2'">(已确认)</span>
                        <span v-else-if="item.status === null &&item.appointmentTime !== null">(已预约)</span>
                        <span v-else>(未预约)</span>
                      </span>
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      检查位置：{{ item.examPositionName }}
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      申请科室：{{ item.applyDeptName }}
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      申请医师：{{ item.applyDoctorName }}
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      申请时间：{{ item.createDate }}
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      预约日期：{{ item.appointmentDate }}
                    </div>
                    <div :style="myStyle.left.case.table.size2">
                      预约时间：{{ item.appointmentTime }}
                    </div>
                  </div>
                </el-tooltip>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
      <!--      右侧内容-->
      <div class="home-right">
        <!--        top文案-->
        <!--        中间时间选项-->
        <div
          class="right-time" :style="myStyle.right.time.height" :class="{ two: myStyle.right.ontSourceBut }"
        >
          <div class="time-table">
            <el-table ref="multipleTable2" :data="caseList" :height="myStyle.right.table" size="mini" border
                      style="width: 100%"
            >
              <el-table-column align="center" prop="inspectionPurpose" label="检查目的"></el-table-column>
              <el-table-column align="center" prop="clinSymp" label="症状"></el-table-column>
              <el-table-column align="center" prop="physSign" label="体征"></el-table-column>
              <el-table-column align="center" prop="clinDiag" label="临床诊断"></el-table-column>
              <el-table-column align="center" prop="relevantDiag" width="75" label="其他诊断"></el-table-column>
              <el-table-column align="center" prop="relevantLabTest" width="85" label="相关化验结果"></el-table-column>
            </el-table>
          </div>
          <div class="time-select">
            <!--            时间类型/患者来源-->
            <div class="select-one">
              <div class="select-one-type">
                <el-radio-group v-model="myData.timeType" @change="timeModelDisplay(myData, 2)">
                  <el-radio-button disabled label="时间类型："></el-radio-button>
                  <el-radio-button label="凌晨"></el-radio-button>
                  <el-radio-button label="早晨"></el-radio-button>
                  <el-radio-button label="上午"></el-radio-button>
                  <el-radio-button label="中午"></el-radio-button>
                  <el-radio-button label="下午"></el-radio-button>
                  <el-radio-button label="傍晚"></el-radio-button>
                  <el-radio-button label="晚上"></el-radio-button>
                </el-radio-group>
              </div>
              <div class="select-one-source" :style="myStyle.right.ontSource">
                <el-radio-group disabled v-if="myData.source === '住院'" v-model="myData.source">
                  <el-radio-button disabled label="来源："></el-radio-button>
                  <el-radio-button label="门诊"></el-radio-button>
                  <el-tooltip class="item" effect="light" placement="top">
                    <div slot="content">
                      <el-button size="mini" type="success" @click="beHospitalizedInformBut">住院通知</el-button>
                    </div>
                    <el-radio-button label="住院"></el-radio-button>
                  </el-tooltip>
                  <el-radio-button label="体检"></el-radio-button>
                </el-radio-group>
                <el-radio-group disabled v-else v-model="myData.source">
                  <el-radio-button disabled label="来源："></el-radio-button>
                  <el-radio-button label="门诊"></el-radio-button>
                  <el-radio-button label="住院"></el-radio-button>
                  <el-radio-button label="体检"></el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <!--            检查项目-->
            <div class="select-two">
              <el-radio-group v-model="myData.examItem">
                <el-radio-button disabled label="检查项目："></el-radio-button>
                <el-tooltip class="item" effect="light" placement="right">
                  <div slot="content">
                    <el-button size="mini" type="success" @click="manualInputBut">手动登记</el-button>
                  </div>
                  <el-radio-button class="two-top" :label="myData.examItem"></el-radio-button>
                </el-tooltip>
              </el-radio-group>
            </div>
            <!--            预约地点-->
            <div class="select-five">
              <div class="select-two"
                   v-if="myData.thisUnitId !== null && myData.thisUnitId !== '' && myData.thisUnitId !== undefined"
              >
                <el-radio-group v-model="myData.thisUnitName" @change="unitIdDisplay">
                  <el-radio-button disabled label="预约地点："></el-radio-button>
                  <span v-for="item in myData.unitTree" :key="item.value">
                    <el-tooltip class="item" effect="light" placement="top">
                      <div slot="content">
                        <el-button size="mini" type="success" @click="callQueueSelect(item)">详情搜索</el-button>
                      </div>
                      <el-radio-button class="two-top" :label="item.label"></el-radio-button>
                    </el-tooltip>
                  </span>
                </el-radio-group>
              </div>
              <!--              窗口选择-->
              <div class="select-two select-two-one" :style="myStyle.right.twoSource"
                   v-if="myData.thisWindowId !== null && myData.thisWindowId !== '' &&myData.thisWindowId !== undefined"
              >
                <el-radio-group v-model="myData.thisWindowName" @change="windowIdDisplay">
                  <el-radio-button disabled label="预约窗口："></el-radio-button>
                  <span v-for="item in myData.windowTree" :key="item.value">
                    <el-tooltip class="item" effect="light" v-if="item.index <= 3" placement="top">
                      <div slot="content">
                        <el-button size="mini" type="success" @click="callQueueBut(item.value, item.label)"
                        >队列列表</el-button>
                      </div>
                      <el-radio-button class="two-top" :label="item.label"></el-radio-button>
                    </el-tooltip>
                    <el-tooltip class="item" effect="light" v-if="item.index > 3" placement="bottom">
                      <div slot="content">
                        <el-button size="mini" type="success" @click="callQueueBut(item.value, item.label)"
                        >队列列表</el-button>
                      </div>
                      <el-radio-button class="two-top" :label="item.label"></el-radio-button>
                    </el-tooltip>
                  </span>
                </el-radio-group>
              </div>
            </div>
            <!--            日期选择-->
            <div class="select-three">
              <el-radio-group v-model="myData.thisTime" @change="timeModelDisplay(myData, 2)">
                <el-tooltip class="item" effect="light" placement="top">
                  <div slot="content">
                    <el-button size="mini" type="success" @click="timeUpdateBut">更多日期</el-button>
                  </div>
                  <el-radio-button disabled label="日期选择："></el-radio-button>
                </el-tooltip>
                <span v-for="item in myData.timeList" :key="item.index">
                  <el-radio-button :label="item"></el-radio-button>
                </span>
              </el-radio-group>
              <el-radio-group v-model="textTime" @change="appointmentListBut">
                <el-radio-button label="预约列表"></el-radio-button>
              </el-radio-group>
            </div>
            <div class="select-four" v-if="applyDate === myData.thisTime">
              <div class="four-item" @click="timeAppointment(item)" v-for="item in timeData" :key="item.index"
                   :style="myStyle.right.time.width"
                   :class="{four:(!item.status || item.surplus === 0) &&!item.indicate &&item.value !== applyTime,
                  five: item.indicate && item.value !== applyTime,
                  one:(applyTime !== null ||applyTime !== undefined ||applyTime !== '') &&item.value === applyTime,}"
              >
                <div class="item-son">{{ item.value }}</div>
                <div class="item-son">可预约{{ item.surplus }}个</div>
                <div class="item-son">已预约{{ item.sum }}个</div>
                <div class="item-son">总预约{{ item.numbers }}个</div>
              </div>
            </div>
            <div class="select-four" v-else>
              <div class="four-item" @click="timeAppointment(item)" v-for="item in timeData" :key="item.index"
                   :style="myStyle.right.time.width"
              >
                <div class="item-son">{{ item.value }}11</div>
                <div class="item-son">可预约{{ item.surplus }}个</div>
                <div class="item-son">已预约{{ item.sum }}个</div>
                <div class="item-son">总预约{{ item.numbers }}个</div>
              </div>
            </div>
          </div>
        </div>
        <!--        底部流程图-->
        <div class="right-task">
          <div class="task-list" :style="myStyle.right.task.size">
            <div class="task-two task-border" :style="myStyle.right.task.one">
              <div>开立检查</div>
            </div>
            <div class="image1" :style="myStyle.right.task.image1"></div>
            <div class="task-two task-border" :style="myStyle.right.task.two">
              收费
            </div>
            <div class="image1" :style="myStyle.right.task.image1"></div>
            <div class="task-three task-border" :style="myStyle.right.task.three">
              预约时间
            </div>
            <div class="image2" :style="myStyle.right.task.image2"></div>
            <div class="task-four task-border" :style="myStyle.right.task.four">
              检查开始
            </div>
            <div class="image3" :style="myStyle.right.task.image3"></div>
            <div class="task-five task-border" :style="myStyle.right.task.five">
              报告填写
            </div>
            <div class="image3" :style="myStyle.right.task.image3"></div>
            <div class="task-six task-border" :style="myStyle.right.task.six">
              报告审核
            </div>

            <div class="task-bottom">
              <div class="task-bottom-s">
                <span class="task-bottom-on" style="background: #8caae7">
                </span>
                <span class="bottom-text"> 已处理 </span>
              </div>
              <div class="task-bottom-s">
                <span class="task-bottom-on" style="background: red"> </span>
                <span class="bottom-text"> 当前节点 </span>
              </div>
              <div class="task-bottom-s">
                <span class="task-bottom-on" style="background: #f8f8f9">
                </span>
                <span class="bottom-text"> 待办节点 </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="home-drawer">
        <el-drawer :size="myStyle.drawer.size" :title="callQueueTitle" :visible.sync="callQueueButton">
          <div style="margin: 10px 0px;display: flex;justify-content: space-around;">
            <el-radio-group v-model="drawerTable">
              <el-radio :label="1">等待中</el-radio>
              <el-radio :label="2">已就诊</el-radio>
            </el-radio-group>
          </div>

          <div v-if="drawerTable === 1">
            <div :style="myStyle.drawer.text2">等待中人员列表</div>
            <el-table ref="multipleTable2" :data="callQueueList" :height="myStyle.drawer.height" size="mini" border
                      style="width: 100%"
            >
              <!--              <el-table-column align="center" prop="no" :width="myStyle.drawer.table.width1" label="序号"></el-table-column>-->
              <el-table-column align="center" :width="myStyle.drawer.table.width2" label="操作">
                <template slot-scope="scope">
                  <el-button icon="" size="mini" type="text" @click="deleteCallQueueByQueueId(scope.row)">删除
                  </el-button>
                  <el-button icon="" size="mini" type="text" @click="transferBut(scope.row)">转出</el-button>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="queueNo" :width="myStyle.drawer.table.width3" label="队列号"
              ></el-table-column>
              <el-table-column align="center" prop="queueName" label="姓名"></el-table-column>
              <el-table-column align="center" prop="start" :width="myStyle.drawer.table.width4" label="登记时间"
              ></el-table-column>
              <el-table-column align="center" prop="appointmentTime" label="预约时间"></el-table-column>
              <el-table-column align="center" prop="awaitTime" :width="myStyle.drawer.table.width4" label="等待时间"
              ></el-table-column>
              <el-table-column align="center" prop="callNum" :width="myStyle.drawer.table.width5" label="叫号次数"
              ></el-table-column>
              <el-table-column align="center" :width="myStyle.drawer.table.width6" label="号源">
                <template slot-scope="scope">
                  <el-tooltip class="item" effect="light" placement="top">
                    <div slot="content">
                      <el-button size="mini" type="success" @click="doubleClickMark(scope.row)">调整</el-button>
                    </div>
                    <el-tag v-if="scope.row.priority === 1" type="success">回诊</el-tag>
                    <el-tag v-else-if="scope.row.priority === 2">预约</el-tag>
                    <el-tag v-else-if="scope.row.priority === 3">普通</el-tag>
                    <el-tag v-else-if="scope.row.priority === 4" type="info">过号</el-tag>
                    <el-tag v-else-if="scope.row.priority === 5" type="danger">急诊</el-tag>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column align="center" :width="myStyle.drawer.table.width7" label="状态">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.deleteFlag !== 0" type="info">已删除</el-tag>
                  <el-tag v-else-if="scope.row.status === 1">等待中</el-tag>
                  <el-tag v-else-if="scope.row.status === 2" type="success">就诊结束</el-tag>
                  <el-tag v-else-if="scope.row.status === 3" type="success">就诊结束</el-tag>
                  <el-tag v-else-if="scope.row.status === 4" type="success">就诊结束</el-tag>
                  <el-tag v-else-if="scope.row.status === 5" type="info">过号</el-tag>
                  <el-tag v-else-if="scope.row.status === 6" type="info">作废</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div v-else>
            <div :style="myStyle.drawer.text1">已就诊人员列表</div>
            <el-table ref="multipleTable2" :data="alreadyQueueList" :height="myStyle.drawer.height" size="mini" border
                      style="width: 100%"
            >
              <!--              <el-table-column align="center" prop="no" width="50" label="序号"></el-table-column>-->
              <el-table-column align="center" width="90" label="操作">
                <template slot-scope="scope">
                  <el-button icon="" size="mini" type="text" @click="recoverCallQueue(scope.row)">恢复</el-button>
                </template>
              </el-table-column>
              <el-table-column align="center" :width="myStyle.drawer.table.width3" prop="queueNo" label="队列号"
              ></el-table-column>
              <el-table-column align="center" prop="queueName" label="姓名"></el-table-column>
              <el-table-column align="center" prop="start" :width="myStyle.drawer.table.width4" label="登记时间"
              ></el-table-column>
              <el-table-column align="center" prop="appointmentTime" label="预约时间"></el-table-column>
              <el-table-column align="center" prop="awaitTime" :width="myStyle.drawer.table.width4" label="等待时间"
              ></el-table-column>
              <el-table-column align="center" prop="callOutTime" :width="myStyle.drawer.table.width5" label="叫号时间"
              ></el-table-column>
              <el-table-column align="center" :width="myStyle.drawer.table.width6" label="号源">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.priority === 1" type="success">回诊</el-tag>
                  <el-tag v-else-if="scope.row.priority === 2">预约</el-tag>
                  <el-tag v-else-if="scope.row.priority === 3">普通</el-tag>
                  <el-tag v-else-if="scope.row.priority === 4" type="info">过号</el-tag>
                  <el-tag v-else-if="scope.row.priority === 5" type="danger">急诊</el-tag>
                </template>
              </el-table-column>
              <el-table-column align="center" :width="myStyle.drawer.table.width7" label="状态">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.deleteFlag !== 0" type="info">已删除</el-tag>
                  <el-tag v-else-if="scope.row.status === 1">等待中</el-tag>
                  <el-tag v-else-if="scope.row.status === 2" type="success">就诊结束</el-tag>
                  <el-tag v-else-if="scope.row.status === 3" type="success">就诊结束</el-tag>
                  <el-tag v-else-if="scope.row.status === 4" type="success">就诊结束</el-tag>
                  <el-tag v-else-if="scope.row.status === 5" type="info">过号</el-tag>
                  <el-tag v-else-if="scope.row.status === 6" type="info">作废</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-drawer>
      </div>

      <div class="home-dialog">
        <el-dialog :visible.sync="transferButton" width="50%">
          <div class="dialog-son">
            <!--        中间时间选项-->
            <div>
              <div
                v-if="myData.thisUnitId !== null && myData.thisUnitId !== '' && myData.thisUnitId !== undefined"
              >
                <el-radio-group v-model="transferModel.newUnitName" @change="unitIdDisplayTwo">
                  <el-radio-button disabled label="预约地点："></el-radio-button>
                  <span v-for="item in transferModel.unitTree" :key="item.value">
                    <el-radio-button :label="item.label"></el-radio-button>
                  </span>
                </el-radio-group>
              </div>
              <!--              窗口选择-->
              <div
                style="margin-top: 10px"
                v-if=" myData.thisWindowId !== null &&myData.thisWindowId !== '' &&  myData.thisWindowId !== undefined "
              >
                <el-radio-group v-model="transferModel.newWindowName">
                  <el-radio-button disabled label="预约窗口："></el-radio-button>
                  <span v-for="item in transferModel.windowTree" :key="item.value">
                    <el-radio-button :label="item.label"></el-radio-button>
                  </span>
                </el-radio-group>
              </div>
            </div>
            <div style="display: flex; margin-top: 20px; justify-content: center">
              <el-button round @click="saveTransferTwo">确定</el-button>
              <el-button type="danger" @click="transferButton = false" round>取消</el-button>
            </div>
          </div>
        </el-dialog>

        <div class="dialog-two">
          <el-dialog :visible.sync="markButton" title="号源调整" width="50%">
            <div class="dialog-son">
              <!--        中间时间选项-->
              <div style="display: flex; justify-content: space-around">
                <el-radio-group v-model="markModel.priority">
                  <el-radio :label="1">回诊</el-radio>
                  <el-radio :label="3">普通</el-radio>
                  <el-radio :label="4">过号</el-radio>
                  <el-radio :label="5" style="color: red !important">急诊</el-radio>
                </el-radio-group>
              </div>
              <div style="display: flex; margin-top: 20px; justify-content: center">
                <el-button round @click="saveClickMark">确定</el-button>
                <el-button type="danger" @click="recoverCallQueueTwo(markModel.unitId, markModel.windowId)" round>取消
                </el-button>
              </div>
            </div>
          </el-dialog>
        </div>

        <div class="dialog-two">
          <el-dialog :visible.sync="callSelectButton" :title="callSelectDialogTitle" :width="myStyle.dialog.ont.width">
            <div style="
                display: flex;
                justify-content: center;
                margin-bottom: 20px;
              "
            >
              <el-input @keyup.enter.native="executeCallSelect" style="width: 30%" v-model="callSelectForm.patient"
                        placeholder="请输入患者ID号进行查询"
              ></el-input>
              <el-button type="primary" @click="executeCallSelect">搜索</el-button>
            </div>
            <div style="height: 500px">
              <el-scrollbar style="height: 90%">
                <el-table ref="multipleTable3" :data="callSelectTable" size="mini" border style="width: 100%">
                  <el-table-column align="center" prop="windowName" :width="myStyle.dialog.ont.table.table1"
                                   label="窗口"
                  ></el-table-column>
                  <el-table-column align="center" prop="queueName" :width="myStyle.dialog.ont.table.table2"
                                   label="姓名"
                  ></el-table-column>
                  <el-table-column align="center" prop="queueNo" :width="myStyle.dialog.ont.table.table3"
                                   label="队列号"
                  ></el-table-column>
                  <el-table-column align="center" prop="priority" :width="myStyle.dialog.ont.table.table4" label="号源">
                    <template slot-scope="scope">
                      <el-tooltip class="item" effect="light" placement="top">
                        <div slot="content">
                          <el-button size="mini" type="success" @click="doubleClickMark(scope.row)">调整</el-button>
                        </div>
                        <el-tag v-if="scope.row.priority === 1" type="success">回诊</el-tag>
                        <el-tag v-else-if="scope.row.priority === 2">预约</el-tag>
                        <el-tag v-else-if="scope.row.priority === 3">普通</el-tag>
                        <el-tag v-else-if="scope.row.priority === 4" type="info">过号</el-tag>
                        <el-tag v-else-if="scope.row.priority === 5" type="danger">急诊</el-tag>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="status" :width="myStyle.dialog.ont.table.table5" label="状态">
                    <template slot-scope="scope">
                      <el-tag v-if="scope.row.deleteFlag !== 0" type="info">已删除</el-tag>
                      <el-tag v-else-if="scope.row.status === 1">等待中</el-tag>
                      <el-tag v-else-if="scope.row.status === 2" type="success">就诊结束</el-tag>
                      <el-tag v-else-if="scope.row.status === 3" type="success">就诊结束</el-tag>
                      <el-tag v-else-if="scope.row.status === 4" type="success">就诊结束</el-tag>
                      <el-tag v-else-if="scope.row.status === 5" type="info">过号</el-tag>
                      <el-tag v-else-if="scope.row.status === 6" type="info">作废</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" prop="callNum" :width="myStyle.dialog.ont.table.table6"
                                   label="呼叫次数"
                  ></el-table-column>
                  <el-table-column align="center" prop="time" :width="myStyle.dialog.ont.table.table7" label="登记时间"
                  ></el-table-column>
                  <el-table-column align="center" prop="content" label="检查项目"></el-table-column>
                  <el-table-column align="center" prop="memo" :width="myStyle.dialog.ont.table.table8" label="备注"
                  ></el-table-column>
                </el-table>
              </el-scrollbar>
            </div>
          </el-dialog>
        </div>

        <div class="dialog-two">
          <el-dialog :visible.sync="timeUpdateButton" title="预约日期调整" width="50%">
            <div style="display: flex; justify-content: space-around">
              <el-date-picker v-model="timeThisTime" type="date" format="yyyy-MM-dd" placeholder="选择日期"
                              @change="timeUpdateDisplay"
              ></el-date-picker>
            </div>
            <div class="time-selectTwo">
              <el-radio-group v-model="timeThisTime">
                <el-radio-button disabled label="日期选择："></el-radio-button>
                <span v-for="item in timeThisList" :key="item.index">
                  <el-radio-button disabled :label="item"></el-radio-button>
                </span>
              </el-radio-group>
            </div>
            <div style="display: flex; justify-content: center; margin-top: 35px">
              <el-button type="primary" round @click="timeSaveDisplay">确定</el-button>
              <el-button type="danger" round @click="timeUpdateButton = false">取消</el-button>
            </div>
          </el-dialog>
        </div>

        <div class="dialog-two">
          <el-dialog :visible.sync="appointmentListButton" :title="appointmentListTitle"
                     :width="myStyle.dialog.two.dialogWidth"
          >
            <div>
              <el-form ref="form" :model="appointmentListForm" :inline="true" label-width="60px">
                <el-form-item label="日期：">
                  <el-date-picker v-model="appointmentListForm.thisTime" type="date" format="yyyy-MM-dd"
                                  placeholder="选择日期"
                                  @change="appointmentTimeDisplay" :style="myStyle.dialog.two.form.width1"
                  >
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="时间：">
                  <el-select :style="myStyle.dialog.two.form.width2" v-model="appointmentListForm.timeType"
                             placeholder="请选择"
                  >
                    <el-option v-for="item in appointmentTimeTypeDict" :key="item.value" :label="item.label"
                               :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="单元：">
                  <el-select :style="myStyle.dialog.two.form.width3" v-model="appointmentListForm.unitId"
                             @change="appointmentListFormUnitTreeClick" placeholder="请选择"
                  >
                    <el-option v-for="item in appointmentListForm.unitTree" :key="item.value" :label="item.label"
                               :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="窗口：">
                  <el-select :style="myStyle.dialog.two.form.width4" v-model="appointmentListForm.windowId"
                             placeholder="请选择"
                  >
                    <el-option v-for="item in appointmentListForm.windowTree" :key="item.value" :label="item.label"
                               :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" icon="el-icon-search" round @click="appointmentListSelect">搜索</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div>
              <el-table ref="multipleTable3" :max-height="myStyle.dialog.two.table.height" :data="appointmentListModel"
                        size="mini" border
                        style="width: 100%"
              >
                <el-table-column type="index" width="50" label="序号" align="center"></el-table-column>
                <el-table-column align="center" prop="examNo" :width="myStyle.dialog.two.table.width2" label="检查号"
                ></el-table-column>
                <el-table-column align="center" prop="subClassName" :width="myStyle.dialog.two.table.width3"
                                 label="类型"
                ></el-table-column>
                <el-table-column align="center" prop="examItem" label="检查项目"></el-table-column>
                <el-table-column align="center" prop="patientId" :width="myStyle.dialog.two.table.width2" label="ID号"
                ></el-table-column>
                <el-table-column align="center" prop="patientName" :width="myStyle.dialog.two.table.width4"
                                 label="姓名"
                ></el-table-column>
                <el-table-column align="center" prop="appointmentTime" :width="myStyle.dialog.two.table.width5"
                                 label="时间段"
                ></el-table-column>
                <el-table-column align="center" prop="updateTime" :show-overflow-tooltip="true"
                                 :width="myStyle.dialog.two.table.width6" label="预约时间"
                ></el-table-column>
                <el-table-column align="center" prop="unitName" :width="myStyle.dialog.two.table.width7"
                                 label="检查地点"
                ></el-table-column>
                <el-table-column align="center" prop="windowName" :width="myStyle.dialog.two.table.width8"
                                 label="检查窗口"
                ></el-table-column>
              </el-table>
            </div>
            <div style="display: flex; justify-content: center; margin-top: 20px">
              <el-button type="danger" round @click="appointmentListButton = false">关闭</el-button>
            </div>
          </el-dialog>
        </div>

        <div class="dialog-two">
          <el-dialog :visible.sync="manualInputButton" :title="manualInputTitle"
                     :width="myStyle.dialog.three.dialogWidth"
          >
            <div class="dialog-manual-input">
              <div style="display: flex; justify-content: center; margin-top: 15px">
                <el-form ref="form" :model="appointmentListForm" :inline="true" label-width="80px">
                  <el-form-item label="患者ID：">
                    <el-input disabled style="width: 180px" v-model="manualInputForm.patientId"
                              placeholder="请输入患者ID号"
                    ></el-input>
                  </el-form-item>
                </el-form>

                <!--                <el-button type="primary" round icon="el-icon-search" @click="manualInputPatientSelect">检索</el-button>-->
              </div>
              <div style="display: flex; justify-content: center">
                <div style="width: 70%">
                  <el-descriptions class="margin-top" :column="2" border>
                    <el-descriptions-item style="width: 30%">
                      <template slot="label">
                        <i class="el-icon-user"></i>
                        姓名:
                      </template>
                      {{ manualInputForm.patientName }}
                    </el-descriptions-item>
                    <el-descriptions-item v-if="manualInputForm.sex === '男'">
                      <template slot="label">
                        <i class="el-icon-male"></i>
                        性别:
                      </template>
                      {{ manualInputForm.sex }}
                    </el-descriptions-item>
                    <el-descriptions-item v-else-if="manualInputForm.sex === '女'">
                      <template slot="label">
                        <i class="el-icon-female"></i>
                        性别:
                      </template>
                      {{ manualInputForm.sex }}
                    </el-descriptions-item>
                    <el-descriptions-item v-else>
                      <template slot="label">
                        <i class="el-icon-male"></i>
                        性别:
                      </template>
                      {{ manualInputForm.sex }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
              <div style="margin-top: 20px">
                <el-checkbox-group v-model="manualInputForm.thisTable">
                  <el-checkbox-button disabled label="检查项目："></el-checkbox-button>
                  <el-checkbox-button v-for="city in manualInputForm.tableList" :label="city.examNo" :key="city.examNo">
                    {{ city.examItem }}
                  </el-checkbox-button>
                </el-checkbox-group>
              </div>
              <div style="margin-top: 10px">
                <el-radio-group v-model="manualInputForm.unitId" @change="manualInputUnitDisplay">
                  <el-radio-button disabled label="预约地点："></el-radio-button>
                  <span v-for="item in manualInputForm.unitTree" :key="item.value">
                    <el-radio-button class="two-top" :label="item.value">{{ item.label }}</el-radio-button>
                  </span>
                </el-radio-group>
              </div>
              <div style="margin-top: 10px">
                <el-radio-group v-model="manualInputForm.windowId">
                  <el-radio-button disabled label="预约窗口："></el-radio-button>
                  <span v-for="item in manualInputForm.windowTree" :key="item.value">
                    <el-radio-button class="two-top" :label="item.value">{{ item.label }}</el-radio-button>
                  </span>
                </el-radio-group>
              </div>
              <div style="margin-top: 10px">
                <el-radio-group v-model="manualInputForm.priority">
                  <el-radio-button disabled label="号源选择："></el-radio-button>
                  <el-radio-button class="two-top" :label="1">回诊</el-radio-button>
                  <el-radio-button class="two-top" :label="3">普通</el-radio-button>
                  <el-radio-button class="two-top" :label="4">过号</el-radio-button>
                  <el-radio-button class="two-top" style="color: red !important" :label="5">急诊</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div style="display: flex; justify-content: center; margin-top: 35px">
              <el-button type="primary" round @click="manualInputSave">确定</el-button>
              <el-button type="danger" round @click="manualInputButton = false">取消</el-button>
            </div>
          </el-dialog>
        </div>

        <div class="dialog-two beHospitalized">
          <el-dialog :visible.sync="beHospitalizedInformButton" :title="beHospitalizedInformTitle"
                     :width="myStyle.dialog.three.dialogWidth"
          >
            <div class="beHospitalizedBottom">
              <div class="beHospitalizedTitle">通知项目(可多选)</div>
              <div class="beHospitalizedOne">
                <el-checkbox-group class="beHospitalizedGroup" v-model="beHospitalizedInformModel.clickItemList">
                  <span v-for="item in beHospitalizedInformModel.itemList" :key="item.value">
                    <el-checkbox :label="item.value">{{ item.label }}</el-checkbox>
                  </span>
                </el-checkbox-group>
              </div>
            </div>
            <div class="beHospitalizedTop">
              <div class="beHospitalizedTitle">预约时间</div>
              <div style="display: flex; justify-content: center" class="beHospitalizedTop">
                <el-date-picker v-model="beHospitalizedInformModel.appointmentDateTime" type="datetime"
                                placeholder="选择日期时间"
                >
                </el-date-picker>
              </div>
              <div class="beHospitalizedTitle">检查嘱咐</div>
              <div class="beHospitalizedInput">
                <el-input type="textarea" style="width: 60%" :autosize="{ minRows: 4, maxRows: 8 }"
                          placeholder="检查嘱咐" v-model="beHospitalizedInformModel.remark"
                >
                </el-input>
                <div style="display: flex">
                  <el-button type="primary" round @click="getBeHospitalizedInformTemplateBut">模板</el-button>
                </div>
              </div>
              <div class="beHospitalizedMessage">
                <div class="beHospitalizedMessageItem">
                  <div>发送人：</div>
                  <div class="beHospitalizedMessageItemSon">
                    {{ beHospitalizedInformModel.doctorName }}
                  </div>
                </div>
                <div class="beHospitalizedMessageItem">
                  <div>发送位置：</div>
                  <div class="beHospitalizedMessageItemSon">
                    {{ beHospitalizedInformModel.deptName }}
                  </div>
                </div>
              </div>
            </div>
            <div style="display: flex; justify-content: center; margin-top: 35px">
              <el-button type="primary" round @click="sendBeHospitalizedInform">发送</el-button>
              <el-button type="danger" round @click="beHospitalizedInformButton = false">关闭</el-button>
            </div>
          </el-dialog>
        </div>

        <div class="dialog-two">
          <el-dialog :visible.sync="beHospitalizedInformTemplateButton" title="模板选择"
                     :width="myStyle.dialog.three.dialogWidth"
          >
            <div style="max-height: 530px">
              <el-scrollbar style="height: 95%; overflow-x: hidden">
                <div v-for="src in beHospitalizedInformTemplateList" :key="src.index" style="
                    display: flex;
                    justify-content: center;
                    margin-bottom: 10px;
                  "
                >
                  <div style="width: 60%; border: 1px solid #dcdfe6; padding: 10px">{{ src }}</div>
                  <div style="display: flex; align-items: center">
                    <el-button type="primary" round @click="choiceBeHospitalizedInformTemplate(src)">选择</el-button>
                  </div>
                </div>
              </el-scrollbar>
            </div>
            <div style="display: flex; justify-content: center; margin-top: 35px">
              <el-button type="danger" round @click="beHospitalizedInformTemplateButton = false">关闭</el-button>
            </div>
          </el-dialog>
        </div>

        <div class="dialog-two">
          <el-dialog :visible.sync="patientMessageButton" title="患者信息填写" width="50%">
            <div class="dialog-patient">
              <!--        中间时间选项-->
              <div class="patient-fiex">
                <el-button round @click="saveClickMark">读卡</el-button>
                <el-button round @click="saveClickMark">扫码</el-button>  
              </div>
              <div class="patient-fiex">
                <el-input style="width: 200px;" v-model="patientId" placeholder="请输入患者id号"></el-input>
              </div>
              <div class="patient-fiex" style="margin-top: 20px;">
                <el-button type="primary" round @click="patientIdMonitor">确定</el-button>
              </div>
            </div>
          </el-dialog>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import man from '@/assets/icons/svg/man.png'
import woMan from '@/assets/icons/svg/woman.png'
import {
  GetReservationCenterInfo,
  GetAppointmentTimeList,
  TimeAppointment,
  GetUnitOrWindowTree,
  GetUnitAndWindowTree,
  GetWindowTimeMessage,
  GetCallQueueList,
  DeleteCallQueueByQueueId,
  CallTransfer,
  RecoverCallQueue,
  UpdateMark,
  GetReservationCenterInfoTwo,
  GetCallQueueMessage,
  GetTimeDispose,
  GetAppointmentListFormUnitTree,
  GetAppointmentListFormWindowTree,
  GetAppointmentList,
  GetManualInputFormUnitTree,
  GetManualInputFormFormWindowTree,
  GetManualInputFormPatient,
  SaveManualInputForm,
  GetBeHospitalizedInformMessage,
  GetBeHospitalizedInformTemplate,
  SendBeHospitalizedInform
} from '@/api/checkAndConfirm/checkAppointment'
import answerQuestions from '@/assets/icons/answerQuestions1.png'

export default {
  name: 'reservationCenter',
  data() {
    return {
      textTime: '',
      answerQuestionsImg: answerQuestions,
      manImg: man,
      woManImg: woMan,
      caseList: [],
      myData: {
        user: {
          sex: ''
        },
        case: {
          inspectionPurpose: ''
        }
      },
      myStyle: {
        home: {
          minHeight: 'height: 753px;'
        },
        left: {
          top: 'height: 40px;background-size: 100% auto;',
          user: {
            height: 'height:130px;',
            info: 'height: 100px;',
            title: 'margin-top: 10px;height: 25px;font-size: 18px;',
            img: 45,
            size: 'font-size: 12px;',
            exam: 'height: 29px;font-size: 12px;'
          },
          case: {
            height: 'height: 545px;',
            title: 'font-size: 15px;',
            span: 'font-size:10px;',
            size: 3,
            table: {
              height: 'height: 150px;',
              size1: 'font-size:22px;',
              szie2: '',
              width1: 'width:40%',
              width2: 'width:50%',
              one: false,
              two: false,
              three: true
            }
          }
        },
        right: {
          top: 'height: 40px;font-size: 24px;',
          ontSource: 'width: 35%',
          twoSource: 'width: 45%',
          ontSourceBut: true,
          table: 110,
          img: 'width: 20px;margin-top: 5px',
          time: {
            height: 'height: 600px',
            radioButtonPadding: 'padding:6px 18px;',
            radioButtonFontSize: '14px',
            width: 'width: 13%;font-size: 14px;'
          },
          task: {
            one: 'width: 20%;border-radius: 40px;margin-top: 2%',
            two: 'padding: 10px 20px;border-radius: 20px;width: 20%;margin-top: 3%',
            three:
              'padding: 10px 10px;border-radius: 20px;width: 20%;margin-top: 3%',
            four: ' margin-right: 8%;width: 20%;float: right; padding: 10px 10px;margin-top: 1%',
            five: 'margin-right: 0%;float: right;border-radius: 20px;width: 20%;padding: 10px 10px;',
            six: 'float: right;width: 20%;border-radius: 20px;padding: 10px 10px;',
            image1: 'margin: 7% 20px;margin: 4% 20px',
            image2: 'width:17%;height:27%',
            image3: 'margin: 2% 20px;',
            size: 'font-size: 14px;'
          }
        },
        drawer: {
          size: '35%',
          table: {
            width1: '50',
            width2: '90',
            width3: '60',
            width4: '90',
            width5: '90',
            width6: '70',
            width7: '95'
          },
          height: '300',
          height2: '500',
          text1:
            'width: 100%;height: 35px;background: red;display: flex;justify-content: space-around;align-items: center;font-size: 24px;color: #ffffff',
          text2:
            'width: 100%;height: 35px;background: #20B2AA;display: flex;justify-content: space-around;align-items: center;font-size: 24px;color: #ffffff',
          tableThree: false,
          tableTwo: false,
          tableOne: true
        },
        dialog: {
          ont: {
            width: '90%',
            table: {
              table1: '75',
              table2: '100',
              table3: '80',
              table4: '90',
              table5: '120',
              table6: '100',
              table7: '110',
              table8: '150',
              table9: '78'
            }
          },
          two: {
            dialogWidth: '70%',
            form: {
              width1: 'width: 180px;',
              width2: 'width: 180px;',
              width3: 'width: 180px;',
              width4: 'width: 180px;'
            },
            table: {
              height: '650',
              width1: '',
              width2: '100',
              width3: '120',
              width4: '95',
              width5: '120',
              width6: '100',
              width7: '110',
              width8: '110'
            }
          },
          three: {
            dialogWidth: '50%'
          }
        }
      },
      empNo: this.$store.getters.empNo,
      // patientId: this.$store.getters.patient.patientId,
      patientId: this.$store.getters.patient.patientId,
      deptCode: '020901',
      // deptCode: this.$store.getters.deptCode,
      callQueueButton: false,
      callQueueList: [],
      alreadyQueueList: [],
      callQueueTitle: '',
      transferButton: false,
      transferModel: {},
      timeModel: {},
      timeData: [],
      deptName: '',
      applyDate: '',
      applyTime: '',
      markModel: {},
      markButton: false,
      drawerTable: 1,
      autherEntity: {
        autherKey: 'DA12FB821147938CA09641D3B51365C5',
        userGuid: '',
        serialNumber: '',
        doctorGuid: '',
        doctorName: '',
        department: '',
        hospitalGuid: '1',
        hospitalName: '河南宏力医院',
        customEnv: '1',
        flag: 'm'
      },
      windowId: '',
      unitId: '',
      callSelectButton: false,
      callSelectTable: [],
      callSelectForm: {
        patient: '',
        unitId: '',
        unitName: ''
      },
      callSelectDialogTitle: '',
      timeUpdateButton: false,
      timeThisTime: '',
      timeThisList: [],
      appointmentListButton: false,
      appointmentListModel: [],
      appointmentListTitle: '',
      appointmentListForm: {},
      appointmentTimeTypeDict: [
        {
          value: '0',
          label: '全部'
        },
        {
          value: '凌晨',
          label: '凌晨'
        },
        {
          value: '早晨',
          label: '早晨'
        },
        {
          value: '上午',
          label: '上午'
        },
        {
          value: '中午',
          label: '中午'
        },
        {
          value: '下午',
          label: '下午'
        },
        {
          value: '傍晚',
          label: '傍晚'
        },
        {
          value: '晚上',
          label: '晚上'
        }
      ],
      manualInputButton: false,
      manualInputForm: [],
      manualInputTitle: '',
      beHospitalizedInformButton: false,
      beHospitalizedInformTitle: '',
      beHospitalizedInformForm: {
        itemList: []
      },
      beHospitalizedInformModel: {},
      beHospitalizedInformTemplateList: [],
      beHospitalizedInformTemplateButton: false,
      patientMessageButton: false
    }
  },
  created() {
    if (!this.patientId) {
      this.$msgbox.alert(
        '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' + '请先进行读卡/扫码/手动输入患者id' + '</div>' +
        '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">点击确定按钮进行后续操作!!!</div>',
        '系统提示',
        {
          confirmButtonText: '确定',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      ).then(() => {
        this.patientMessageButton = true
      })
      return
    }
    if (!this.empNo) {
      this.$msgbox.alert(
        '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' + '系统登录超时!请F5刷新页面重新操作' + '</div>',
        '系统提示',
        {
          confirmButtonText: '确定',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      ).then(() => {
      })
      return
    }
    this.getReservationCenterInfo()
  },
  methods: {
    //patientId监听
    patientIdMonitor() {
      if (!this.patientId) {
        this.$msgbox.alert(
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' + '请先进行读卡/扫码/手动输入患者id' + '</div>' +
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">进行以上操作后再进行按钮确定!!!</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        ).then(() => {
        })
        return
      }
      this.getReservationCenterInfo();
      this.patientMessageButton = false;
    },
    //修改点击的号源信息
    saveClickMark() {
      let data = this.markModel
      if (data) {
        UpdateMark(data).then((res) => {
          if (res.code === 200) {
            this.$message.success(res.message)
            this.recoverCallQueueTwo(data.unitId, data.windowId)
          } else {
            this.$msgbox.alert(
              '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' + res.message + '</div>',
              '系统提示',
              {
                confirmButtonText: '确定',
                type: 'warning',
                dangerouslyUseHTMLString: true
              }
            ).then(() => {
            })
          }
        })
      } else {
        this.$msgbox.alert(
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
          '修改号源信息数据错乱,请联系信息部处理!!!</div>', '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        ).then(() => {
        })
      }
    },
    //双击号源
    doubleClickMark(row) {
      this.markModel = row
      this.markButton = true
    },
    //恢复叫号信息
    recoverCallQueue(row) {
      let data = {
        id: row.id,
        empNo: this.empNo
      }
      RecoverCallQueue(data).then((res) => {
        if (res.code === 200) {
          this.$message.success(res.message)
          this.$msgbox.alert(
            '<div style="font-size: 36px !important; text-align: center;font-weight: 800">队列号</div>' +
            '<div style="font-size: 72px !important; text-align: center;margin-top: 50px;">' + res.data + '</div>',
            '系统提示',
            {
              confirmButtonText: '确定',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }
          ).then(() => {
          })
          this.recoverCallQueueTwo(row.unitId, row.windowId)
        }
      })
    },
    recoverCallQueueTwo(unitId, windowId) {
      let t = this.myData
      let data = {
        unitId: unitId,
        windowId: windowId,
        applyDate: t.thisTime
      }
      GetCallQueueList(data).then((res) => {
        this.callQueueList = res.data.callQueueList
        this.alreadyQueueList = res.data.alreadyQueueList
        this.markButton = false
        this.markModel = {}
      })
    },
    //住院通知按钮
    beHospitalizedInformBut() {
      let data = this.myData
      let table = data.table[0]
      this.beHospitalizedInformForm = {
        tableList: data.table,
        empNo: this.empNo
      }
      this.beHospitalizedInformTitle = table.executeDeptName + ' -> 住院消息通知'
      GetBeHospitalizedInformMessage(this.beHospitalizedInformForm).then((res) => {
          if (res.code === 200) {
            this.beHospitalizedInformModel = res.data
            this.beHospitalizedInformModel.clickItemList = []
            this.beHospitalizedInformButton = true
          }
        }
      )
    },
    //住院通知模板获取
    getBeHospitalizedInformTemplateBut() {
      GetBeHospitalizedInformTemplate().then((res) => {
        if (res.code === 200) {
          this.beHospitalizedInformTemplateList = res.data
          this.beHospitalizedInformTemplateButton = true
        }
      })
    },
    //住院通知模板选择
    choiceBeHospitalizedInformTemplate(text) {
      this.beHospitalizedInformModel.remark = text
      this.beHospitalizedInformTemplateButton = false
    },
    //住院通知发送
    sendBeHospitalizedInform() {
      SendBeHospitalizedInform(this.beHospitalizedInformModel).then((res) => {
        if (res.code === 200) {
          this.$msgbox.alert(
            '<div style="font-size: 18px !important;color: red">' + res.message + '</div>',
            '系统提示',
            {
              confirmButtonText: '确定',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }
          ).then(() => {
          }).catch(() => {
          })
        }
      })
    },
    //手动登记事件   //需要获取 unit，window，tableList
    manualInputBut() {
      let data = this.myData
      let one = data.table[0]
      let table = data.table
      let thisTable = []
      let unitId = ''
      let unitName = ''
      let windowId = ''
      let windowName = ''
      let thisExamList = []
      this.manualInputTitle = one.executeDeptName + ' -> 叫号登记'
      table.forEach(function(item, index) {
        if (!item.status || item.status !== '4') {
          thisTable.push(item)
          one = item
        }
      })
      if (thisTable.length > 0) {
        thisExamList = [one.examNo]
      }
      this.manualInputForm = {
        unitId: unitId,
        unitName: unitName,
        unitTree: [],
        windowId: windowId,
        windowName: windowName,
        windowTree: [],
        patientId: data.user.patientId,
        patientName: data.user.name,
        sex: data.user.sex,
        priority: '3',
        deptCode: one.executeDeptCode,
        deptName: one.executeDeptName,
        tableList: thisTable,
        thisTable: thisExamList,
        empNo: this.empNo
      }
      //获取执行科室单元列表
      GetManualInputFormUnitTree(one.executeDeptCode).then((res) => {
        let unitData = res.data
        this.manualInputForm.unitTree = unitData.unitTree
        this.manualInputForm.unitId = unitData.thisUnitId
        this.manualInputForm.unitName = unitData.thisUnitName
        GetManualInputFormFormWindowTree(this.manualInputForm.unitId).then(
          (res) => {
            let windowData = res.data
            this.manualInputForm.windowTree = windowData.windowTree
            this.manualInputForm.windowId = windowData.thisWindowId
            this.manualInputForm.windowName = windowData.thisWindowName
          }
        )
      })
      this.manualInputButton = true
    },
    //手动登记  点击预约地点 处理
    manualInputUnitDisplay() {
      GetManualInputFormFormWindowTree(this.manualInputForm.unitId).then((res) => {
          let windowData = res.data
          this.manualInputForm.windowTree = windowData.windowTree
          this.manualInputForm.windowId = windowData.thisWindowId
          this.manualInputForm.windowName = windowData.thisWindowName
        }
      )
    },
    //患者信息搜索
    manualInputPatientSelect() {
      this.manualInputForm.patientName = ''
      this.manualInputForm.sex = ''
      GetManualInputFormPatient(this.manualInputForm.patientId).then((res) => {
        this.manualInputForm.patientName = res.data.label
        this.manualInputForm.sex = res.data.son
      })
    },
    //手动登记保存
    manualInputSave() {
      if (!this.manualInputForm.patientName) {
        this.$msgbox.alert(
          '<div style="font-size: 18px !important;color: red">' + '请先进行检索操作,查询患者信息是否正确' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
        ).then(() => {
        }).catch(() => {
        })
        return
      }
      SaveManualInputForm(this.manualInputForm).then((res) => {
        if (res.code === 200) {
          this.$message.success('预约成功')
          this.$msgbox.alert(
            '<div style="font-size: 36px !important; text-align: center;font-weight: 800">队列号</div>' +
            '<div style="font-size: 72px !important; text-align: center;margin-top: 50px;">' + res.data + '</div>',
            '系统提示',
            {
              confirmButtonText: '确定',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }
          ).then(() => {
          })
          // window.open('','_self').close()
        }
      })
    },
    //预约列表事件
    appointmentListBut() {
      let data = this.myData
      let one = data.table[0]
      this.textTime = ''
      let unitId = data.thisUnitId
      let windowId = data.thisWindowId
      if (!unitId) {
        unitId = '0'
        windowId = '0'
      }
      this.appointmentListTitle = one.executeDeptName + ' -> 预约列表详情'
      this.appointmentListForm = {
        thisTime: data.thisTime,
        timeType: '0',
        unitId: unitId,
        unitTree: [],
        windowId: windowId,
        windowTree: [],
        deptCode: one.executeDeptCode
      }
      //获取执行科室单元列表
      GetAppointmentListFormUnitTree(one.executeDeptCode).then((res) => {
        this.appointmentListForm.unitTree = res.data
      })
      GetAppointmentListFormWindowTree(unitId).then((res) => {
        this.appointmentListForm.windowTree = res.data
      })
      this.appointmentListSelect()
      this.appointmentListButton = true
    },
    //预约列表，点击单元事件
    appointmentListFormUnitTreeClick() {
      GetAppointmentListFormWindowTree(this.appointmentListForm.unitId).then((res) => {
          this.appointmentListForm.windowTree = res.data
          this.appointmentListForm.windowId = '0'
        }
      )
    },
    //预约列表，搜索功能
    appointmentListSelect() {
      GetAppointmentList(this.appointmentListForm).then((res) => {
        this.appointmentListModel = res.data
      })
    },
    appointmentTimeDisplay() {
      let date = this.appointmentListForm.thisTime
      this.appointmentListForm.thisTime =
        this.$moment(date).format('YYYY-MM-DD')
    },
    //日期调整Button
    timeUpdateBut() {
      let data = this.myData
      this.timeThisTime = data.thisTime
      this.timeThisList = data.timeList
      this.timeUpdateButton = true
    },
    //时间选择处理
    timeUpdateDisplay() {
      let date = this.timeThisTime
      this.timeThisTime = this.$moment(date).format('YYYY-MM-DD')
      GetTimeDispose(this.timeThisTime).then((res) => {
        this.timeThisList = res.data.timeList
      })
    },
    //时间提交处理
    timeSaveDisplay() {
      this.myData.thisTime = this.timeThisTime
      this.myData.timeList = this.timeThisList
      this.timeThisTime = ''
      this.timeThisList = []
      this.timeModelDisplay(this.myData, 2)
      this.timeUpdateButton = false
    },
    //转出按钮操作
    saveTransferTwo() {
      let data = this.transferModel
      //提取正确的windowid
      let windowId = ''
      let tree = data.windowTree
      let windowName = data.newWindowName
      tree.forEach(function(item, index) {
        if (windowName === item.label) {
          windowId = item.value
        }
      })
      this.$msgbox.alert(
        '<div style="font-size: 18px !important;color: red">' +
        '你确定要将当前人员叫号信息转出到《' + data.newWindowName + '》队列当中吗？' + '</div>',
        '系统提示',
        {
          confirmButtonText: '确定',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      ).then(() => {
        let dto = {
          id: data.id,
          windowId: data.windowId,
          unitId: data.unitId,
          newWindowId: windowId,
          newUnitId: data.newUnitId,
          empNo: this.empNo,
          queueId: data.patientId,
          queueName: data.patientName,
          examItem: data.examItem,
          sex: data.sex,
          applyDeptCode: data.applyDeptCode
        }
        CallTransfer(dto).then((res) => {
          if (res.code === 200) {
            this.$message.success('转出成功')
            this.$msgbox.alert(
              '<div style="font-size: 36px !important; text-align: center;font-weight: 800">队列号</div>' +
              '<div style="font-size: 72px !important; text-align: center;margin-top: 50px;">' + res.data + '</div>',
              '系统提示',
              {
                confirmButtonText: '确定',
                type: 'warning',
                dangerouslyUseHTMLString: true
              }
            ).then(() => {
            })
          }
        })
      }).catch(() => {
      }).finally((t) => {
        this.transferButton = false
        this.callQueueButton = false
      })
    },
    //转出按钮操作
    transferBut(row) {
      this.transferModel = {}
      //转移处理事件，需要准备 检查号，unitId,unitName,windowId,windowName,unitTree,windowName
      //applyDate,applyTime,patientId,examNo, timeList,examClass,examSubClass,ExamItem,ExamItemCode,TimeTye,TimeTypeList
      let t = this.myData
      let examNo = t.examNo
      let exam = {}
      let table = this.myData.table
      let windowId = row.windowId
      let windowName = ''
      let winTree = t.windowTree
      table.forEach(function(item, index) {
        if (examNo === item.examNo) {
          exam = item
        }
      })
      winTree.forEach(function(item, index) {
        if (windowId === item.value) {
          windowName = item.label
        }
      })
      let data = {
        id: row.id,
        unitId: row.unitId,
        windowId: windowId,
        newUnitId: row.unitId,
        newUnitName: t.thisUnitName,
        newWindowId: windowId,
        newWindowName: windowName,
        unitTree: t.unitTree,
        windowTree: winTree,
        applyDate: t.thisTime,
        patientId: t.user.patientId,
        examNo: examNo,
        timeDateList: t.timeList,
        timeItemList: this.timeData,
        source: t.source,
        timeType: t.timeType,
        examClass: exam.examClass,
        examSubClass: exam.examSubClass,
        examItem: exam.examItem,
        examItemCode: exam.examItemCode,
        sex: t.user.sex,
        patientName: t.user.name,
        applyDeptCode: exam.applyDeptCode
      }
      this.transferModel = data
      this.transferButton = true
    },
    //删除叫号信息
    deleteCallQueueByQueueId(row) {
      this.$confirm('确定要删除' + row.queueName + '的叫号信息吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          empNo: this.empNo,
          patientId: row.queueId,
          unitId: row.unitId,
          windowId: row.windowId
        }
        DeleteCallQueueByQueueId(data).then((res) => {
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: res.message
            })
            this.recoverCallQueueTwo(row.unitId, row.windowId)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    //叫号搜索
    callQueueSelect(item) {
      this.callSelectTable = []
      this.callSelectForm = {
        patient: this.myData.user.patientId,
        unitId: item.value,
        unitName: item.label
      }
      this.callSelectDialogTitle = item.label + ' -> 叫号信息查询'
      this.callSelectButton = true
      this.executeCallSelect()
    },
    //执行叫号搜索
    executeCallSelect() {
      GetCallQueueMessage(this.callSelectForm).then((res) => {
        this.callSelectTable = res.data
      })
    },
    //叫号队列按钮事件
    callQueueBut(windowId, windowName) {
      this.getCallQueueList(windowId, windowName)
      this.callQueueButton = true
    },
    //获取叫号信息
    getCallQueueList(windowId, windowName) {
      this.callQueueList = []
      let data = this.myData
      this.callQueueTitle = data.thisUnitName + ' >>> ' + windowName + '  队列信息'
      let thisData = {
        unitId: data.thisUnitId,
        windowId: windowId,
        applyDate: data.thisTime
      }
      GetCallQueueList(thisData).then((res) => {
        this.callQueueList = res.data.callQueueList
        this.alreadyQueueList = res.data.alreadyQueueList
      })
    },
    // 检查项目点击切换处理
    tableItemDisplay(data) {
      let table = this.myData.table
      table.forEach(function(item, index) {
        if (item.color === '1') {
          if (item.appointmentTime === null || item.appointmentTime === '' || item.appointmentTime === undefined) {
            item.color = '2'
          } else {
            item.color = '0'
          }
          if (item.status !== null && item.status !== '' && item.status !== undefined) {
            if (item.status === '4') {
              item.color = '3'
            }
          }
        }
      })
      let cases = {
        clinDiag: data.clinDiag,
        clinSymp: data.clinSymp,
        inspectionPurpose: data.inspectionPurpose,
        physSign: data.physSign,
        relevantDiag: data.relevantDiag,
        relevantLabTest: data.relevantLabTest
      }
      let caseList = []
      caseList.push(cases)
      this.caseList = caseList
      this.myData.user.appointmentDate = data.createDate3
      this.myData.case = cases
      this.myData.source = data.source
      this.myData.examItem = data.examItem
      this.myData.examNo = data.examNo
      this.myData.status = data.status
      data.color = '1'

      //获取到数据后，加载预约地点、预约窗口、日期选择选项
      //先获取加载地点后  再 获取日期选择
      let unitDates = {
        itemCode: data.examItemCode,
        executeDeptCode: data.executeDeptCode,
        windowId: this.myData.thisWindowId,
        unitId: this.myData.thisUnitId,
        examNo: data.examNo,
        tableList: table
      }
      GetUnitAndWindowTree(unitDates).then((res) => {
        this.myData.timeType = res.data.timeType
        this.myData.thisUnitId = res.data.thisUnitId
        this.myData.thisUnitName = res.data.thisUnitName
        this.myData.thisWindowId = res.data.thisWindowId
        this.myData.thisWindowName = res.data.thisWindowName
        this.myData.unitTree = res.data.unitTree
        this.myData.windowTree = res.data.windowTree
        this.timeModelDisplay(this.myData, 2)
        if (mayson) {
          let dataTwo = this.myData
          this.createInit(
            dataTwo.user.patientId,
            dataTwo.user.serialNumber,
            data.executeDeptCode,
            data.applyDoctorName,
            data.executeDeptName
          )
        }
      })
    },
    //点击预约窗口信息
    windowIdDisplay() {
      let data = this.myData
      let windowTree = data.windowTree
      let windowId = ''
      let windowName = data.thisWindowName
      windowTree.forEach(function(item, index) {
        if (item.label === windowName) {
          windowId = item.value
        }
      })
      this.myData.thisWindowId = windowId
      this.myData.thisWindowName = windowName
      this.timeModelDisplay(this.myData, 3)
    },
    //点击预约单元 处理
    unitIdDisplayTwo() {
      let t = this.transferModel
      let newUnitName = t.newUnitName
      let newUnitId = ''
      let unitTree = t.unitTree
      unitTree.forEach(function(item, index) {
        if (newUnitName === item.label) {
          newUnitId = item.value
        }
      })
      let data = {
        unitId: newUnitId,
        itemCode: t.examItemCode,
        examNo: t.examNo
      }
      GetUnitOrWindowTree(data).then((res) => {
        this.transferModel.windowTree = res.data.windowTree
        this.transferModel.newWindowId = res.data.thisWindowId
        this.transferModel.newWindowName = res.data.thisWindowName
        this.transferModel.newUnitId = newUnitId
      })
    },
    //
    unitIdDisplay() {
      let data = this.myData
      let unitTree = data.unitTree
      let unitId = ''
      let unitName = data.thisUnitName
      unitTree.forEach(function(item, index) {
        if (item.label === unitName) {
          unitId = item.value
        }
      })
      this.myData.thisUnitId = unitId
      this.myData.thisUnitName = unitName
      this.timeModelDisplay(this.myData, 1)
    },
    //获取初始化信息
    getReservationCenterInfo() {
      const loading = this.$loading({
        lock: true,
        text: "数据正在努力加载中,请耐心等待!!!(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      GetReservationCenterInfo(this.patientId, this.empNo, this.deptCode).then((res) => {
        let data = res.data
        this.myData = data

        let caseList = []
        caseList.push(data.case)
        this.caseList = caseList
        this.timeModelDisplay(res.data, 2)
        this.createInit(
          data.patientId,
          data.serialNumber,
          data.deptCode,
          data.doctorName,
          data.deptName
        )
      }).finally(() => {
        loading.close();
      })
    },
    getReservationCenterInfoTwo() {
      const loading = this.$loading({
        lock: true,
        text: "数据正在努力加载中,请耐心等待!!!(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      let d = {
        patientId: this.patientId,
        unitId: this.unitId,
        deptCode: this.deptCode,
        windowId: this.windowId,
        thisTime: this.myData.thisTime,
        timeList: this.myData.timeList
      }
      GetReservationCenterInfoTwo(d).then((res) => {
        let data = res.data
        this.myData = data
        let caseList = []
        caseList.push(data.case)
        this.caseList = caseList
        this.timeModelDisplay(res.data, 2)
        this.createInit(data.patientId, data.serialNumber, data.deptCode, data.doctorName, data.deptName)
      }).finally(() => {
        loading.close();
      })
    },
    //时间处理
    timeModelDisplay(data, type) {
      let model = {}
      let modelList = data.table
      let examNo = data.examNo
      modelList.forEach(function(item, index) {
        if (examNo === item.examNo) {
          model = item
        }
      })
      if (type === 1) {
        let datas = {
          unitId: data.thisUnitId,
          itemCode: model.examItemCode,
          examNo: model.examNo,
          executeDeptCode: model.executeDeptCode,
          tableList: modelList
        }
        GetUnitOrWindowTree(datas).then((res) => {
          this.myData.timeType = res.data.timeType
          this.myData.windowTree = res.data.windowTree
          this.myData.thisWindowName = res.data.thisWindowName
          this.myData.thisWindowId = res.data.thisWindowId
          this.transferModel.windowTree = res.data.windowTree
          this.transferModel.newWindowId = res.data.thisWindowId
          this.transferModel.newWindowName = res.data.thisWindowName
          this.timeModel = {
            examClass: model.examClass,
            examSubClass: model.examSubClass,
            examItem: model.examItem,
            examItemCode: model.examItemCode,
            examNo: model.examNo,
            appointmentDate: data.thisTime,
            timeType: this.myData.timeType,
            money: model.costs,
            unitId: data.thisUnitId,
            windowId: data.thisWindowId,
            executeDeptCode: model.executeDeptCode,
            tableList: modelList
          }
          this.deptName = model.executeDeptName
          this.applyDate = data.thisTime
          this.applyTime = model.appointmentTime

          this.getAppointmentTimeList()
        })
      } else if (type === 2) {
        this.timeModel = {
          examClass: model.examClass,
          examSubClass: model.examSubClass,
          examItem: model.examItem,
          examItemCode: model.examItemCode,
          examNo: model.examNo,
          appointmentDate: data.thisTime,
          timeType: data.timeType,
          money: model.costs,
          unitId: data.thisUnitId,
          windowId: data.thisWindowId,
          executeDeptCode: model.executeDeptCode,
          tableList: modelList
        }
        this.deptName = model.executeDeptName
        this.applyDate = data.thisTime
        this.applyTime = model.appointmentTime

        this.getAppointmentTimeList()
      } else if (type === 3) {
        let datas = {
          unitId: data.thisUnitId,
          itemCode: model.examItemCode,
          examNo: model.examNo,
          windowId: data.thisWindowId,
          executeDeptCode: model.executeDeptCode,
          tableList: modelList
        }
        GetWindowTimeMessage(datas).then((res) => {
          this.myData.timeType = res.data
          this.timeModel = {
            examClass: model.examClass,
            examSubClass: model.examSubClass,
            examItem: model.examItem,
            examItemCode: model.examItemCode,
            examNo: model.examNo,
            appointmentDate: data.thisTime,
            timeType: this.myData.timeType,
            money: model.costs,
            unitId: data.thisUnitId,
            windowId: data.thisWindowId,
            executeDeptCode: model.executeDeptCode,
            tableList: modelList
          }
          this.deptName = model.executeDeptName
          this.applyDate = data.thisTime
          this.applyTime = model.appointmentTime

          this.getAppointmentTimeList()
        })
      }
    },
    //获取时间集合
    getAppointmentTimeList() {
      GetAppointmentTimeList(this.timeModel).then((res) => {
        this.timeData = res.data
      })
    },
    //数据保存
    timeAppointment(data) {
      let myData = this.myData
      if (data.surplus === 0) {
        this.$msgbox
          .alert(
            '<div style="font-size: 18px !important;color: red">' + '当前时间点预约人员已满,请选择其他时间点预约!!!' + '</div>',
            '系统提示',
            {
              confirmButtonText: '确定',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }).then(() => {
        }).catch(() => {
        })
        return
      }
      if (myData.status === '4') {
        this.$msgbox.alert(
          '<div style="font-size: 18px !important;color: red">' +
          '当前项目已结束,不要随意更改数据!!!' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        }).catch(() => {
        })
        return
      }
      if (!data.status) {
        this.$msgbox.alert(
          '<div style="font-size: 18px !important;color: red">' +
          '当前选择时间已过,请选择其他时间预约!!!' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        }).catch(() => {
        })
        return
      }
      this.$msgbox.alert(
        '<div style="font-size: 18px !important;">' +
        '<div style="display: flex"><div style="width: 30%;font-size: 18px;padding-left: 10%;">姓名：</div><div style="font-size: 16px;color: black;">' +
        myData.user.name + '</div></div>' +
        '<div style="display: flex"><div style="width: 30%;font-size: 18px;padding-left: 5%;">检查号：</div><div style="font-size: 16px;color: black;">' +
        myData.user.examNo + '</div></div>' +
        '<div style="display: flex"><div style="width: 30%;font-size: 18px;">检查项目：</div><div style="font-size: 16px;color: black;">' +
        myData.examItem + '</div></div>' +
        '<div style="display: flex"><div style="width: 30%;font-size: 18px;">预约日期：</div><div style="font-size: 16px;color: black;">' +
        myData.thisTime + '</div></div>' +
        '<div style="display: flex"><div style="width: 30%;font-size: 18px;">预约时间：</div><div style="font-size: 16px;color: black;">' +
        data.value + '</div></div>' +
        '<div style="display: flex"><div style="width: 30%;font-size: 18px;margin-top: 10px;">叫号单元：</div><div style="font-size: 32px;color: black;margin-top: 10px;">' +
        myData.thisUnitName + '</div></div>' +
        '<div style="display: flex"><div style="width: 30%;font-size: 18px;margin-top: 10px;">叫号窗口：</div><div style="font-size: 32px;color: black;margin-top: 10px;">' +
        myData.thisWindowName + '</div></div>' +
        '<div style="font-size: 24px;margin-top: 10px;color: red">请确认以上信息是否正确!!!</div>' + '</div>',
        '系统提示',
        {
          confirmButtonText: '确定',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }).then(() => {
        let examNo = myData.examNo
        let examModel = {}
        let examTable = myData.table
        examTable.forEach(function(item, index) {
          if (examNo === item.examNo) {
            examModel = item
          }
        })
        let model = {
          applyDeptCode: examModel.applyDeptCode,
          examNo: examModel.examNo,
          patientId: myData.user.patientId,
          patientName: myData.user.name,
          sex: myData.user.sex,
          visitId: myData.user.visitId,
          empNo: this.empNo,
          appointmentTime: data.value,
          timeId: data.id,
          appointmentDate: myData.thisTime,
          examClassName: examModel.examClass,
          examSubClassName: examModel.examSubClass,
          description: examModel.examItem,
          descriptionCode: examModel.examItemCode,
          money: examModel.costs,
          executeDeptCode: examModel.executeDeptCode,
          executeDeptName: examModel.executeDeptName,
          attention: examModel.attention,
          positionName: examModel.examPositionName,
          positionCode: examModel.examPositionCode,
          unitId: myData.thisUnitId,
          windowId: myData.thisWindowId
        }
        if (myData.thisUnitId && myData.thisWindowId) {
          this.unitId = myData.thisUnitId
          this.windowId = myData.thisWindowId
        } else {
          this.unitId = ''
          this.windowId = ''
        }

        TimeAppointment(model).then((res) => {
          if (res.code === 200) {
            this.$message.success('预约成功')
            this.$msgbox.alert(
              '<div style="font-size: 36px !important; text-align: center;font-weight: 800">队列号</div>' +
              '<div style="font-size: 72px !important; text-align: center;margin-top: 50px;">' +
              res.data + '</div>',
              '系统提示',
              {
                confirmButtonText: '确定',
                type: 'warning',
                dangerouslyUseHTMLString: true
              }).then(() => {
            })
            // window.open('','_self').close()
            this.getReservationCenterInfoTwo()
            if (mayson === undefined) {
              this.getAppointmentTimeList()
            }
          }
        })
      }).catch(() => {
        this.$message.info('已取消预约')
      })
    },
    //ai查看按钮
    AiButton(item) {
      this.knowledgeBase(item)
    },
    //惠美知识库参数查看
    knowledgeBase(item) {
      let data = {
        name: item.examItem,
        type: '12',
        id: item.examItemCode,
        customEnv: '1',
        isActicleDetail: '1'
      }
      mayson.openArticleDetail(data)
    }
  },
  mounted() {
    this.$nextTick(() => {
      const bodyStyle = document.body.style, // 获取body节点样式
        htmlStyle = document.getElementsByTagName('html')[0].style, // 获取html节点样式
        docEl = document.documentElement,
        docBody = document.body,
        winWidth = docEl.clientWidth || docBody.clientWidth, // 获取浏览器的宽度
        WinHeight = docEl.clientHeight || docBody.clientHeight // 获取浏览器的高
      this.bodyHeight = 'height:' + WinHeight + 'px'
      bodyStyle.minWidth = '1014px'
      bodyStyle.minHeight = '768px'
      htmlStyle.minHeight = '768px'
      htmlStyle.minWidth = '1014px'
      if (winWidth <= 1200) {
        this.myStyle = {
          home: {
            minHeight: 'height: 690px;'
          },
          left: {
            top: 'height: 2px;background-size: 100% auto;',
            user: {
              height: 'height:130px;',
              info: 'height: 100px;',
              title: 'margin-top: 10px;height: 25px;font-size: 18px;',
              img: 45,
              size: 'font-size: 12px;',
              exam: 'height: 29px;font-size: 12px;'
            },
            case: {
              height: 'height: 545px;',
              title: 'font-size: 15px;',
              span: 'font-size:10px;',
              size: 3,
              table: {
                height: 'height: 150px;',
                size1: 'font-size:16px;',
                size2: 'font-size: 12px;',
                width1: 'width:40%',
                width2: 'width:50%',
                one: false,
                two: false,
                three: true
              }
            }
          },
          right: {
            top: 'height: 2px;font-size: 24px;',
            ontSource: 'width: 35%',
            twoSource: 'width: 60%',
            ontSourceBut: true,
            table: 120,
            img: 'width: 25px;margin-top: 5px',
            time: {
              height: 'height: 495px',
              radioButtonPadding: 'padding:6px 18px;',
              radioButtonFontSize: '14px',
              width: 'width: 13%;font-size: 14px;'
            },
            task: {
              one: 'padding: 6px 5px;border-radius: 20px;width: 20%;margin-top: 1%',
              two: 'padding: 6px 5px;border-radius: 20px;width: 20%;margin-top: 1%',
              three:
                'padding: 6px 5px;border-radius: 20px;width: 20%;margin-top: 1%',
              four: ' margin-right: 8%;width: 20%;float: right; padding: 6px 5px;margin-top: 1%',
              five: 'margin-right: 0%;float: right;border-radius: 20px;width: 20%;padding: 6px 5px',
              six: 'float: right;width: 20%;border-radius: 20px;padding: 6px 5px',
              image1: 'margin: 7% 20px;margin: 2% 20px',
              image2: 'width:17%;height:27%',
              image3: 'margin: 2% 20px;',
              size: 'font-size: 14px;'
            }
          },
          drawer: {
            size: '70%',
            table: {
              width1: '50',
              width2: '90',
              width3: '60',
              width4: '75',
              width5: '75',
              width6: '70',
              width7: '95'
            },
            height: '600',
            text1:
              'width: 100%;height: 25px;background: red;display: flex;justify-content: space-around;align-items: center;font-size: 20px;color: #ffffff',
            text2:
              'width: 100%;height: 25px;background: #20B2AA;display: flex;justify-content: space-around;align-items: center;font-size: 20px;color: #ffffff',
            tableThree: true,
            tableTwo: false,
            tableOne: false
          },
          dialog: {
            ont: {
              width: '90%',
              table: {
                table1: '70',
                table2: '70',
                table3: '70',
                table4: '70',
                table5: '90',
                table6: '80',
                table7: '80',
                table8: '100',
                table9: '78'
              }
            },
            two: {
              dialogWidth: '90%',
              form: {
                width1: 'width: 160px;',
                width2: 'width: 100px;',
                width3: 'width: 110px;',
                width4: 'width: 110px;'
              },
              table: {
                height: '450',
                width1: '',
                width2: '75',
                width3: '80',
                width4: '70',
                width5: '90',
                width6: '100',
                width7: '75',
                width8: '75'
              }
            },
            three: {
              dialogWidth: '80%'
            }
          }
        }
      } else if (winWidth <= 1400) {
        this.myStyle = {
          home: {
            minHeight: 'height: 950px;'
          },
          left: {
            top: 'height: 3px;background-size: 100% auto;',
            user: {
              height: 'height:200px;',
              info: 'height: 150px;',
              title: 'margin-top: 27px;height: 35px;font-size: 22px;',
              img: 55,
              size: 'font-size: 14px;',
              exam: 'height: 50px;font-size: 14px;'
            },
            case: {
              height: 'height: 705px;',
              title: 'font-size: 20px;',
              span: 'font-size:12px;',
              size: 4,
              table: {
                height: 'height: 190px;',
                size1: 'font-size:20px;',
                szie2: 'font-size: 13px',
                width1: 'width:40%',
                width2: 'width:50%',
                one: false,
                two: true,
                three: false
              }
            }
          },
          right: {
            top: 'height: 3px;font-size: 26px;',
            ontSource: 'width: 35%',
            twoSource: 'width: 57%',
            ontSourceBut: false,
            table: 140,
            img: 'width: 25px;margin-top: 5px',
            time: {
              height: 'height: 600px',
              select: 'font-size: 14px;padding: 10px 40px;',
              width: 'width: 10%'
            },
            task: {
              one: 'width: 20%;border-radius: 40px;margin-top: 2%;margin-left: 5%;',
              two: 'padding: 10px 20px;border-radius: 20px;width: 20%;margin-top: 2%',
              three:
                'padding: 10px 10px;border-radius: 20px;width: 20%;margin-top: 2%',
              four: ' margin-right: 6%;width: 20%;float: right; padding: 10px 10px;margin-top: 1%',
              five: 'margin-right: 0%;float: right;border-radius: 20px;width: 20%;padding: 10px 10px;',
              six: 'float: right;width: 20%;border-radius: 20px;padding: 10px 10px;margin-bottom: 1%',
              image1: 'margin: 7% 20px;margin: 3% 20px',
              image2: 'width:17%;height:27%',
              image3: 'margin: 1% 20px;',
              size: '16px;'
            }
          },
          drawer: {
            size: '56%',
            table: {
              width1: '50',
              width2: '90',
              width3: '60',
              width4: '75',
              width5: '75',
              width6: '70',
              width7: '95'
            },
            height: '800',
            text1:
              'width: 100%;height: 35px;background: red;display: flex;justify-content: space-around;align-items: center;font-size: 22px;color: #ffffff',
            text2:
              'width: 100%;height: 35px;background: #20B2AA;display: flex;justify-content: space-around;align-items: center;font-size: 22px;color: #ffffff',
            tableThree: false,
            tableTwo: true,
            tableOne: false
          },
          dialog: {
            ont: {
              width: '80%',
              table: {
                table1: '75',
                table2: '85',
                table3: '70',
                table4: '90',
                table5: '90',
                table6: '90',
                table7: '100',
                table8: '120',
                table9: '78'
              }
            },
            two: {
              dialogWidth: '80%',
              form: {
                width1: 'width: 160px;',
                width2: 'width: 130px;',
                width3: 'width: 140px;',
                width4: 'width: 140px;'
              },
              table: {
                height: '600',
                width2: '75',
                width3: '90',
                width4: '80',
                width5: '95',
                width6: '100',
                width7: '90',
                width8: '90'
              }
            },
            three: {
              dialogWidth: '60%'
            }
          }
        }
      } else {
        this.myStyle = {
          home: {
            minHeight: 'height: 895px;'
          },
          left: {
            top: 'height: 50px;',
            user: {
              height: 'height:200px;',
              info: 'height: 150px;',
              title: 'margin-top: 4px;height: 35px;font-size: 24px;',
              img: 80,
              size: 'font-size: 14px;',
              exam: 'height: 50px;font-size: 14px;'
            },
            case: {
              height: 'height: 630px;',
              title: 'font-size: 20px;',
              span: 'font-size:12px;',
              size: 4,
              table: {
                height: 'height: 170px;',
                size1: 'font-size:22px;',
                szie2: '',
                width1: 'width:40%',
                width2: 'width:50%',
                one: true,
                two: false,
                three: false
              }
            }
          },
          right: {
            top: 'height: 50px;font-size: 28px;',
            ontSource: 'width: 40%',
            twoSource: 'width: 40%',
            ontSourceBut: false,
            table: 120,
            img: 'width: 20px;margin-top: 5px',
            time: {
              height: 'height: 600px',
              select: 'font-size: 14px;padding: 10px 40px;',
              width: 'width: 10%'
            },
            task: {
              one: 'width: 20%;border-radius: 40px;margin-top: 1%;margin-left: 7%;',
              two: 'padding: 10px 20px;border-radius: 20px;width: 20%;margin-top: 1%',
              three:
                'padding: 10px 10px;border-radius: 20px;width: 20%;margin-top: 1%',
              four: ' margin-right: 7%;width: 20%;float: right; padding: 10px 10px;margin-top: 0%',
              five: 'margin-right: 0%;float: right;border-radius: 20px;width: 20%;padding: 10px 10px;margin-top: 0%',
              six: 'float: right;width: 20%;border-radius: 20px;padding: 10px 10px;margin-top: 0%',
              image1: 'margin: 7% 20px;margin: 2% 20px',
              image2: 'width:17%;height:27%',
              image3: 'margin: 1% 20px;',
              size: '16px;'
            }
          },
          drawer: {
            size: '40%',
            table: {
              width1: '50',
              width2: '90',
              width3: '60',
              width4: '90',
              width5: '90',
              width6: '70',
              width7: '95'
            },
            height: '800',
            text1:
              'width: 100%;height: 35px;background: red;display: flex;justify-content: space-around;align-items: center;font-size: 24px;color: #ffffff',
            text2:
              'width: 100%;height: 35px;background: #20B2AA;display: flex;justify-content: space-around;align-items: center;font-size: 24px;color: #ffffff',
            tableThree: false,
            tableTwo: false,
            tableOne: true
          },
          dialog: {
            ont: {
              width: '70%',
              table: {
                table1: '75',
                table2: '100',
                table3: '80',
                table4: '90',
                table5: '120',
                table6: '100',
                table7: '110',
                table8: '150',
                table9: '78'
              }
            },
            two: {
              dialogWidth: '70%',
              form: {
                width1: 'width: 180px;',
                width2: 'width: 180px;',
                width3: 'width: 180px;',
                width4: 'width: 180px;'
              },
              table: {
                height: '650',
                width1: '',
                width2: '80',
                width3: '120',
                width4: '95',
                width5: '100',
                width6: '100',
                width7: '110',
                width8: '110'
              }
            },
            three: {
              dialogWidth: '50%'
            }
          }
        }
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.home {
  width: 100%;
  margin: 0 auto;

  .home-index {
    height: 100%;
    display: flex;
    justify-content: space-between;

    .home-left {
      width: 23%;
      border-right: 1px dashed #a7c8fe;

      .left-top {
        height: 100%;
        background: url("../../../assets/logo/honlivhpOne.png") 35% no-repeat;
      }

      .left-user-title {
        background: #185f7d;
        text-align: center;
        color: #ffffff;
      }

      .left-user {
        background: rgb(#b4bccc, 0.1);

        .user-info {
          display: flex;
          border-bottom: 1px solid #b4bccc;

          .user-img {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            width: 20%;
            height: 100%;
            border-right: 1px solid #b4bccc;
          }

          .user-msg {
            width: 80%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;

            .user-msg-1 {
              height: 33.3%;
              display: flex;
              align-items: center;
              justify-content: space-around;

              span {
                width: 45%;
              }
            }

            .user-msg-2 {
              margin-left: 3%;
            }
          }
        }

        .user-exam {
          display: flex;
          justify-content: space-around;

          span {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
          }
        }
      }

      .left-case {
        .case-table {
          :hover {
            background: #1890ff !important;
            color: #ffffff;
            cursor: pointer;
          }
        }

        .case-table-item {
          display: flex;
          height: 150px;
          border: 1px solid #3f536e;
          flex-direction: column;
          justify-content: space-around;
        }

        ::v-deep.el-scrollbar__wrap {
          overflow: auto;
          height: 100%;
        }

        .my-header-text {
          border-top: 1px solid #b4bccc;
          background-color: #f9f9fa;
          text-align: center;
          letter-spacing: 10px;
          color: #1b2947;
          font-weight: bolder;
        }

        .my-sketch-span {
          font-weight: bolder;
          margin-left: 2%;
        }

        ::v-deep.el-textarea {
          position: relative;
          width: 95%;
          vertical-align: bottom;
          font-weight: 1500;
          font-size: 12px;
          margin-left: 1.5%;
          border: 1px solid #00afff;
          border-radius: 4px;
        }

        ::v-deep.el-textarea.is-disabled .el-textarea__inner {
          background-color: #ffffff;
          border-color: #e4e7ed;
          color: black;
          cursor: not-allowed;
        }
      }
    }

    .home-right {
      width: 77%;
      display: flex;
      flex-direction: column;

      .right-top {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        background: #185f7d;
        color: #ffffff;
        border-radius: 0px 10px 0px 0px;

        span {
          margin-left: 1%;
        }
      }

      .right-time {
        margin-top: 4px;
        width: 100%;
        height: 600px;
        border: 1px solid #f7f8fa;
        background: #f7f8fa;

        .time-table {
          ::v-deep.el-table--medium .el-table__cell {
            padding: 0 !important;
          }

          ::v-deep.el-table .el-table__header-wrapper th,
          .el-table .el-table__fixed-header-wrapper th {
            word-break: break-word;
            background-color: #f8f8f9;
            color: #515a6e;
            height: 30px;
            font-size: 14px;
          }

          ::v-deep.el-table th.el-table__cell > .cell {
            padding: 0;
          }

          ::v-deep.el-table--border .el-table__cell:first-child .cell {
            padding: 0;
          }

          ::v-deep.el-button + .el-button {
            margin-left: 2px;
          }

          ::v-deep.el-table .cell {
            padding: 1px;
          }
        }

        .time-select {
          margin-top: 5px;
          height: 300px;
          background: #ffffff;

          ::v-deep.el-radio-button--medium .el-radio-button__inner {
            font-size: 14px;
            border-radius: 0;
          }

          ::v-deep.el-radio-button__orig-radio:disabled
          + .el-radio-button__inner {
            color: black;
          }

          div {
            margin-top: 0.5%;
          }

          .select-one {
            height: 15%;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .select-one-type {
            }

            .select-one-source {
              width: 40%;

              ::v-deep.el-radio-button__orig-radio:disabled:checked
              + .el-radio-button__inner {
                background-color: #1890ff;
                color: #ffffff;
              }
            }
          }

          .select-two {
            min-height: 15%;
            max-height: 36%;
          }

          .select-two-one {
            ::v-deep.el-radio-button--medium .el-radio-button__inner {
              font-size: 18px !important;
              border-radius: 0;
              font-weight: 600;
            }
          }

          .select-three {
            height: 15%;
          }

          .select-four {
            height: 50%;
            display: flex;

            :hover {
              cursor: pointer;
              color: #ffffff;
              box-shadow: 0 0 0 0 grey;
              transform: scale(1.1);
              background: #1890ff !important;
            }

            .four-item {
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              align-items: center;
              text-align: center;
              width: 10%;
              height: 80%;
              background: #edf4fc;
              margin: 1%;

              .item-son {
                transform: scale(1) !important;
              }
            }
          }

          .select-five {
            display: flex;
            justify-content: space-between;
            margin-top: 0.5%;
          }
        }
      }

      .right-task {
        height: 30%;
        margin-top: 10px;
        padding: 5px 30px;

        .task-list {
          height: 100%;
          border: 1px solid #b4bccc;

          .task-border {
            border: 1px solid #b4bccc;
            width: 13%;
            display: flex;
            justify-content: space-around;
          }

          .task-one {
            border: 1px solid #b4bccc;
            width: 15%;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 5%;
            background: #8caae7;
            float: left;
            margin-left: 1%;
            border-radius: 20px;
          }

          .task-two {
            background: #8caae7;
            margin-top: 6%;
            padding: 10px 20px;
            border-radius: 20px;
            float: left;
          }

          .task-three {
            background: red;
            margin-top: 6%;
            padding: 10px 20px;
            border-radius: 20px;
            float: left;
          }

          .task-four {
            background: #f8f8f9;
            margin-top: 6%;
            padding: 10px 20px;
            border-radius: 20px;
            float: left;
          }

          .task-five {
            background: #f8f8f9;
            margin-top: 1%;
            padding: 10px 20px;
            border-radius: 20px;
            float: right;
            margin-right: 6%;
          }

          .task-six {
            background: #f8f8f9;
            margin-top: 1%;
            padding: 10px 20px;
            border-radius: 20px;
            float: right;
          }

          .task-bottom {
            margin-top: 0.1%;
            float: left;
            width: 100%;
            display: flex;
            justify-content: space-evenly;

            .task-bottom-s {
              display: flex;

              .bottom-text {
                display: flex;
                align-items: center;
              }

              .task-bottom-on {
                display: inline-block;
                height: 25px;
                width: 60px;
                border: 1px solid #b4bccc;
                border-radius: 7%;
                /* margin-left: 29px; */
                margin-right: 4px;
                position: relative;
              }
            }
          }
        }
      }
    }

    .home-drawer {
      ::v-deep.el-drawer__header {
        -webkit-box-align: center;
        -ms-flex-align: center;
        text-align: center;
        color: #ffffff;
        display: -webkit-box;
        display: -ms-flexbox;
        font-size: 20px;
        display: flex;
        margin-bottom: 0;
        padding: 10px;
        background: #185f7d;
        align-items: center;
        height: 53px;
      }

      ::v-deep.el-radio__label {
        font-size: 20px;
        padding-left: 10px;
      }
    }

    .home-dialog {
      .dialog-two {

        ::v-deep.el-radio__label {
          font-size: 18px;
          padding-left: 10px;
        }

        .dialog-patient {
          .patient-fiex {
            display: flex;
            justify-content: center;
            margin-bottom: 10px;
          }
        }
      }

      .dialog-manual-input {
        ::v-deep.el-checkbox-button.is-disabled .el-checkbox-button__inner {
          color: black;
        }

        ::v-deep.el-radio-button__orig-radio:disabled
        + .el-radio-button__inner {
          color: black;
        }

        ::v-deep.el-radio-button__orig-radio:checked + .el-radio-button__inner {
          color: #ffffff;
          background-color: #1890ff;
          border-color: #1890ff;
          -webkit-box-shadow: -1px 0 0 0 #1890ff;
          box-shadow: -1px 0 0 0 #1890ff;
        }

        ::v-deep.el-descriptions--medium.is-bordered
        .el-descriptions-item__cell {
          padding: 10px;
          width: 1%;
        }
      }

      .time-selectTwo {
        margin-top: 25px;

        ::v-deep.el-radio-button__orig-radio:disabled
        + .el-radio-button__inner {
          color: black;
        }

        ::v-deep.el-radio-button__orig-radio:checked + .el-radio-button__inner {
          color: #ffffff;
          background-color: #1890ff;
          border-color: #1890ff;
          -webkit-box-shadow: -1px 0 0 0 #1890ff;
          box-shadow: -1px 0 0 0 #1890ff;
        }
      }

      .beHospitalized {
        .beHospitalizedOne {
          max-height: 150px;
          min-height: 50px;
          border: 1px solid #e0f5ec;
          margin-top: 5px;
          margin-bottom: 10px;

          .beHospitalizedGroup {
            display: flex;
            justify-content: space-evenly;
            flex-wrap: wrap;
            padding: 10px;

            ::v-deep.el-checkbox__label {
              display: inline-block;
              padding-left: 10px;
              line-height: 19px;
              font-size: 16px;
            }
          }
        }

        .beHospitalizedTitle {
          margin-top: 10px;
          display: flex;
          justify-content: center;
          font-size: 24px;
          font-weight: 800;
        }

        .beHospitalizedTop {
          margin-top: 10px;
        }

        .beHospitalizedBottom {
          margin-bottom: 10px;
        }

        .beHospitalizedInput {
          display: flex;
          justify-content: center;
          margin-top: 10px;
          margin-left: 10%;
        }

        .beHospitalizedButton {
          display: flex;
          justify-content: center;
          margin-top: 35px;
        }

        .beHospitalizedMessage {
          display: flex;
          font-size: 16px;
          margin-top: 20px;
          justify-content: space-evenly;

          .beHospitalizedMessageItem {
            display: flex;

            .beHospitalizedMessageItemSon {
              text-decoration: underline;
              text-underline-offset: 3px;
            }
          }
        }
      }
    }
  }
}

.image1 {
  float: left;
  width: 10%;
  height: 10%;
  margin: 6% 20px;
  background: url("../../../assets/images/rightImage.png") 50% no-repeat;
}

.image2 {
  float: left;
  width: 10%;
  height: 19%;
  margin: 1% 20px;
  background: url("../../../assets/images/belowImage.png") 50% no-repeat;
}

.image3 {
  float: right;
  width: 10%;
  height: 10%;
  margin: 1% 20px;
  background: url("../../../assets/images/leftImage.png") 50% no-repeat;
}

.one {
  cursor: pointer;
  color: #ffffff;
  box-shadow: 0 0 0 0 grey;
  transform: scale(1.1);
  background: #1890ff !important;
  z-index: 1;
}

.four {
  color: #ffffff;
  background: red !important;
  z-index: 99;
}

.five {
  color: #ffffff;
  background: #20b2aa !important;
  z-index: 2;
}

.two {
  ::v-deep.el-radio-button--medium .el-radio-button__inner {
    padding: 10px 13px !important;
  }
}

.myAlert {
  width: 30%;
}

.myAlertMaster {
  display: flex;
}

.two-top {
  margin-top: 0.5%;
}

.tableZero {
  background: #ffffff !important;
  color: black;
}

.tableOne {
  background: #1890ff !important;
  color: #ffffff;
}

.tableTwo {
  color: #ffffff;
  background: #20b2aa !important;
}

.tableThree {
  color: #ffffff;
  background: red !important;
}

.time-table-img {
  z-index: 1;

  :hover {
    z-index: 1;
    cursor: pointer;
    transform: scale(1.6);
  }
}
</style>
