<template>
    <div class="single-master">
        <div class="single-title">退款银行登记列表</div>
        <div class="single-element">
            <div class="element-master">
                <div class="element-form">
                    <el-form :inline="true" :model="queueForm" class="demo-form-inline">
                        <el-form-item label="时间:">
                            <el-date-picker v-model="queueForm.settlementTime" type="date" value-format="yyyy-MM-dd"
                                format="yyyy-MM-dd" placeholder="选择日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="收费类型:">
                            <el-select v-model="queueForm.chargeType" placeholder="请选择">
                                <el-option v-for="item in options" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item>
                            <el-radio-group v-model="queueForm.radio" @change="agreeChange">
                                <el-radio :label="1">行内</el-radio>
                                <el-radio :label="2">跨行</el-radio>
                                <el-radio :label="3">其它</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <div class="element-button">
                                <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
                                <el-button type="primary" icon="el-icon-takeaway-box" v-print="print">打印</el-button>
                                <export-excel excelName="退款数据导出" :excelData="tableDate || []" :columnMap="columnMap"
                                    v-if="this.queueForm.radio !== 2">
                                    <el-button type="primary" icon="el-icon-folder-opened" slot="trigger"
                                        style="margin-left: 10%;">导出</el-button>
                                </export-excel>
                                <export-excel excelName="退款数据导出" :excelData="tableDate || []" :columnMap="columnMapX"
                                    v-if="this.queueForm.radio === 2">
                                    <el-button type="primary" icon="el-icon-folder-opened" slot="trigger"
                                        style="margin-left: 10%;">导出</el-button>
                                </export-excel>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="element-table" id="printAll">
                    <table border="1px" width="100%" cellspacing="0px" cellpadding="6px" align="center">
                        <tr>
                            <td>
                                <span v-if="this.queueForm.radio === 1">批量行内转账总笔数：</span>
                                <span v-if="this.queueForm.radio === 2">批量跨行转账总笔数：</span>
                                <span v-if="this.queueForm.radio === 3">批量其它转账总笔数：</span>
                            </td>
                            <td>{{ this.countAll }}</td>
                            <td colspan="2">笔</td>
                        </tr>
                        <tr>
                            <td>
                                <span v-if="this.queueForm.radio === 1">批量行内转账总金额：</span>
                                <span v-if="this.queueForm.radio === 2">批量跨行转账总笔数：</span>
                                <span v-if="this.queueForm.radio === 3">批量其它转账总笔数：</span>
                            </td>
                            <td>{{ this.sumAll }}</td>
                            <td>元</td>
                            <td>{{ this.queueForm.settlementTime }}</td>
                        </tr>
                    </table>
                    <table border="1px" width="100%" cellspacing="0px" cellpadding="6px" align="center">
                        <tr>
                            <td>
                                序号
                            </td>
                            <td>
                                收款户名
                            </td>
                            <td>
                                收款账号
                            </td>
                            <td>
                                收款账号类型
                            </td>
                            <td>
                                交易金额(元)
                            </td>
                            <td v-if="queueForm.radio === 2">
                                收款行开户行号
                            </td>
                            <td v-if="queueForm.radio === 2">
                                收款银行名称
                            </td>
                            <td>
                                备注
                            </td>
                            <td>
                                收费类型
                            </td>
                        </tr>
                        <tr v-for="(item, index) in tableDate" :key="index">
                            <td>
                                {{ index + 1 }}
                            </td>
                            <td>
                                {{ item.cardholdeR_NAME }}
                            </td>
                            <td>
                                {{ item.banK_CARD_NO }}
                            </td>
                            <td>
                                {{ item.bank }}
                            </td>
                            <td>
                                {{ item.remittancE_AMOUNT }}
                            </td>
                            <td v-if="queueForm.radio === 2">
                                {{ item.banK_BRANCH }}
                            </td>
                            <td v-if="queueForm.radio === 2">
                                {{ item.datA_NAME }}
                            </td>
                            <td>
                                {{ item.note }}
                            </td>
                            <td>
                                {{ item.chargE_TYPE }}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    GetRefundBankHang,
    GetRefundBankKua,
    GetRefundBankQt
} from "@/api/singlePage/refundBank";
import ExportExcel from "@/components/excel/exportExcel";
export default {
    name: 'parasiteIndex',
    props: [],
    components: {
        ExportExcel
    },
    data() {
        return {
            queueForm: {
                pageNum: 1,
                pageSize: 10,
                radio: 1,
                chargeType: "医保收费",
                settlementTime: this.formatDate(new Date()),
            },
            size: '',
            tableDate: [],
            countAll: {},
            sumAll: {},
            columnMap: [
                {
                    label: "收款户名",
                    key: "cardholdeR_NAME",
                },
                {
                    label: "收款账号",
                    key: "banK_CARD_NO",
                },
                {
                    label: "收款账号类型",
                    key: "bank",
                },
                {
                    label: "交易金额(元)",
                    key: "remittancE_AMOUNT",
                },
                {
                    label: "备注",
                    key: "note",
                }
            ],
            columnMapX: [
                {
                    label: "收款户名",
                    key: "cardholdeR_NAME",
                },
                {
                    label: "收款账号",
                    key: "banK_CARD_NO",
                },
                {
                    label: "收款账号类型",
                    key: "bank",
                },
                {
                    label: "交易金额(元)",
                    key: "remittancE_AMOUNT",
                },
                {
                    label: "收款行开户行号",
                    key: "banK_BRANCH",
                },
                {
                    label: "收款银行名称",
                    key: "datA_NAME",
                },
                {
                    label: "备注",
                    key: "note",
                }
            ],
            print: {
                id: 'printAll',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {
                }, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {
                }, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeEntryIframe() {
                    const cells = document.querySelectorAll('.cell');
                    [].slice.call(cells).forEach((item) => {
                        // 为了让表格中的内容自动换行，不需要的话可以删掉
                        item.style.whiteSpace = 'pre-wrap'
                    })
                },
                openCallback() {
                }, // 调用打印之后的回调事件
                closeCallback() {
                }, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: ''
            },
            options: [{
                value: '医保收费',
                label: '医保收费'
            }, {
                value: '自费',
                label: '自费'
            }],
        }
    },

    created() {
        this.getList();
        this.handleResize();
    },

    mounted() {
        window.addEventListener('resize', this.handleResize); // 添加监听器
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize); // 移除监听器
    },

    methods: {
        // 初始化数据
        getList() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            if (this.queueForm.radio === 1) {
                GetRefundBankHang(this.queueForm).then(res => {
                    this.tableDate = res.data.list;
                    console.log("1", this.tableDate);
                    this.countAll = res.data.count[0].count;
                    this.sumAll = res.data.sum[0].sum;
                }).finally(() => {
                    loading.close();
                });
            } else {
                if (this.queueForm.radio === 2) {
                    GetRefundBankKua(this.queueForm).then(res => {
                        this.tableDate = res.data.list;
                        console.log("2", this.tableDate);
                        this.countAll = res.data.count[0].count;
                        this.sumAll = res.data.sum[0].sum;
                    }).finally(() => {
                        loading.close();
                    });
                } else {
                    if (this.queueForm.radio === 3) {
                        GetRefundBankQt(this.queueForm).then(res => {
                            this.tableDate = res.data.list;
                            console.log("3", this.tableDate);
                            this.countAll = res.data.count[0].count;
                            this.sumAll = res.data.sum[0].sum;
                        }).finally(() => {
                            loading.close();
                        });
                    }
                }
            }
        },

        // el-radio-group点击变化参数
        agreeChange(data) {
            this.queueForm.radio = data;
            this.getList();
        },

        // 默认当前时间
        formatDate(date) {
            const year = date.getFullYear()
            const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
            const day = date.getDate().toString().padStart(2, '0')
            return `${year}-${month}-${day}`
        },

        // 自定义高度变化更新高度
        handleResize() {
            this.tableHeight = window.innerHeight; // 更新高度数据
        },
    }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
</style>