const env = {
  api_url: 'http://**************:8009/api/',
  rabbit_url: 'ws://**********:15674/ws',
  mappings: [
    {
      ip: 'localhost',
      // url: 'http://***********:10018',
      url: 'http://localhost:10500/',
    }, {
      ip: "***********",
      url: "http://localhost:10500/",
    },
    {
      ip: "**************",
      url: "http://localhost:10500/",
    },
    {
      ip: '*********',
      url: 'http://**********:10500/',
    },
    {
      ip: '************',
      url: 'http://************:10500/',
    },
    {
      ip: "**********",
      url: "http://**********:10500/",
    },
    {
      ip: "************",
      url: "http://************:10500/",
    },
    {
      ip: "**********",
      url: "http://**********:10500/",
    },
    {
      ip: "************",
      url: "http://************:10500/",
    },
    {
      ip: "*********",
      url: "http://**********:10500/",
    },
    {
      ip: "************",
      url: "http://************:10500/",
    },
    {
      ip: '*********',
      url: 'http://**********:10500/',
    },
    {
      ip: '************9',
      url: 'http://************:10500/',
    },
    {
      ip: 'http://mobile.honlivhp.com',
      url: 'http://**************:8009/api/',
    },
  ],
  baseMappings: [
    {
      ip: 'localhost',
      url: 'http://localhost:8010/',
    },{
      ip: "***********",
      url: "http://***********:10500/",
    },
    {
      ip: "**************",
      url: "http://**************:10001/",
    },
    {
      ip: '*********',
      url: 'http://*********:10001/',
    },
    {
      ip: '************',
      url: 'http://************:10001/',
    }, {
      ip: '**********',
      url: 'http://**********:10001/',
    },
    {
      ip: '************',
      url: 'http://************:10001/',
    }, {
      ip: '**********',
      url: 'http://**********:10001/',
    },
    {
      ip: '************',
      url: 'http://************:10001/',
    }, {
      ip: "*********",
      url: "http://*********:10001/",
    },
    {
      ip: "************",
      url: "http://************:10001/",
    },
    {
      ip: '*********',
      url: 'http://*********:8009/',
    },
    {
      ip: '************9',
      url: 'http://************9:8009/',
    },
    {
      ip: 'http://mobile.honlivhp.com',
      url: 'http://**************:8009/',
    },
  ],
  rabbitMqUrls: [
    {
      ip: 'localhost',
      url: 'ws://**********:15674/ws',
    },
    {
      ip: '*********',
      url: 'ws://**********:15674/ws',
    },
    {
      ip: '************',
      url: 'ws://*************:15674/ws',
    },
  ],
  get_api_url() {
    let ip = window.location.hostname
    let mapping = this.mappings.find((t) => {
      return t.ip === ip
    })

    if (mapping !== undefined) {
      return mapping.url
    }
    return this.api_url
  },
  get_rabbit_url() {
    let ip = window.location.hostname
    let mapurl = this.rabbitMqUrls.find((t) => {
      return t.ip === ip
    })
    if (mapurl !== undefined) {
      return mapurl.url
    }
    return this.rabbit_url
  },
  get_base_url() {
    let ip = window.location.hostname
    let mapping = this.baseMappings.find((t) => {
      return t.ip === ip
    })

    if (mapping !== undefined) {
      return mapping.url
    }
    return mapping.url
  },
}
export default env
