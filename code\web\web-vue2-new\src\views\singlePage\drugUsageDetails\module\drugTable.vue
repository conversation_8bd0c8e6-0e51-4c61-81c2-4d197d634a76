<template>
  <div class="element-table" >
    <el-tabs v-model="queueForm.drugType" type="card" @tab-click="handleClick">
      <el-tab-pane label="住院明细" name="1">
        <el-table :data="tableData" style="width: 100%" border  show-summary :height="(tableHeight-230)" highlight-current-row
        >
          <el-table-column type="index" width="40" align="center"></el-table-column>
          <el-table-column prop="ITEM_CODE" align="center" label="药品代码" ></el-table-column>
          <el-table-column prop="ITEM_NAME" align="center" label="药品名称"></el-table-column>
          <el-table-column prop="ITEM_SPEC" align="center" label="规格"></el-table-column>
          <el-table-column prop="DEPT_NAME" align="center" label="科室" ></el-table-column>
          <el-table-column prop="ORDER_DOCTOR" align="center" label="医生" ></el-table-column>
          <el-table-column prop="AMOUNT" align="center" sortable label="数量" ></el-table-column>
          <el-table-column prop="UNITS" align="center" label="单位" ></el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="门诊明细" name="2">
        <el-table :data="tableData" style="width: 100%" border  :show-summary="true" :height="(tableHeight-230)" highlight-current-row
        >
          <el-table-column type="index" width="40" align="center"></el-table-column>
          <el-table-column prop="ITEM_CODE" align="center" label="药品代码" ></el-table-column>
          <el-table-column prop="ITEM_NAME" align="center" label="药品名称"></el-table-column>
          <el-table-column prop="ITEM_SPEC" align="center" label="规格"></el-table-column>
          <el-table-column prop="DEPT_NAME" align="center" label="科室" ></el-table-column>
          <el-table-column prop="ORDER_DOCTOR" align="center" label="医生" ></el-table-column>
          <el-table-column prop="AMOUNT" align="center" label="数量" ></el-table-column>
          <el-table-column prop="UNITS" align="center" label="单位" ></el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
/**
 * 按药品查询
 */
import { getDrugUsageDetailsTable,exportDrugUsageDetailsTable } from '@/api/singlePage/drugUsageDetails'
import { excelDownloadXLSX } from "@/utils/BlobUtils"
export default {
  name: 'drugTable',
  props: ['tableHeight','queueForm'],
  components: {},
  data() {
    return {
      tableData: [],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    handleClick(){
      this.getTableDate();
    },
    getTableDate(){
      const loading = this.$loading({
        lock: true,
        text: "请稍后,数据正在努力获取中...(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.tableData = [];
      getDrugUsageDetailsTable(this.queueForm).then(res => {
        this.tableData = res.data;
      }).finally(() => {
        loading.close();
      })
    },
    exportTableData(){
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力导出中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      exportDrugUsageDetailsTable(this.queueForm).then(res => {
        let fileName = "药品使用明细("
        if (this.queueForm.drugType === '1'){
          fileName += "住院)";
        }else if (this.queueForm.drugType === '2'){
          fileName += "门诊)";
        }
        excelDownloadXLSX(res, fileName)
      }).finally(() => {
        loading.close();
      })
    },
  }
}
</script>

<style scoped lang="scss">

</style>
