import request from '@/utils/request2'

export function getDrugStatementTable(data) {
  return request({
    url: '/singlePage/drugStatement/inquire',
    method: 'post',
    data: data
  })
}

export function getDrugStatementItemDict() {
  return request({
    url: '/singlePage/drugStatement/item',
    method: 'post',
  })
}

export function exportDrugStatementItemDict(data) {
  return request({
    url: '/singlePage/drugStatement/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function getUserInfoByEmpNo(empNo) {
  return request({
    url: '/singlePage/userInfo/' + empNo,
    method: 'get',
  })
}
