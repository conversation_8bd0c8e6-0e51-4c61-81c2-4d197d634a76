<template>
  <div class="element-table">
    <el-table v-loading="loading" :data="tableDate" style="width: 100%" border :height="tableHeight">
      <el-table-column type="index" width="50" align="center"></el-table-column>
      <el-table-column prop="DOCUMENT_NO" align="center" label="入库单号" width="100"></el-table-column>
      <el-table-column prop="DRUG_CODE" align="center" label="药品代码" width="135"></el-table-column>
      <el-table-column prop="DRUG_NAME" align="center" label="药品名称" width="190"></el-table-column>
      <el-table-column prop="DRUG_SPEC" align="center" label="规格" width="70"></el-table-column>
      <el-table-column prop="PACKAGE_SPEC" align="center" label="包装规格" width="75"></el-table-column>
      <el-table-column prop="PACKAGE_UNITS" align="center" label="包装单位" width="75"></el-table-column>
      <el-table-column prop="UNITS" align="center" label="单位" width="50"></el-table-column>
      <el-table-column prop="PRICE" align="center" label="价格" width="70"></el-table-column>
      <el-table-column prop="PURCHASE_PRICE" align="center" label="进价" width="70"></el-table-column>
      <el-table-column prop="QUANTITY" align="center" label="数量" width="70"></el-table-column>
      <el-table-column prop="SUPPLIER" align="center" label="供货商" width="180"></el-table-column>
      <el-table-column prop="DISCOUNT" align="center" label="折扣" width="70"></el-table-column>
      <el-table-column prop="RETAIL_PRICE" align="center" label="零售价" width="70"></el-table-column>
      <el-table-column prop="FIRM_ID" align="center" label="厂家标识" width="110"></el-table-column>
      <el-table-column prop="IMPORT_DATE" align="center" label="入库日期" width="120"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import {getDrugStatementTable} from '@/api/singlePage/drugStatement'
/**
 * 药品历史价格查询
 */
export default {
  name: 'drugHistory',
  props: ['tableHeight','queryForm'],
  components: {},
  data() {
    return {
      tableDate: [],
      loading: false,
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    getTable(){
      this.loading = true;
      getDrugStatementTable(this.queryForm).then(res => {
        if (res.code === 200){
          this.tableDate = res.data;
        }
      }).finally(() => {
        this.loading = false;
      })
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../../assets/styles/singlePage";
</style>
