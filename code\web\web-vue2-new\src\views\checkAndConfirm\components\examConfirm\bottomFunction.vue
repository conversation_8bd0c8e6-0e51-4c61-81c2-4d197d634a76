<template>
  <div :class="{ confirmBottomHomeOne: bottomOne, confirmBottomHomeTwo: bottomTwo, confirmBottomHomeThree: bottomThree }">
    <div class="bottom-master">
      <div class="style-master">
        <div class="top-master">
          <div class="left-form">
            <form-select ref="formSelectRef" @send-table="tableMonitor"></form-select>
          </div>
          <div class="right-button">
            <el-button @click="readCardClick">读卡</el-button>
            <el-button @click="scanQrCodesClick">扫码</el-button>
            <el-button @click="extractSelectClick">提取</el-button>
            <el-button @click="clsClick">清屏</el-button>
            <el-button @click="liveReportClick">报到</el-button>
            <el-button @click="viewClick">360视图</el-button>
          </div>
        </div>
        <div class="cost-master"><particulars-cost ref="costRef"></particulars-cost></div>
      </div>

      <div class="bottom-center">
        <div class="bottom-style">
          <div class="bottom-text">已授权科室</div>
          <div class="bottom-select">
            <medical-dept-tree></medical-dept-tree>
          </div>
        </div>
      </div>

      <div class="bottom-right">
        <div class="bottom-item">
          <triage-particulars></triage-particulars>
        </div>
      </div>
    </div>
    <div>
      <read-card ref="readCardRef" @card-no="patientIdMonitor"></read-card>
      <scan-qr-codes ref="scanQrCodesRef" @send-patient="patientIdMonitor"></scan-qr-codes>
      <live-report ref="liveReportRef"></live-report>
    </div>
  </div>
</template>

<script>
/**
 * 底部功能列表
 */
import formSelect from './buttomFunctionRealization/formSelect.vue'
import ReadCard from './buttomFunctionRealization/readCard.vue'
import ScanQrCodes from './buttomFunctionRealization/scanQrCodes.vue'
import LiveReport from './buttomFunctionRealization/liveReport.vue'
import ParticularsCost from './buttomFunctionRealization/particularsCost.vue'
import MedicalDeptTree from './buttomFunctionRealization/medicalDeptTree.vue'
import TriageParticulars from './buttomFunctionRealization/triageParticulars.vue'

export default {
  name: 'bottomFunction',
  props: [],
  components: { TriageParticulars, MedicalDeptTree, ParticularsCost, LiveReport, ScanQrCodes, ReadCard, formSelect },
  data() {
    return {
      bottomOne: true,
      bottomTwo: false,
      bottomThree: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      const docEl = document.documentElement,
        docBody = document.body,
        winWidth = docEl.clientWidth || docBody.clientWidth // 获取浏览器的宽度
      if (winWidth <= 1200) {
        this.bottomOne = false
        this.bottomTwo = false
        this.bottomThree = true
      } else if (winWidth <= 1400) {
        this.bottomOne = false
        this.bottomTwo = true
        this.bottomThree = false
      } else {
        this.bottomOne = true
        this.bottomTwo = false
        this.bottomThree = false
      }
    });
    window.addEventListener('keydown', this.handleKeydown);
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleKeydown);
  },
  methods: {
    handleKeydown(event) {
      // 检查是否按下了Alt+Z
      if (event.altKey && event.key === 'z') {
        this.readCardClick();
        // 阻止默认行为和事件冒泡
        event.preventDefault();
        event.stopPropagation();
      } else if (event.altKey && event.key === 'a') {
        this.extractSelectClick();
        // 阻止默认行为和事件冒泡
        event.preventDefault();
        event.stopPropagation();
      } else if (event.altKey && event.key === 'q') {
        this.scanQrCodesClick();
        // 阻止默认行为和事件冒泡
        event.preventDefault();
        event.stopPropagation();
      }
    },
    tableMonitor(data) {
      this.$emit('send-table', data)
    },
    viewClick() {
      window.open('http://192.168.10.49:9001/360patient?patientId=' + this.$store.getters.patient.patientId, '_blank')
    },
    patientIdMonitor(data) {
      this.$refs.formSelectRef.patientIdCapture(data)
      this.$refs.costRef.getMoney(data);
    },
    readCardClick() {
      this.$refs.readCardRef.read()
    },
    scanQrCodesClick() {
      this.$refs.scanQrCodesRef.codeScanner(true)
    },
    extractSelectClick() {
      this.$emit('send-table', this.$store.getters.patient);
    },
    clsClick() {
      this.$refs.formSelectRef.center()
    },
    liveReportClick() {
      this.$refs.liveReportRef.report(this.$store.getters.rowTable);
    }
  }
}
</script>

<style scoped lang="scss">
.confirmBottomHomeTwo {

  .bottom-master {
    margin-left: 3%;
    display: flex;
    justify-content: space-around;

    .style-master {
      display: flex;
      flex-direction: column;
      padding: 5px;
      width: 40%;

      .top-master {
        display: flex;

        .left-form {
          width: 58%;
        }

        .right-button {
          width: 45%;
          display: flex;
          -webkit-box-orient: horizontal;
          -webkit-box-direction: normal;
          -ms-flex-direction: row;
          flex-direction: row;
          -ms-flex-wrap: wrap;
          flex-wrap: wrap;
          justify-content: flex-start;
          align-items: center;

          ::v-deep.el-button--medium {
            padding: 8px 20px;
            font-size: 14px;
            margin-left: 10px;
            border-radius: 4px;
          }
        }
      }

      .cost-master {}
    }

    .bottom-center {
      width: 18%;
      display: flex;
      align-items: center;

      .bottom-style {
        width: 100%;
        margin-top: -30px;

        .bottom-text {
          display: flex;
          justify-content: center;
        }

        .bottom-select {
          display: flex;
          justify-content: center;
        }
      }
    }

    .bottom-right {
      width: 30%;
      display: flex;
      align-content: center;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;

      .bottom-item {
        border: 1px solid #000000;
        padding: 10px;
      }
    }
  }

}

.confirmBottomHomeThree {

  .bottom-master {
    margin-left: 2%;
    display: flex;
    justify-content: space-around;

    .style-master {
      display: flex;
      flex-direction: column;
      padding: 5px;
      width: 50%;

      .top-master {
        display: flex;

        .left-form {
          width: 60%;
        }

        .right-button {
          width: 40%;
          display: flex;
          -webkit-box-orient: horizontal;
          -webkit-box-direction: normal;
          -ms-flex-direction: row;
          flex-direction: row;
          -ms-flex-wrap: wrap;
          flex-wrap: wrap;
          justify-content: flex-start;
          align-items: center;

          ::v-deep.el-button--medium {
            padding: 8px 15px;
            font-size: 14px;
            margin-left: 10px;
            border-radius: 4px;
          }
        }
      }
    }

    .bottom-center {
      width: 20%;
      display: flex;
      align-items: center;

      .bottom-style {
        width: 100%;
        margin-top: -30px;

        .bottom-text {
          display: flex;
          justify-content: center;
        }

        .bottom-select {
          display: flex;
          justify-content: center;
        }
      }
    }

    .bottom-right {
      width: 30%;
      display: flex;
      align-content: center;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;

      .bottom-item {
        border: 1px solid #000000;
        padding: 10px;
      }
    }
  }

}

.confirmBottomHomeOne {

  .bottom-master {
    margin-left: 3%;
    display: flex;
    justify-content: space-around;

    .style-master {
      display: flex;
      flex-direction: column;
      padding: 5px;
      width: 30%;

      .top-master {
        display: flex;

        .left-form {
          width: 60%;
        }

        .right-button {
          width: 40%;
          display: flex;
          -webkit-box-orient: horizontal;
          -webkit-box-direction: normal;
          -ms-flex-direction: row;
          flex-direction: row;
          -ms-flex-wrap: wrap;
          flex-wrap: wrap;
          justify-content: flex-start;
          align-items: center;

          ::v-deep.el-button--medium {
            padding: 8px 20px;
            font-size: 14px;
            margin-left: 10px;
            border-radius: 4px;
          }
        }
      }

      .cost-master {}
    }

    .bottom-center {
      width: 15%;
      display: flex;
      align-items: center;

      .bottom-style {
        width: 100%;
        margin-top: -30px;

        .bottom-text {
          display: flex;
          justify-content: center;
        }

        .bottom-select {
          display: flex;
          justify-content: center;
        }
      }
    }

    .bottom-right {
      width: 30%;
      display: flex;
      align-content: center;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;

      .bottom-item {
        border: 1px solid #000000;
        padding: 8px 30px;
      }
    }
  }

}
</style>
