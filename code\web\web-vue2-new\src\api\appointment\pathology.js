import request from "@/utils/request";


/**
 * 根据不同检查类别获取信息
 * @param {*} query 
 * @returns 
 */
export function GetExamClassSelectInfo(query){
  return request({
    url: "/pathology/GetExamClassSelectInfo",
    method: "get",
    params: query,
  });
}

/**
 * 病理检查申请树形结构
 * @param {*} query 
 * @returns 
 */
export function GetPatologyExamTree(query){
  return request({
    url: "/pathology/GetPatologyExamTree",
    method: "get",
    params: query,
  });
}

/**
 * 获取住院病理初始化信息
 * @param {*} query 
 * @returns 
 */
export function GetPatologyInitInfo(query){
  return request({
    url: "/pathology/GetPatologyInitInfo",
    method: "get",
    params: query,
  });
}


/**
 * 获取检查项目初始化信息
 * @param {*} query 
 * @returns 
 */
export function GetExamItemInfo(query){
  return request({
    url: "/pathology/GetExamItemInfo",
    method: "get",
    params: query,
  });
}



//保存病理住院检查申请信息
export function saveExamPathologyRegister(data) {
    return request({
      url: "/pathology/saveExamPathologyRegister",
      method: "post",
      data: data,
    });
  }

  //保存门诊患者病理住院检查申请信息
export function saveOutpExamPathologyRegister(data) {
  return request({
    url: "/pathology/saveOutpPathologyApplyRegister",
    method: "post",
    data: data,
  });
}