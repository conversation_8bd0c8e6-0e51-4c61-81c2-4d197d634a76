import request from '@/utils/request'

// 寄生虫查询->首页数据查询
export function GetParasiteList(data) {
    return request({
        url: '/Parasite/GetParasiteList',
        method: 'get',
        params: data
    })
}

// 寄生虫查询(根据检验号查询)
export function GetParasiteByTestNo(data) {
    return request({
        url: '/Parasite/GetParasiteByTestNo',
        method: 'get',
        params: data
    })
}

// 寄生虫查询(根据医生姓名查询)
export function GetParasiteByDocName(data) {
    return request({
        url: '/Parasite/GetParasiteByDocName',
        method: 'get',
        params: data
    })
}