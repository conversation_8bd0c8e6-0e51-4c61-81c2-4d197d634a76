<template>
  <div>
    <el-row>
      <el-col :span="23" :offset="1">
        <br />
        <el-row>
          <el-col :span="4" :offset="1">
            <el-button size="small" plain type="success" @click="toAdd">
              新增
            </el-button>
          </el-col>
        </el-row>

        <el-table
          :data="frameInfo"
          style="width: 100%; font-size: 15px;"
          stripe
          height="800"
          :header-cell-style="{ 'text-align': 'center', height: '64px' }"
          :cell-style="{ 'text-align': 'center' }"
        >
          <el-table-column
            :show-overflow-tooltip="true"
            label="序号"
            align="center"
            min-width="20"
          >
            <template slot-scope="scop">
              {{ scop.$index + 1 }}
            </template>
          </el-table-column>

          <el-table-column
            :show-overflow-tooltip="true"
            prop="timE_FRAME"
            label="时段"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            prop="season"
            label="排序"
          ></el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            prop="halF_DATE"
            label="备注"
          ></el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                size="small"
                plain
                type="warning"
                @click="toUpdown(scope.row)"
              >
                修改
              </el-button>
              <el-button
                size="small"
                plain
                type="danger"
                @click="toDelete(scope.row)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <!-- 添加或者修改 -->
    <el-dialog
      :visible.sync="addDetailDialog"
      width="800"
      append-to-body
      size="50%"
    >
      <el-row>
        <el-form :model="detailModel" label-width="85px">
          <el-row>
            <el-col :span="1" :offset="1">
              <el-tag size="medium">备注：</el-tag>
            </el-col>
            <el-col :span="4" :offset="1">
              <el-input v-model="detailModel.HALF_DATE"></el-input>
            </el-col>
            <el-col :span="4" :offset="1">
              <el-time-select
                v-model="detailModel.startTime"
                :picker-options="{
                  start: '00:00',
                  step: '00:30',
                  end: '24:00',
                }"
                placeholder="开始时间"
              ></el-time-select>
            </el-col>
            <el-col :span="4" :offset="1">
              <el-time-select
                placeholder="结束时间"
                v-model="detailModel.endTime"
                :picker-options="{
                  start: '00:00',
                  step: '00:30',
                  end: '24:00',
                  minTime: detailModel.startTime,
                }"
              ></el-time-select>
            </el-col>
          </el-row>
        </el-form>
      </el-row>
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <el-row>
        <el-col :offset="1">
          <el-button plain @click="setDetail" type="primary" size="small">
            确认
          </el-button>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import { GetFrameInfo, DeleFrameInfo, SetFrame } from '@/api/appointment/frame'
export default {
  data() {
    return {
      time_frame: [],
      detailModel: {
        id: '',
        SEASON: '',
        HALF_DATE: '',
        DEL_FLAG: '',
        TIME_FRAME: '',
        startTime: '',
        endTime: '',
      },
      addDetailDialog: false,
      class_name: undefined,
      subclass_name: undefined,

      treeSearch: '',
      begetterExamName: '',
      sonExamName: '',
      defaultProps: {
        children: 'children',
        label: 'label',
      },

      examTree: [],
      frameList: [],
      frameInfo: [],
    }
  },

  computed: {
    scrollerHeight: function () {
      return window.innerHeight - 200 + 'px'
    },
  },

  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
  },

  created() {
    this.dept_code = this.$route.query.dept_code
    this.EmpNo = this.$route.query.EmpNo
    this.gainFrameInfo()
  },
  mounted() {},
  methods: {
    gainFrameInfo() {
      let param = {}
      GetFrameInfo(param).then((res) => {
        if (res.code == 200) {
          this.frameInfo = res.data
          this.$message({
            showClose: true,
            message: res.message,
            type: 'success',
          })
        }
      })
    },

    toDelete(row) {
      let param = {
        detail_id: row.detail_id,
      }
      DeleFrameInfo(param).then((res) => {
        if (res.code == 200) {
          this.gainFrameInfo()
          this.$message({
            showClose: true,
            message: res.message,
            type: 'success',
          })
        }
      })
    },
    toUpdown(row) {
      if (!row.halF_DATE) {
        this.$message({
          showClose: true,
          message: '无时间信息',
          type: 'warning',
        })
        return
      }
      this.detailModel.id = row.id
      this.detailModel.TIME_FRAME = row.timE_FRAME
      this.detailModel.SEASON = row.season
      this.detailModel.HALF_DATE = row.halF_DATE
      this.detailModel.DEL_FLAG = row.deL_FLAG
      let a = this.detailModel.TIME_FRAME
      a = String(a)

      this.time_frame = a.split('-')

      this.detailModel.startTime = this.time_frame[0]
      this.detailModel.endTime = this.time_frame[1]
      this.addDetailDialog = true
    },
    toAdd() {
      this.detailModel = {}
      this.addDetailDialog = true
    },
    setDetail() {
      if (!this.detailModel.startTime) {
        this.$message({
          showClose: true,
          message: '请选择开始时间',
          type: 'warning',
        })
        return
      }
      if (!this.detailModel.endTime) {
        this.$message({
          showClose: true,
          message: '请选择结束时间',
          type: 'warning',
        })
        return
      }
      this.detailModel.time_frame =
        this.detailModel.startTime + '-' + this.detailModel.endTime

      console.log(this.detailModel)
      SetFrame(this.detailModel).then((res) => {
        this.gainFrameInfo()
        this.addDetailDialog = false
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.down-tree {
  height: 680px;
  display: block;
  overflow-y: scroll;
}
</style>
