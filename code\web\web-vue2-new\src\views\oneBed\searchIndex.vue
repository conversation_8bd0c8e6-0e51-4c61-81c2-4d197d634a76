<template>
  <div class="app_container">
    <div class="">
      <el-input
        v-model.trim="wardName"
        placeholder="请输入病区名称"
        clearable
        size="small"
        prefix-icon="el-icon-search"
        style="margin-bottom: 20px"
      />
    </div>
    <div class="down-tree">
      <el-tree
        :data="wardOptions"
        :props="defaultProps"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        ref="tree"
        :default-expand-all="false"
        highlight-current
        @node-click="handleNodeClick"
      />
    </div>
  </div>
</template>

<script>
import {utils} from "@/mixin.js";
import {GetLocationInfo, GetWardListInfo} from "../../api/oneBed/OneBedInfo";

export default {
  name: "searchIndex",
  mixins: [utils],
  components: {},
  props: {
    queryParams: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      total: 0,
      //名称
      wardName: undefined,
      // 树选项
      wardOptions: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
    };
  },
  methods: {
    getList() {
      GetLocationInfo(this.queryParams).then(res => {
        if (this.queryParams.Type === "1") {
          this.wardOptions = res.data.buildingNoList;
        } else {
          this.wardOptions = res.data.wardFloor;
        }
        if (this.wardOptions.length === 0) {
          GetWardListInfo().then(res => {
            this.wardOptions = res.data;
          });
        }
      });
      // console.log(this.wardOptions)
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.$emit("node-clicked", data);
    },

  },
  computed: {},
  watch: {
    // 根据名称部门树
    wardName(val) {
      this.$refs.tree.filter(val);
    },
    // 监听 queryParams.Type 的变化
    'queryParams.Type': function (newVal, oldVal) {
      this.getList();
    }
  },

  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.$bus.$on('data-refresh', res => {
      this.getList();
    })
  },

  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.getList();
  },
  // 生命周期 - 销毁前
  beforeDestroy() {
    this.$bus.$off('data-refresh');
  }
};
</script>

<style lang="scss" scoped>
@media only screen and (max-width: 1024px) {
  .down-tree {
    height: 680px;
    display: block;
    overflow-y: scroll;
  }
}

.searchIndex {
}

.down-tree {
  //height: 680px;
  display: block;
  overflow-y: scroll;
}

.app_container {
  padding-right: 20px;
}
</style>

