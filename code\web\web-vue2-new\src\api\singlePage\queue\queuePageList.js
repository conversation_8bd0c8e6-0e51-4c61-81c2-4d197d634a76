import request from '@/utils/request'

export function GetQueuePageList(data) {
  return request({
    url: '/QueueReferTo/GetQueuePageList',
    method: 'get',
    params: data
  })
}

export function GetQueuePageListByPerformedBy(data) {
  return request({
    url: '/QueueReferTo/GetQueuePageListByPerformedBy',
    method: 'get',
    params: data
  })
}

export function GetUnitList() {
  return request({
    url: '/QueueReferTo/GetUnitList',
    method: 'get',
  })
}

export function GetWindowList(unitId) {
  return request({
    url: '/QueueReferTo/GetWindowList?unitId=' + unitId,
    method: 'get',
  })
}
