<template>
  <div class="appoint-home">
    <div class="item-list">
      <div class="header-text">检查项目列表</div>
      <div class="case-table">
        <div
          class="case-table-item"
          v-for="(item, index) in examList"
          :style="
            currentRow == index
              ? 'backgroundColor: #1890ff;color:#fff'
              : item.createDate2 !== null && item.appointmentTime !== null
              ? 'backgroundColor: #66FFCC'
              : ''
          "
          :key="index"
          @click="getPatExamAppointInfo(item, index)"
        >
          <div>
            <span> 检查项目：</span
            ><span
              class="span-text"
              :style="currentRow == index ? 'color:#fff' : ''"
              >{{ item.examItem }}</span
            >
            <span
              class="span-text-warning"
              v-if="item.createDate2 !== null && item.appointmentTime !== null"
              >(已预约)</span
            >
            <span class="span-text-warning" v-else>(未预约)</span>
          </div>
          <div class="table-item-space">
            <div>
              检查位置：<span
                class="span-text"
                :style="currentRow == index ? 'color:#fff' : ''"
                >{{ item.examPositionName }}</span
              >
            </div>
            <div>
              申请科室：<span
                class="span-text"
                :style="currentRow == index ? 'color:#fff' : ''"
                >{{ item.applyDeptName }}</span
              >
            </div>
          </div>

          <div>
            申请时间：<span
              class="span-text"
              :style="currentRow == index ? 'color:#fff' : ''"
              >{{ item.createDate }}</span
            >
          </div>
          <div class="table-item-space">
            <div>
              预约日期：<span
                class="span-text-warning"
                :style="currentRow == index ? 'color:#fff' : ''"
                >{{ item.createDate2 }}</span
              >
            </div>
            <div>
              预约时间：<span
                class="span-text-warning"
                :style="currentRow == index ? 'color:#fff' : ''"
                >{{ item.appointmentTime }}</span
              >
            </div>
          </div>
          <div>
            申请医师：<span
              class="span-text"
              :style="currentRow == index ? 'color:#fff' : ''"
              >{{ item.applyDoctorName }}</span
            >
          </div>

          <!-- 操作图标 -->
          <div class="text-icon">
            <el-tooltip effect="dark" content="检查预约" placement="top">
              <el-button
                type="text"
                icon="el-icon-date"
                circle
                @click="getPatExamAppointInfo(item, index)"
              ></el-button>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    <div class="appoint-info">
      <div ref="appointInfoSearch" class="appoint-info-search">
        <!-- 时间类型 -->
        <div class="select-one">
          <div class="select-one-type">
            <el-radio-group
              v-model="timeType"
              @change="getExamAppointDetailInfo('1')"
            >
              <el-radio-button disabled label="时间类型："></el-radio-button>
              <el-radio-button label="凌晨"></el-radio-button>
              <el-radio-button label="早晨"></el-radio-button>
              <el-radio-button label="上午"></el-radio-button>
              <el-radio-button label="中午"></el-radio-button>
              <el-radio-button label="下午"></el-radio-button>
              <el-radio-button label="傍晚"></el-radio-button>
              <el-radio-button label="晚上"></el-radio-button>
            </el-radio-group>
          </div>
          <div class="select-one-source">
            <el-radio-group v-model="source">
              <el-radio-button label="来源："></el-radio-button>
              <el-radio-button label="住院"></el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <!-- 检查项目 -->
        <div class="select-two">
          <el-radio-group v-model="currentExamItem">
            <el-radio-button disabled label="检查项目："></el-radio-button>
            <el-radio-button
              class="two-top"
              :label="currentExamItem"
            ></el-radio-button>
          </el-radio-group>
        </div>
        <!-- 预约地点+窗口 -->
        <div class="select-three">
          <!-- 预约地点 -->
          <div
            class="select-loction"
            style="width: 50%"
            v-if="unitId !== null && unitId !== '' && unitId !== undefined"
          >
            <el-radio-group
              v-model="unitId"
              @change="getExamAppointDetailInfo('2')"
            >
              <el-radio-button label="预约地点："></el-radio-button>
              <span v-for="item in unitTree" :key="item.value">
                <el-radio-button class="two-top" :label="item.value">{{
                  item.label
                }}</el-radio-button>
              </span>
            </el-radio-group>
          </div>
          <!-- 预约窗口 -->
          <div
            class="select-window"
            v-if="
              windowId !== null && windowId !== '' && windowId !== undefined
            "
          >
            <el-radio-group
              v-model="windowId"
              @change="getExamAppointDetailInfo('1')"
            >
              <el-radio-button disabled label="预约窗口："></el-radio-button>
              <span v-for="item in windowTree" :key="item.value">
                <el-radio-button class="two-top" :label="item.value">{{
                  item.label
                }}</el-radio-button>
              </span>
            </el-radio-group>
          </div>
        </div>

        <!-- 日期选择 -->
        <div class="select-four">
          <div class="select-date">
            <el-radio-group
              v-model="textTime"
              @change="getExamAppointDetailInfo('1')"
            >
              <el-radio-button label="日期选择："></el-radio-button>
              <span v-for="(item, index) in timeList" :key="index">
                <el-radio-button :label="item"></el-radio-button>
              </span>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div
        class="select-five"
        :style="{ height: 'calc(100% - ' + height + 'px)' }"
      >
        <div
          class="four-item"
          v-for="(item, index) in timeData"
          :key="index"
          @click="patAppointmentSaveExamTime(item)"
          :class="{
            four:
              (!item.status || item.surplus === 0) &&
              !item.indicate &&
              item.value !== appointTime,
            five: item.indicate && item.value !== appointTime,
            one:
              (textTime !== null ||
                textTime !== undefined ||
                textTime !== '') &&
              item.value === appointTime,
          }"
        >
          <div class="item-son">{{ item.value }}</div>
          <div class="item-son">可预约{{ item.surplus }}个</div>
          <div class="item-son">已预约{{ item.sum }}个</div>
          <div class="item-son">总预约{{ item.numbers }}个</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  GetPatExamInfo,
  GetPatExamAppointInfo,
  GetExamAppointDetailInfo,
  patAppointmentExamTime,
} from "@/api/appointment/appointInpExam";

export default {
  name: "appoint-time",
  props: {
    patientInfo: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      default: Math.random(),
    },
  },
  data() {
    return {
      currentRow: 0,
      appointDateMore: false,
      height: 0,
      examList: [],
      timeType: "下午",
      source: "住院",
      currentExamItem: "心脏彩色多普勒超声检查",
      examNo: "",
      //是否是床旁患者
      isCpButton: false,
      //执行科室Code
      executeDeptCode: undefined,
      //检查项目Code
      examItemCode: undefined,
      unitName: "功能科",
      unitId: undefined,
      //获取检查患者当前信息
      patName: "",
      //性别
      sex: "",
      unitTree: [
        {
          label: "功能科",
          value: "d07de195a8c04c7c8e04a71d899e270a",
        },
        {
          label: "皮肤科",
          value: "e4ddac4ea47341bba3dd800af579bf5b1",
        },
      ],
      WindowName: "诊室一 (7)",
      windowId: undefined,
      windowTree: [
        {
          label: "诊室一 (7)",
          value: "66",
          index: 1,
        },
        {
          label: "诊室二 (10)",
          value: "11",
          index: 2,
        },
        {
          label: "诊室三 (6)",
          value: "d07de195a8c04c7c8e04a71d899e270a",
          index: 3,
        },
        {
          label: "诊室四 (0)",
          value: "22",
          index: 4,
        },
        {
          label: "诊室五 (6)",
          value: "33",
          index: 5,
        },
        {
          label: "诊室六 (6)",
          value: "44",
          index: 6,
        },
        {
          label: "诊室六 (7)",
          value: "55",
          index: 7,
        },
      ],
      thisTime: "",
      textTime: "",
      appointTime: "",
      timeList: [
        "2024-07-12",
        "2024-07-13",
        "2024-07-14",
        "2024-07-15",
        "2024-07-16",
      ],
      timeData: [],
    };
  },
  watch: {
    index: {
      immediate: true,
      handler() {
        this.getPatExamInfo();
      },
    },
  },
  async mounted() {
    await this.getDomHeight();
  },
  methods: {
    /**
     *住院患者保存异步方法调用
     */
    async patAppointmentSaveExamTime(data) {
      if (data.surplus === 0) {
        this.$msgbox
          .alert(
            '<div style="font-size: 18px !important;color: red">' +
              "当前时间点预约人员已满,请选择其他时间点预约!!!" +
              "</div>",
            "系统提示",
            {
              confirmButtonText: "确定",
              type: "warning",
              dangerouslyUseHTMLString: true,
            }
          )
          .then(() => {})
          .catch(() => {});
        return;
      }

      if (!data.status) {
        this.$msgbox
          .alert(
            '<div style="font-size: 18px !important;color: red">' +
              "当前选择时间已过,请选择其他时间预约!!!" +
              "</div>",
            "系统提示",
            {
              confirmButtonText: "确定",
              type: "warning",
              dangerouslyUseHTMLString: true,
            }
          )
          .then(() => {})
          .catch(() => {});
        return;
      }

      this.$msgbox
        .alert(
          '<div style="font-size: 18px !important;">' +
            '<div style="display: flex"><div style="width: 30%;font-size: 18px;padding-left: 10%;">姓名：</div><div style="font-size: 16px;color: black;">' +
            this.patName +
            "</div></div>" +
            '<div style="display: flex"><div style="width: 30%;font-size: 18px;padding-left: 5%;">检查号：</div><div style="font-size: 16px;color: black;">' +
            this.examNo +
            "</div></div>" +
            '<div style="display: flex"><div style="width: 30%;font-size: 18px;">检查项目：</div><div style="font-size: 16px;color: black;">' +
            this.currentExamItem +
            "</div></div>" +
            '<div style="display: flex"><div style="width: 30%;font-size: 18px;">预约日期：</div><div style="font-size: 16px;color: black;">' +
            this.textTime +
            "</div></div>" +
            '<div style="display: flex"><div style="width: 30%;font-size: 18px;">预约时间：</div><div style="font-size: 16px;color: black;">' +
            data.value +
            "</div></div>" +
            '<div style="display: flex"><div style="width: 30%;font-size: 18px;margin-top: 10px;">叫号单元：</div><div style="font-size: 32px;color: black;margin-top: 10px;">' +
            this.unitName +
            "</div></div>" +
            '<div style="display: flex"><div style="width: 30%;font-size: 18px;margin-top: 10px;">叫号窗口：</div><div style="font-size: 32px;color: black;margin-top: 10px;">' +
            this.WindowName +
            "</div></div>" +
            '<div style="font-size: 24px;margin-top: 10px;color: red">请确认以上信息是否正确!!!</div>' +
            "</div>",
          "系统提示",
          {
            confirmButtonText: "确定",
            type: "warning",
            dangerouslyUseHTMLString: true,
          }
        )
        .then(() => {
          let model = {
            applyDeptCode: this.executeDeptCode,
            examNo: this.examNo,
            patientId: this.patientInfo.patientId,
            patientName: this.patName,
            sex: this.sex,
            visitId: this.patientInfo.visitId,
            empNo: this.patientInfo.empNo,
            timeId: data.id,
            appointmentTime: data.value,
            appointmentDate: this.textTime,
            description: this.currentExamItem,
            descriptionCode: this.examItemCode,
            executeDeptCode: this.executeDeptCode,
            unitId: this.unitId,
            windowId: this.windowId,
          };
          console.log("model", model);
          patAppointmentExamTime(model).then((res) => {
            this.$message.success("预约成功");
            this.$msgbox
              .alert(
                '<div style="font-size: 36px !important; text-align: center;font-weight: 800">队列号</div>' +
                  '<div style="font-size: 72px !important; text-align: center;margin-top: 50px;">' +
                  res.data +
                  "</div>",
                "系统提示",
                {
                  confirmButtonText: "确定",
                  type: "warning",
                  dangerouslyUseHTMLString: true,
                }
              )
              .then(() => {});
            this.getPatExamInfo();
          });
        })
        .catch(() => {
          this.$message.info("已取消预约");
        });
    },

    /**
     * 选择更多日期
     */
    handleAppointDate() {
      this.appointDateMore = !this.appointDateMore;
    },

    /**
     * 获取当前窗口叫号号源明细
     */
    async getExamAppointDetailInfo(searchType) {
      let query = {
        examItemCode: this.examItemCode,
        executeDeptCode: this.executeDeptCode,
        timeType: this.timeType,
        unitId: this.unitId,
        windowId: this.windowId,
        isCpButton: this.isCpButton,
        appointmentDate: this.textTime,
        searchType,
      };
      /***
       *多条件检索
       */
      await GetExamAppointDetailInfo(query).then((response) => {
        this.timeData = response.data.disposeModel.timeModel;
        this.unitTree = response.data.windowUnitInfo.unitTree;
        this.windowTree = response.data.windowUnitInfo.windowTree;
        this.unitId = response.data.unitId;
        this.windowId = response.data.windowId;
      });
    },

    /**
     *获取住院患者检查申请列表
     */
    async getPatExamInfo() {
      await GetPatExamInfo(this.patientInfo).then((response) => {
        this.examList = response.data.examList;
        this.timeList = response.data.timeList;
        this.timeType = response.data.timeType;
        this.source = response.data.source;
        this.patName = response.data.name;
        this.sex = response.data.sex;
        this.currentExamItem = response.data.examItem;
        this.unitName = response.data.currentUnitName;
        this.unitId = response.data.currentUnitId;
        this.unitTree = response.data.unitTree;
        this.windowId = response.data.currentWindowId;
        this.WindowName = response.data.currentWindowName;
        this.windowTree = response.data.windowTree;
        this.thisTime = response.data.thisTime;
        this.textTime = response.data.thisTime;
        this.timeData = response.data.examAppointDispost.timeModel;
        this.examNo = response.data.examNo;
        this.isCpButton = response.data.isCpButton;
        this.examItemCode = response.data.examItemCode;
        this.executeDeptCode = response.data.executeDeptCode;
        this.currentRow = 0;
      });
    },

    /**
     * 获取住院患者检查项目预约信息
     */
    async getPatExamAppointInfo(row, index) {
      let query = {
        examNo: row.examNo,
        examName: row.examItem,
        appointTime: row.appointmentTime,
        examItemCode: row.examItemCode,
        executeDeptCode: row.executeDeptCode,
        IsCpButton: this.isCpButton,
      };
      this.currentRow = index;
      await GetPatExamAppointInfo(query).then((response) => {
        this.timeList = response.data.timeList;
        this.timeType = response.data.timeType;
        this.source = response.data.source;
        this.currentExamItem = response.data.examItem;
        this.unitName = response.data.currentUnitName;
        this.unitId = response.data.currentUnitId;
        this.unitTree = response.data.unitTree;
        this.windowId = response.data.currentWindowId;
        this.WindowName = response.data.currentWindowName;
        this.windowTree = response.data.windowTree;
        this.thisTime = response.data.thisTime;
        this.textTime = response.data.thisTime;
        this.timeData = response.data.examAppointDispost.timeModel;
        this.examNo = response.data.examNo;
        this.examItemCode = response.data.examItemCode;
        this.executeDeptCode = response.data.executeDeptCode;
      });
    },

    /**
     *获取Html查询模块高度
     */
    async getDomHeight() {
      await this.$nextTick(() => {
        this.height = this.$refs.appointInfoSearch.offsetHeight;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.appoint-home {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 5px;
  box-sizing: border-box;
  & > div {
    height: 100%;
  }
  .item-list {
    flex: 0 0 29%; /* 不伸缩，固定宽度为30% */
    overflow-y: auto;
    .header-text {
      padding: 5px;
      background-color: #1c84c6;
      color: #f2f2f2;
      height: 30px;
      font-size: 18px;
      text-align: center;
    }
    .case-table {
      margin-top: 5px;
      height: calc(100% - 40px);
      overflow-y: auto;
      overflow-x: auto;
      cursor: pointer;

      .case-table-item {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        padding: 10px;
        border: 1px solid #00a19b;
        float: left;
        width: 100%;
        margin-top: 5px;
        transition: background-color 0.3s, transform 0.3s ease-in-out; /* 平滑的过渡效果 */
        position: relative;
        .table-item-space {
          display: flex;
          justify-content: space-between;
        }
        .text-icon {
          position: absolute;
          height: 20px;
          bottom: 15px;
          right: 5px;
          font-size: 14px;
          .el-button {
            margin-right: 10px;
          }
        }
      }

      .case-table-item:hover {
        background-color: #00ffff;
        transform: translateY(-3px); /* 鼠标悬停时向上移动 */
      }
      .span-text {
        color: #3a5fcd;
      }
      .span-text-warning {
        color: red;
      }
    }
  }
  .appoint-info {
    flex: 0 0 70%; /* 不伸缩，固定宽度为70% */
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    .appoint-info-search {
      flex: 1;
      .select-one {
        display: flex;
        justify-content: space-between;
      }
      .select-two {
        margin-top: 10px;
      }
      .select-three {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;

        justify-content: flex-start;
        margin-top: 10px;
      }
      .select-four {
        // display: flex;
        // justify-content: space-between;
        margin-top: 10px;
        // .select-date {
        //   width: 100%;
        // }
        // .select-date-more {
        //   display: flex;
        //   justify-content: space-between;
        //   .span-date {
        //     width: 50%;
        //     color: blue;
        //   }
        // }
      }
    }

    .select-five {
      overflow-y: auto;
      height: 250px;
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      margin-top: 10px;
      cursor: pointer;
      .four-item {
        align-items: center;
        text-align: center;
        width: 150px;
        transition: transform 0.3s ease; /* 平滑的过渡效果 */
        background: #edf4fc;
        margin: 1%;
        padding: 5px;

        border: 1px solid #ffffff;
        .item-son {
          height: 25px;
          line-height: 25px;
          width: 100%;
        }
      }

      .four-item:hover {
        transform: scale(1.1); /* 放大10% */
      }
    }
  }
}

.one {
  cursor: pointer;
  color: #ffffff;
  box-shadow: 0 0 0 0 grey;
  transform: scale(1.1);
  background: #1890ff !important;
  z-index: 1;
}

.two {
  ::v-deep.el-radio-button--medium .el-radio-button__inner {
    padding: 10px 13px !important;
  }
}

.four {
  color: #ffffff;
  background: red !important;
  z-index: 99;
}

.five {
  color: #ffffff;
  background: #20b2aa !important;
  z-index: 2;
}
</style>
