<template>
  <div></div>
</template>

<script>
import { GetPrintMeg } from '@/api/checkAndConfirm/checkConfirm'
import { ExamConfirmPrint,ExamTakePacsPrint,ExamItemRefundPrint } from '@/utils/common/pb-proxy'
export default {
  name: 'ExamPrint',
  props: [],
  components: {},
  data() {
    return {
      printData: {
        examNos: [],
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    confirmPrintTest(){
      let data = [];
      let printData = {
        exam_no: "000000001",
        name: "测试",
        patient_id: "000000001",
        sex: "测试",
        age: "测试",
        patient_local_id: "00000001",
        req_dept: "测试",
        req_date_time: "2024-12-01 00:00:00",
        req_physician: "测试",
        queueno: "测试",
        notice: "测试",
        illness_desc: "测试",
        clin_symp: "测试",
        phys_sign: "测试",
        clin_diag: "测试",
        bed_no: 0,
        memo: "测试",
        windowno: "测试",
        phone: "00000000000",
        printed: false,
        exam_items: {
          item_no: "00000001",
          item_name: "数据测试",
          item_price: "00.00",
        }
      }
      data.push(printData)
      ExamConfirmPrint(data);
    },
    imagePrintTest(){
      let data = [];
      let printImageData = {
        name: "测试",
        patient_id: "00000001",
        sex: "测试",
        patient_local_id: "00000001",
        notice: "测试",
        printed:false,
        exam_items: {
          item_no: "000000001",
          item_name: "数据测试",
          item_price: "00.00",
        },
      };
      data.push(printImageData);
      ExamTakePacsPrint(data);
    },
    returnPrintTest(){
      let data =[];
      let printData = {
        name: "测试",
        performed_by: "测试",
        rcpt_no: "000000001",
        create_time: "2024-12-01 00:00:00",
        costs: "00.00",
        item_name: "数据测试",
        refund_reason: "数据测试",
        dept_name: "数据测试",
        date: this.formatDate(new Date),
      }
      data.push(printData);
      ExamItemRefundPrint(data);
    },
    printOptFor(data){
      let examNos = [];
      if (data && data.length > 0){
        data.forEach(t => {
          examNos.push(t.exam_no);
        })
        let print = this.printData;
        print.examNos = examNos;
        this.pringAlphacall(print,false);
      }
    },
    printRow(data){
      let examNos = [];
      if (data){
        examNos.push(data.examNo);
        let print = this.printData;
        print.examNos = examNos;
        this.pringAlphacallRow(print,true)
      }
    },
    printRowImg(data){
      let examNos = [];
      if (data){
        examNos.push(data.examNo);
        let print = this.printData;
        print.examNos = examNos;
        this.pringAlphacallRowImage(print,true)
      }
    },
    printOutpReturn(data){
      if (data){
        ExamItemRefundPrint(data);
      }
    },
    // 一键打印
    // pringAlphacall(selectData,status){
    //   GetPrintMeg(selectData).then(res => {
    //     let resData = res.data;
    //     let imagePrintData = this.printDataImageDispose(resData,status);
    //     let commonPrintData =  this.printDataDispose(resData,status)
    //     if (imagePrintData.length > 0){
    //       ExamTakePacsPrint(imagePrintData);
    //     }
    //     ExamConfirmPrint(commonPrintData);
    //     this.printData.examNos = [];
    //   })
    // },
    // printDataImageDispose(data,status){
    //   let printImageData = [];
    //   data.forEach(t => {
    //     if (t.executeDeptCode === '0203'){
    //       printImageData.push(this.printDataImagePackaging(t,status))
    //     }
    //   })
    //   return printImageData;
    // },
    // printDataDispose(data,status){
    //   let printData = [];
    //   data.forEach(t => {
    //     printData.push(this.printDataPackaging(t,status))
    //   })
    //   return printData;
    // },

    //循环打印
    pringAlphacall(selectData,status){
      GetPrintMeg(selectData).then(res => {
        let resData = res.data;
        this.printDataImageDispose(resData,status);
        this.printDataDispose(resData,status)
        this.printData.examNos = [];
      })
    },
    pringAlphacallRow(selectData,status){
      GetPrintMeg(selectData).then(res => {
        let resData = res.data;
        this.printDataDispose(resData,status)
        this.printData.examNos = [];
      })
    },
    pringAlphacallRowImage(selectData,status){
      GetPrintMeg(selectData).then(res => {
        res.data.forEach(t => {
          if (t.executeDeptCode === '0203'){
            let print = []
            print.push(this.printDataImagePackaging(t,status))
            ExamTakePacsPrint(print);
          }
        })
        this.printData.examNos = [];
      })
    },
    printDataImageDispose(data,status){
      data.forEach(t => {
        if (t.executeDeptCode === '0203' && t.visitId === '0'){
          let print = []
          print.push(this.printDataImagePackaging(t,status))
          ExamTakePacsPrint(print);
        }
      })
    },
    printDataDispose(data,status){
      data.forEach(t => {
        let print = []
        print.push(this.printDataPackaging(t,status))
        ExamConfirmPrint(print)
      })
    },
    printDataImagePackaging(data,status){
      let text = "";
      if (data.visitId === '0'){
        text = "请您在看到屏幕上的名字后到自助打印机打印报告，右上方报告单可扫码查询你的影像图像。"
      }else{
        text = "检查结束后，主管医生会自行将报告打印入病历保存。"
      }
      let printImageData = {
        name: data.name,
        patient_id: data.patientId,
        sex: data.sex,
        patient_local_id: data.patientLocalId,
        notice: text,
        printed:status,
        dept_name: data.reqDeptName,
        date: data.examDateTime,
        exam_items: [],
      }
      let itemData = data.examItems;
      itemData.forEach(t => {
        printImageData.exam_items.push(this.printItemDataPackaging(t));
      })
      return printImageData;
    },
    printDataPackaging(data,status){
      let printData = {
        exam_no: data.examNo,
        name: data.name,
        patient_id: data.patientId,
        sex: data.sex,
        age: data.ages,
        patient_local_id: data.patientLocalId,
        req_dept: data.reqDeptName,
        req_date_time: data.reqDateTime,
        req_physician: data.reqPhysician,
        queueno: data.queueNo,
        notice: data.notice,
        illness_desc: data.illnessDesc,
        clin_symp: data.clinSymp,
        phys_sign: data.physSign,
        clin_diag: data.clinDiag,
        bed_no: data.bedNo,
        memo: data.reqMemo,
        windowno: data.windowName,
        phone: data.phone,
        printed: status,
        no_perform_msg: '您有未做项目',
        exam_class: data.examClass,
        exam_sub_class: data.examSubClass,
        exam_items: []
      }
      if (data.executeDeptCode === '0203'){
        printData.windowno = '';
      }
      let itemData = data.examItems;
      itemData.forEach(t => {
        printData.exam_items.push(this.printItemDataPackaging(t));
      })
      return printData;
    },
    printItemDataPackaging(item){
      return {
        item_no: item.examItemNo,
        item_name: item.examItem,
        item_price: item.costs,
      };
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style scoped lang="scss">

</style>
