<template>
  <div class="module-test-master" :style="maxHeight">
    <div class="single-master">
      <div class="element-table">
        <el-table ref="table" :data="testData" style="width: 100%" border height="300px" highlight-current-row
                  @row-click="testTableClick" :span-method="objectSpanMethod">
          <el-table-column type="index" width="40" align="center"></el-table-column>
          <el-table-column prop="TEST_NO" align="center" label="检验号" width="90"></el-table-column>
          <el-table-column prop="ITEM_NAME" align="center" label="检验名称" width="180"></el-table-column>
          <el-table-column prop="SPECIMEN" align="center" label="标本" width="100"></el-table-column>
          <el-table-column prop="REQUESTED_DATE_TIME" align="center" label="申请时间" width="120"></el-table-column>
          <el-table-column prop="ORDERING_PROVIDER" align="center" label="申请者" width="80"></el-table-column>
          <el-table-column prop="ORDERING_DEPT_NAME" align="center" label="申请科室" width="120"></el-table-column>
          <el-table-column prop="PRINT_APPLY_DATETIME" align="center" label="采血时间" width="120"></el-table-column>
          <el-table-column prop="TRANSCRIPTIONIST" align="center" label="审核者" width="80"></el-table-column>
          <el-table-column prop="RESULTS_RPT_DATE_TIME" align="center" label="报告时间" width="120"></el-table-column>
        </el-table>
      </div>
      <div class="element-table">
        <el-table ref="table" :data="reportData" style="width: 100%" border height="300px" highlight-current-row>
          <el-table-column prop="PRINT_ORDER" width="40" align="center"></el-table-column>
          <el-table-column prop="RESULT_DATE_TIME" align="center" label="报告日期" width="120"></el-table-column>
          <el-table-column prop="REPORT_ITEM_NAME" align="center" label="报告项目名称" width="180"></el-table-column>
          <el-table-column prop="RESULT" align="center" label="结果" width="100"></el-table-column>
          <el-table-column prop="ABNORMAL_INDICATOR" align="center" label="异常" width="60"></el-table-column>
          <el-table-column prop="UNITS" align="center" label="单位" width="100"></el-table-column>
          <el-table-column prop="PRINT_CONTEXT" align="center" label="参考值" width="120"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {getMaternalDataReportingTestApi,getMaternalDataReportingTestByTestNoApi} from '@/api/singlePage/maternalDataReportingInquire'
export default {
  name: 'moduleText',
  props: ['rowData','maxHeight'],
  components: {},
  data() {
    return {
      testData:[],
      reportData:[],
      mergeArr: ["TEST_NO"],
      mergeObj: {},
    }
  },
  created() {
    this.getMaternalDataReportingTest();
  },
  mounted() {
  },
  methods: {
    getSpanArr(data) {
      // 循环需要合并的单元格数据
      this.mergeArr.forEach((key, index1) => {
        let count = 0; // 用来记录需要合并行的起始位置
        this.mergeObj[key] = []; // 记录每一列的合并信息
        data.forEach((item, index) => {
          // index == 0表示数据为第一行，直接 push 一个 1
          if (index === 0) {
            this.mergeObj[key].push(1);
          } else {
            // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并 并push 一个 0 作为占位
            if (item[key] === data[index - 1][key]) {
              this.mergeObj[key][count] += 1;
              this.mergeObj[key].push(0);
            } else {
              // 如果当前行和上一行其值不相等
              count = index; // 记录当前位置
              this.mergeObj[key].push(1); // 重新push 一个 1
            }
          }
        });
      });
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 判断列的属性
      if (this.mergeArr.indexOf(column.property) !== -1) {
        // 判断其值是不是为0
        if (this.mergeObj[column.property][rowIndex]) {
          return [this.mergeObj[column.property][rowIndex], 1];
        } else {
          // 如果为0则为需要合并的行
          return [0, 0];
        }
      }
    },
    testTableClick(row){
      this.reportData = [];
      getMaternalDataReportingTestByTestNoApi(row.TEST_NO).then(res => {
        if (res.code === 200){
          this.reportData = res.data;
        }
      })
    },
    getMaternalDataReportingTest(){
      getMaternalDataReportingTestApi(this.rowData.PATIENT_ID).then(res => {
        this.testData = res.data;
        this.getSpanArr(res.data);
      })
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../../assets/styles/singlePage";
</style>
