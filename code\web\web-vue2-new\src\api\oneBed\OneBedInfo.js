import request from '@/utils/request'

const baseURL = 'OneBedInfo/'

export function GetWardListInfo() {
  return request({
    url: baseURL + 'GetWardListInfo',
    method: 'get',
  })
}
export function GetFloorType() {
  return request({
    url: baseURL + 'GetFloorType',
    method: 'get',
  })
}

export function GetDetailBedInfo(query) {
  return request({
    url: baseURL + 'GetDetailBedInfo',
    method: 'get',
    params: query
  })
}

export function GetLocationInfo(query) {
  return request({
    url: baseURL + 'GetLocationInfo',
    method: 'get',
    params: query
  })
}

export function SaveLocationInfo(data) {
  return request({
    url: baseURL + 'SaveLocationInfo',
    method: 'post',
    data
  })
}

export function SaveFloorType(data) {
  return request({
    url: baseURL + 'SaveFloorType',
    method: 'post',
    data
  })
}

