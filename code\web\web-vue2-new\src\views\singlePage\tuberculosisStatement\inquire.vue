<template>
  <div class="single-master">
    <div class="single-title">结核报表</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="开始时间:">
              <el-date-picker
                v-model="queueForm.beginDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                :clearable="false"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker
                v-model="queueForm.endDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                :clearable="false"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="病人ID号:">
              <el-input v-model="queueForm.patientId" placeholder="ID号" clearable></el-input>
            </el-form-item>
            <el-form-item label="病人姓名:">
              <el-input v-model="queueForm.name" placeholder="病人姓名" clearable></el-input>
            </el-form-item>
            <div>
              <el-form-item label="检查类别:">
                <el-input v-model="queueForm.examClass" placeholder="检查类别" clearable></el-input>
              </el-form-item>
              <el-form-item label="开单医生:">
                <el-input v-model="queueForm.reqDoctor" placeholder="开单医生" clearable></el-input>
              </el-form-item>
              <el-form-item>
                <div class="element-button">
                  <el-button type="primary" icon="el-icon-search" @click="getTuberculosisStatement">查询</el-button>
                  <el-button type="primary" icon="el-icon-download" @click="exportData">导出</el-button>
                  <el-button type="primary" icon="el-icon-refresh" @click="clear">清空</el-button>
                </div>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div class="element-table">
          <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 185">
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column prop="REPORT_DATE_TIME" align="center" label="检查时间" width="120"></el-table-column>
            <el-table-column prop="EXAM_NO" align="center" label="检查号" width="90">
              <template slot-scope="scope">
                <div class="text-click" @click="openResult(scope.row)">{{ scope.row.EXAM_NO }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="NAME" align="center" label="姓名" width="70"></el-table-column>
            <el-table-column prop="PATIENT_ID" align="center" label="ID号" width="80"></el-table-column>
            <el-table-column prop="DATE_OF_BIRTH" align="center" label="出生日期" width="100"></el-table-column>
            <el-table-column prop="EXAM_CLASS" align="center" label="检查类别" width="100"></el-table-column>
            <el-table-column prop="EXAM_SUB_CLASS" align="center" label="检查子类" width="100"></el-table-column>
            <el-table-column prop="DESCRIPTION" align="center" label="诊断"></el-table-column>
            <el-table-column prop="REQ_PHYSICIAN" align="center" label="开单医生" width="80"></el-table-column>
          </el-table>
        </div>
        <el-dialog
          :visible.sync="tuberculosisStatus"
          :title="title"
          width="85%"
        >
        <tuberculos-result :key="tuberculosisKey" :row-data="tuberculosisRowData"></tuberculos-result>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { getTuberculosisStatementList, exportTuberculosisStatement } from '@/api/singlePage/tuberculosisStatement'
import { excelDownloadXLSX } from '@/utils/BlobUtils'
import TuberculosResult from './module/tuberculosResult.vue'

export default {
  name: 'inquires',
  props: [],
  components: { TuberculosResult },
  data() {
    return {
      queueForm: {
        beginDate: this.formatDate(new Date()),
        endDate: this.formatDate(new Date()),
        patientId: '',
        name: '',
        examClass: '',
        reqDoctor: ''
      },
      tableDate: [],
      tableHeight: undefined,
      tuberculosisStatus: false,
      tuberculosisKey: 0,
      title: '结核结果',
      tuberculosisRowData: {}
    }
  },
  created() {
    this.handleResize()
    this.getTuberculosisStatement()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    openResult(row) {
      console.log(row)
      this.tuberculosisRowData = row
      ++this.tuberculosisKey
      this.tuberculosisStatus = true
    },
    exportData() {
      const loading = this.$loading({
        lock: true,
        text: '休息一下,数据正在努力导出中(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      exportTuberculosisStatement(this.queueForm).then(res => {
        let fileName = '结核报表统计'
        excelDownloadXLSX(res, fileName)
      }).finally(() => {
        loading.close()
      })
    },
    getTuberculosisStatement() {
      const loading = this.$loading({
        lock: true,
        text: '休息一下,数据正在努力加载中(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      getTuberculosisStatementList(this.queueForm).then(res => {
        if (res.code === 200) {
          this.tableDate = res.data
        }
      }).finally(() => {
        loading.close()
      })
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    clear() {
      this.queueForm = {
        beginDate: this.formatDate(new Date()),
        endDate: this.formatDate(new Date()),
        patientId: '',
        name: '',
        examClass: '',
        reqDoctor: ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.text-click {
  color: #00afff;
}

:hover.text-click {
  cursor: pointer;
  border-bottom: 1px solid #00afff;
}
</style>
