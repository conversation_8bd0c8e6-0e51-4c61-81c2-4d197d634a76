import request from '@/utils/request2'

export function maternalDataReportingInquire(data) {
  return request({
    url: '/singlePage/maternalDataReporting/inquire',
    method: 'post',
    data: data
  })
}

export function exportMaternalDataReporting(data) {
  return request({
    url: '/singlePage/maternalDataReporting/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function getMaternalDataReportingInquireByPatientId(patientId) {
  return request({
    url: '/singlePage/maternalDataReporting/inquire/' + patientId,
    method: 'get',
  })
}

export function getMaternalDataReportingExamApi(patientId) {
  return request({
    url: '/singlePage/maternalDataReporting/exam/' + patientId,
    method: 'get',
  })
}

export function getMaternalDataReportingTestApi(patientId) {
  return request({
    url: '/singlePage/maternalDataReporting/test/' + patientId,
    method: 'get',
  })
}

export function getMaternalDataReportingTestByTestNoApi(testNo) {
  return request({
    url: '/singlePage/maternalDataReporting/test/report/' + testNo,
    method: 'get',
  })
}
