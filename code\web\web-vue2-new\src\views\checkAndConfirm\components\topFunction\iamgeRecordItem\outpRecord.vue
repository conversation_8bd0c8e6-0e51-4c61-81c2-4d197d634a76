<template>
  <div class="outp-master">
    <div class="outp-form">
      <el-form ref="form" :model="queueForm" :inline="true">
        <el-form-item label="ID号：">
          <el-input v-model="queueForm.patientId" style="width: 120px;"></el-input>
        </el-form-item>
        <el-form-item label="查询时间:" label-width="85px">
          <div class="form-flex">
            <el-date-picker v-model="queueForm.beginDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
            <div class="date-f">-</div>
            <el-date-picker v-model="queueForm.endDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="formSelectClick">提取</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="outp-item">
      <div class="outp-left">
<!--        收费主记录-->
        <div>
          <div class="outp-title">
            收费主记录
          </div>
          <div class="item-one">
            <el-table :data="chargeMaster" border style="width: 100%" height="220" highlight-current-row @row-click="chargeRowClick">
              <el-table-column align="center" prop="rcpT_NO" label="缴费流水号" width="160"></el-table-column>
              <el-table-column align="center" prop="patienT_ID" label="ID号" width="100"></el-table-column>
              <el-table-column align="center" prop="name" label="姓名" width="90"></el-table-column>
              <el-table-column align="center" prop="totaL_CHARGES" label="金额" width="80"></el-table-column>
              <el-table-column align="center" prop="chargE_INDICATOR" label="计费标记" width="90">
                <template slot-scope="scope">
                  <div v-if="scope.row.chargE_INDICATOR === 0">已缴费</div>
                  <div v-else style="color: #a95812">未缴费</div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="visiT_DATE" label="缴费日期"></el-table-column>
              <el-table-column align="center" prop="paY_MONEY_TYPE" label="支付方式" width="100"></el-table-column>
            </el-table>
          </div>
        </div>
<!--        收费明细-->
        <div class="outp-patient">
          <div class="outp-title">
            收费明细
          </div>
          <div class="item-one">
            <el-table :data="ChargeSchedule" border style="width: 100%" height="260" highlight-current-row>
              <el-table-column align="center" prop="iteM_NAME" label="项目名称" width="180"></el-table-column>
              <el-table-column align="center" prop="iteM_SPEC" label="项目规格" width="130"></el-table-column>
              <el-table-column align="center" prop="units" label="单位" width="80"></el-table-column>
              <el-table-column align="center" prop="amount" label="数量" width="55"></el-table-column>
              <el-table-column align="center" prop="costs" label="收费" width="75"></el-table-column>
              <el-table-column align="center" prop="performeD_BY_NAME" label="执行科室" width="100"></el-table-column>
              <el-table-column align="center" prop="ordereD_BY_DEPT_NAME" label="开单科室" width="110"></el-table-column>
              <el-table-column align="center" prop="ordereD_BY_DOCTOR_NAME" label="开单医生" width="65"></el-table-column>
            </el-table>
          </div>
        </div>
<!--        开单记录-->
        <div class="outp-patient">
          <div class="outp-title">
            开单记录
          </div>
          <div class="item-one">
            <el-table :data="billingMaster" border style="width: 100%" height="220" highlight-current-row>
              <el-table-column align="center" prop="visiT_DATE" label="就诊日期" width="90">
                <template slot-scope="scope">
                  <div>{{scope.row.visiT_DATE.split(" ")[0]}}</div>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="name" label="姓名" width="75"></el-table-column>
              <el-table-column align="center" prop="druG_NAME" label="药品名称" width="120"></el-table-column>
              <el-table-column align="center" prop="administration" label="途径" width="100"></el-table-column>
              <el-table-column align="center" prop="firM_ID" label="厂家" width="100"></el-table-column>
              <el-table-column align="center" prop="amount" label="数量" width="40"></el-table-column>
              <el-table-column align="center" prop="charges" label="金额" width="60"></el-table-column>
              <el-table-column align="center" prop="chargE_INDICATOR" width="70" label="计费标志">
                <template slot-scope="scope">
                  <div v-if="scope.row.chargE_INDICATOR === 1">已缴费</div>
                  <div v-else>未缴费</div>
                </template>
              </el-table-column>
              <el-table-column align="center" width="75" prop="presC_ATTR" label="处方属性"></el-table-column>
              <el-table-column align="center" width="65" prop="doctor" label="医生"></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
      <div class="outp-center"></div>
      <div class="outp-right">
<!--        摆药主记录-->
        <div>
          <div class="outp-title">
            摆药主记录
          </div>
          <div class="item-one">
            <el-table :data="dispenseMedicine" border style="width: 100%" height="365" highlight-current-row @row-click="dispenseMedicineRowClick">
              <el-table-column align="center" prop="name" label="姓名" width="65"></el-table-column>
              <el-table-column align="center" prop="presC_ATTR" label="病人来源" width="70"></el-table-column>
              <el-table-column align="center" prop="dispensinG_DATETIME" label="发药日期" width="145"></el-table-column>
              <el-table-column align="center" prop="presC_DATE" label="处方日期" width="145"></el-table-column>
              <el-table-column align="center" prop="address" label="剂数" width="45"></el-table-column>
              <el-table-column align="center" prop="presC_ATTR" label="药房" width="75"></el-table-column>
              <el-table-column align="center" prop="dispensinG_PROVIDER" label="发药人" width="65"></el-table-column>
              <el-table-column align="center" prop="chargE_TYPE" label="费别" width="60"></el-table-column>
              <el-table-column align="center" prop="costs" label="应收" width="60"></el-table-column>
              <el-table-column align="center" prop="payments" label="实收" width="60"></el-table-column>
              <el-table-column align="center" prop="address" label="发放标志" width="65"></el-table-column>
              <el-table-column align="center" prop="address" label="审核人" ></el-table-column>
              <el-table-column align="center" prop="address" label="药房确认标识" width="110"></el-table-column>
            </el-table>
          </div>
        </div>
        <!--        发药明细-->
        <div class="outp-patient">
          <div class="outp-title">
            发药明细
          </div>
          <div class="item-one">
            <el-table :data="DispensingDetails" border style="width: 100%" height="365" highlight-current-row>
              <el-table-column align="center" prop="druG_NAME" label="药品名称"></el-table-column>
              <el-table-column align="center" prop="druG_SPEC" label="规格"></el-table-column>
              <el-table-column align="center" prop="firM_ID" label="厂家"></el-table-column>
              <el-table-column align="center" prop="packagE_UNITS" label="单位"></el-table-column>
              <el-table-column align="center" prop="administration" label="用法"></el-table-column>
              <el-table-column align="center" prop="quantity" label="单剂数量"></el-table-column>
              <el-table-column align="center" prop="costs" label="单剂应收"></el-table-column>
              <el-table-column align="center" prop="payments" label="单剂实收"></el-table-column>
              <el-table-column align="center" prop="" label="总数量"></el-table-column>
              <el-table-column align="center" prop="" label="应收"></el-table-column>
              <el-table-column align="center" prop="" label="实收"></el-table-column>
              <el-table-column align="center" prop="" label="计量"></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  GetCheckConfirmTable,
  GetOutpChargeScheduleMsg,
  GetOutpDispensingDetailsMsg,} from "@/api/checkAndConfirm/imageBillingRecord"
export default {
  name: 'outpRecord',
  props: ['queueForm'],
  components: {},
  data() {
    return {
      billingMaster: [],
      chargeMaster: [],
      dispenseMedicine:[],
      ChargeSchedule: [],
      DispensingDetails:[],
    }
  },
  created() {
    this.reset();
  },
  mounted() {
  },
  methods: {
    formSelectClick(){
        const loading = this.$loading({
          lock: true,
          text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
          spinner: 'el-icon-coffee-cup',
          background: 'rgba(0, 0, 0, 0.7)'
        })
      GetCheckConfirmTable(this.queueForm).then(res => {
        if (res.code === 200){
          this.billingMaster = res.data.billingMaster;
          this.chargeMaster = res.data.chargeMaster;
          this.dispenseMedicine = res.data.dispenseMedicine;
        }
      }).finally(() => {
        loading.close();
      })
    },
    chargeRowClick(row){
      this.ChargeSchedule = [];
      const loading = this.$loading({
        lock: true,
        text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      GetOutpChargeScheduleMsg(row.rcpT_NO).then(res => {
        if (res.code === 200){
          this.ChargeSchedule = res.data;
        }
      }).finally(() => {
        loading.close();
      })
    },
    dispenseMedicineRowClick(row){
      this.DispensingDetails = [];
      const loading = this.$loading({
        lock: true,
        text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      GetOutpDispensingDetailsMsg(row.presC_DATE,row.presC_NO).then(res => {
        if (res.code === 200){
          this.DispensingDetails = res.data;
        }
      }).finally(() => {
        loading.close();
      });
    },
    reset(){
      this.billingMaster= [];
      this.chargeMaster= [];
      this.dispenseMedicine=[];
      this.ChargeSchedule= [];
      this.DispensingDetails=[];
    }
  }
}
</script>

<style scoped lang="scss">
.outp-master{
  border: 1px solid #00a19b;
  border-radius: 10px;
  .outp-item{
    padding: 3px;
    display: flex;
    .outp-left{
      width: 50%;
      border: 1px solid #00a19b;
      border-radius: 3px 0 0 3px;
    }
    .outp-center{
      padding: 0 2px;
    }
    .outp-right{
      width: 50%;
      border: 1px solid #00a19b;
      border-radius: 0 3px 3px 0;
    }

    .outp-title{
      height: 30px;
      border: 1px solid #00a19b;
      background-color: #185F7D;
      color: #FFFFFF;
      font-size: 20px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
    }
    .outp-patient{
      padding-top: 5px;
    }

    ::v-deep.el-table--medium .el-table__cell {
      padding: 3px 0;
    }
    ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
      height: 30px;
      font-size: 14px;
    }
    ::v-deep.el-table th.el-table__cell > .cell {
      padding-left: 2px;
      padding-right: 2px;
    }
    ::v-deep.el-table .cell {
      padding-left: 2px;
      padding-right: 2px;
    }
    ::-webkit-scrollbar {
      width: 11px;
      height: 11px;
    }
    ::-webkit-scrollbar-thumb {
      background-color: #6bcaaf;
      border-radius: 10px;
    }
    ::v-deep.el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
      background-color: #1890FF;
      color: #FFFFFF;
    }
  }
  .outp-form{
    margin-top: 2px;
    margin-left: 10px;
    .form-flex {
      display: flex;
    }

    .date-f {
      padding: 0 5px;
    }
    ::v-deep.el-date-editor.el-input, .el-date-editor.el-input__inner {
      width: 135px;
    }
    ::v-deep.el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
