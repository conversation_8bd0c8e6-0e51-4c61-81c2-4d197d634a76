import request from '@/utils/request2'

export function getTuberculosisStatementList(data) {
  return request({
    url: '/singlePage/tuberculosisStatement/inquire',
    method: 'post',
    data: data
  })
}
export function exportTuberculosisStatement(data) {
  return request({
    url: '/singlePage/tuberculosisStatement/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function getTuberculosisStatementParticulars(examNo) {
  return request({
    url: '/singlePage/tuberculosisStatement/particulars/'+examNo,
    method: 'post',
  })
}
