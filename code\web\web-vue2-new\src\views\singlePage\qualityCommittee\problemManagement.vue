<template>
    <div class="single-master">
        <div class="single-title">问题管理模块</div>
        <div class="single-element">
            <div class="element-master">
                <!-- form -->
                <div class="element-form">
                    <el-form :inline="true" :model="queueForm">
                        <el-form-item label="委员会名称:">
                            <el-select v-model="queueForm.committeeName" filterable clearable placeholder="请选择">
                                <el-option v-for="item in committeeNameDict" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="日期:">
                            <el-date-picker v-model="queueForm.yearAndMonth" type="month" placeholder="请选择日期"
                                value-format="yyyy-MM" format="yyyy-MM" />
                        </el-form-item>
                        <el-form-item>
                            <div class="element-button">
                                <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
                                <el-button type="primary" icon="el-icon-plus" @click="add">新增</el-button>
                                <el-button type="primary" icon="el-icon-delete" @click="del">删除</el-button>
                                <el-button type="primary" icon="el-icon-share" @click="indexOne">首页</el-button>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <!-- table -->
                <div class="element-table">
                    <div class="my-table">
                        <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 117"
                            highlight-current-row @row-click="handleRowClick" @cell-dblclick="doubleSelectionChange">
                            <el-table-column type="index" align="center" />
                            <el-table-column prop="committeename" align="center" label="问题提交情况">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.committeename !== null">
                                        {{ scope.row.yearandmonth }}{{ scope.row.committeename }}问题提交情况
                                    </span>
                                    <span v-else style="color: red;">
                                        无
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <!-- 新增弹框 -->
                <el-dialog title="新增弹框" :visible.sync="dialogVisible" width="60%" :before-close="handleClose">
                    <el-form :inline="true" :model="queueFormAdd" :label-position="labelPosition" :rules="rules"
                        ref="dataForm">
                        <el-form-item label="委员会名称:" prop="committeename">
                            <el-select v-model="queueFormAdd.committeename" filterable clearable placeholder="请选择">
                                <el-option v-for=" item  in  committeeNameDict " :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="日期:" prop="yearandmonth">
                            <el-date-picker v-model="queueFormAdd.yearandmonth" type="month" placeholder="请选择日期"
                                value-format="yyyy-MM" format="yyyy-MM" />
                        </el-form-item>
                        <el-form-item label="问题描述:">
                            <el-input placeholder="请输入问题描述" v-model="queueFormAdd.problemdescription" type="textarea"
                                :autosize="{ minRows: 5, maxRows: 6 }" style="width: 500px;" show-word-limit
                                maxlength="3000(输入框可上下自定义拉长)">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="拟解决措施:">
                            <el-input placeholder="请输入拟解决措施" v-model="queueFormAdd.proposedsolutions" type="textarea"
                                :autosize="{ minRows: 5, maxRows: 6 }" style="width: 500px;" show-word-limit
                                maxlength="3000(输入框可上下自定义拉长)">
                            </el-input>
                        </el-form-item>
                        <el-form-item label="上次提交问题解决进展及评价:">
                            <el-input placeholder="请输入上次提交问题解决进展及评价" v-model="queueFormAdd.resolveprogress" type="textarea"
                                :autosize="{ minRows: 5, maxRows: 6 }" style="width: 500px;" show-word-limit
                                maxlength="3000(输入框可上下自定义拉长)">
                            </el-input>
                        </el-form-item>
                    </el-form>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="primary" @click="save">确 定</el-button>
                    </span>
                </el-dialog>
            </div>
        </div>
    </div>
</template>

<script>
import {
    GetCommitteeNameDict
} from "@/api/singlePage/qualityCommittee/systemManagement";
import {
    GetProblemManagement,
    AddProblemManagement,
    UpdateProblemManagement,
    DetailProblemManagement,
    DelProblemManagement
} from "@/api/singlePage/qualityCommittee/problemManagement";
import env from '@/utils/ApiConfig';
export default {
    name: '',
    props: [],
    data() {
        return {
            envUrl: env.get_file_url(),
            extraFunctionKey: 0,
            labelPosition: 'top', // from表单对其方式
            committeeNameDict: [], // 委员会名称字典数据
            tableDate: [], // 首页数据展示
            queueForm: { // 首页传递参数
                pageNum: 1,
                pageSize: 20,
                id: '',
                committeeName: '',
                yearAndMonth: ''
            },
            dialogVisible: false, // 新增弹框标志
            queueFormAdd: { // 新增弹框传递参数
                id: '',
            },
            delForm: { // 删除传递参数
                id: '',
                committeename: '',
                yearandmonth: ''
            },
            rules: {
                committeename: [
                    { required: true, message: '请输入委员会名称', trigger: 'blur' },
                ],
                yearandmonth: [
                    { required: true, message: '请输入年度', trigger: 'blur' },
                ]
            }
        }
    },

    created() {
        this.getList();
        this.handleResize();
    },

    mounted() {
        window.addEventListener('resize', this.handleResize); // 添加监听器
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize); // 移除监听器
    },

    methods: {
        // 初始化数据
        getList() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            // 问题管理数据查询
            GetProblemManagement(this.queueForm).then(res => {
                this.tableDate = res.data.list;
            }).finally(() => {
                loading.close();
            });
            // 委员会名称字典数据查询
            GetCommitteeNameDict().then(res => {
                this.committeeNameDict = res.data.getCommitteeNameDictRetVueModels;
            }).finally(() => {
                loading.close();
            });
        },

        // 删除按钮操作
        del() {
            if (this.queueForm.id === undefined || this.queueForm.id === '') {
                this.showMessageA("请选择要删除的数据！");
            } else {
                let id = this.delForm.id;
                this.$modal.confirm(
                    '确认要删除问题提交情况为: ' + this.delForm.yearandmonth + this.delForm.committeename +
                    '提交情况的数据吗?')
                    .then(function () {
                        return DelProblemManagement(id);
                    })
                    .then(() => {
                        this.showMessageA("删除数据成功");
                        this.reset();
                        this.getList();
                    })
                    .catch(() => {
                        this.reset();
                        this.showMessageA("删除数据失败");
                    });
            }
        },

        // 新增按钮操作
        add() {
            this.dialogVisible = true;
        },

        // 新增弹框确定按钮操作
        save: function () {
            this.$refs["dataForm"].validate((valid) => {
                if (valid) {
                    if (this.queueFormAdd.id === null || this.queueFormAdd.id === '') {
                        // 新增
                        AddProblemManagement(this.queueFormAdd).then((response) => {
                            if (response.code == 200 && response.data === null) {
                                this.showMessageA("新增数据成功");
                                this.reset();
                                this.getList();
                                this.dialogVisible = false;
                            } else if (response.data === '无') {
                                this.showMessageA("已存在,请勿重复添加!!!");
                            } else {
                                this.showMessageA("新增数据失败,请联系信息科!!!");
                            }
                        }).catch((error) => {
                            // 处理错误
                        });
                    } else {
                        // 修改
                        UpdateProblemManagement(this.queueFormAdd).then((response) => {
                            if (response.code == 200 && response.data === null) {
                                this.showMessageA("修改数据成功");
                                this.reset();
                                this.getList();
                                this.dialogVisible = false;
                            } else if (response.data === '无') {
                                this.showMessageA("已存在,请勿重复添加!!!");
                            } else {
                                this.showMessageA("新增数据失败,请联系信息科!!!");
                            }
                        }).catch((error) => {
                            // 处理错误
                        });
                    }
                }
            });
        },

        // 新增弹框取消提示
        handleClose(done) {
            this.$confirm('确认关闭新增弹框？')
                .then(_ => {
                    done();
                    this.reset();
                    this.getList();
                })
                .catch(_ => { });
        },

        // 重置按钮操作
        reset() {
            this.queueForm = {};
            this.delForm = {};
            this.queueFormAdd.id = '';
            this.queueFormAdd.committeename = '';
            this.queueFormAdd.yearandmonth = '';
            this.queueFormAdd.problemdescription = '';
            this.queueFormAdd.proposedsolutions = '';
            this.queueFormAdd.resolveprogress = '';
        },

        // 单击某一行操作
        handleRowClick(row) {
            this.queueForm.id = row.id;
            this.delForm.id = row.id;
            this.delForm.committeename = row.committeename;
            this.delForm.yearandmonth = row.yearandmonth;
        },

        // 双击某一行操作
        doubleSelectionChange(row) {
            const id = row.id;
            // 根据ID查询详情数据
            DetailProblemManagement(id).then(res => {
                if (res.data.list.length > 0) {
                    this.queueFormAdd = res.data.list[0];
                    ++this.extraFunctionKey;
                    this.dialogVisible = true;
                }
            });
        },

        // 首页按钮操作
        indexOne() {
            this.$router.push('/singlePage/qualityCommittee/transfer')
        },

        // 提示信息封装
        showMessageA(message) {
            const h = this.$createElement;
            this.$notify({
                title: '提示信息！',
                message: h('i', { style: 'color: teal' }, message)
            });
        },

        // 自定义高度变化更新高度
        handleResize() {
            this.tableHeight = window.innerHeight; // 更新高度数据
        },
    }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.my-table {
    ::v-deep.el-table--medium .el-table__cell {
        padding: 0;
    }

    ::v-deep.el-table .el-table__header-wrapper th,
    .el-table .el-table__fixed-header-wrapper th {
        word-break: break-word;
        background-color: #4f617238;
        color: #303133;
        height: 30px;
        font-size: 10px;
    }

    ::v-deep.el-table th.el-table__cell>.cell {
        padding: 0;
    }

    ::v-deep.el-table--border .el-table__cell:first-child .cell {
        padding: 0;
    }

    ::v-deep.el-button+.el-button {
        margin-left: 2px;
    }

    ::v-deep.el-table .cell {
        padding: 1px;
    }

    // 滚动条的滑块
    ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: rgb(13, 192, 132);
        border-radius: 1px;
    }
}

.scroll-container {
    height: 550px;
    /* 设置容器的高度 */
}
</style>