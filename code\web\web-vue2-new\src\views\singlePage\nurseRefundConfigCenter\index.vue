<template>
  <div class="single-master" v-if="bodyStatus" :style="'height:' + tableHeight + 'px'">
    <div class="single-title my-text">护士站退费权限配置中心</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="名称:">
              <el-input v-model="queueForm.name" clearable placeholder="输入关键字查询"></el-input>
            </el-form-item>
            <el-form-item label="工号:">
              <el-input v-model="queueForm.empNo" clearable placeholder="输入工号查询"></el-input>
            </el-form-item>
            <el-form-item>
              <div class="element-button">
                <el-button type="primary" icon="el-icon-search" @click="getGrantColumnList">查询</el-button>
                <el-button type="primary" icon="el-icon-refresh-left" @click="resetClick">重置</el-button>
<!--                <el-button type="primary" icon="el-icon-s-tools" @click="configCentre">配置中心</el-button>-->
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="element-table">
          <el-table :data="dictTableData" style="width: 100%" border :height="(tableHeight-130)">
            <el-table-column align="center" label="操作" width="150">
              <template slot-scope="scope">
                <el-button type="text" v-if="scope.row.status ==='0'" icon="el-icon-close" @click="fastAuthorization(scope.row,'1')">停用</el-button>
                <el-button type="text" v-if="scope.row.status !=='0'" icon="el-icon-check" @click="fastAuthorization(scope.row,'0')">授权</el-button>
              </template>
            </el-table-column>
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column prop="empno" align="center" label="工号"></el-table-column>
            <el-table-column prop="name" align="center" label="姓名"></el-table-column>
            <el-table-column prop="title" align="center" label="职称"></el-table-column>
            <el-table-column prop="status" align="center" label="状态">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status && scope.row.status === '0'">已授权</el-tag>
                <el-tag v-else type="danger">未授权</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <config-center ref="configCenterRefs" :table-height="tableHeight"></config-center>
      </div>
    </div>
  </div>
</template>

<script>
import {InitializationCheck,GetGrantColumnList,FastAuthorization} from "@/api/singlePage/nurseRefundConfigCenter"
import ConfigCenter from './module/configCenter.vue'
export default {
  name: 'index',
  props: [],
  components: { ConfigCenter },
  data() {
    return {
      bodyStatus: false,
      tableHeight: '',
      queueForm: {
        name: '',
        empNo: '',
        deptCode: '',
      },
      dictTableData: [],
      staffData: {},
    }
  },
  created() {
    let emp_no = this.$route.query && this.$route.query.emp_no;
    this.createInitializationCheck(emp_no);
    this.handleResize()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    configCentre(){
      this.$refs.configCenterRefs.configCenterCreated(true);
    },
    fastAuthorization(row,type){
      FastAuthorization({
        type: type,
        empNo: row.empno,
        name: row.name,
        inputCode: row.inputcode
      }).then(res => {
        this.$message.success(res.message);
        this.getGrantColumnList();
      })
    },
    getGrantColumnList(){
      this.dictTableData = [];
      if (this.queueForm.name){
        this.queueForm.name = this.queueForm.name.toLocaleUpperCase()
      }
      this.queueForm.deptCode = this.staffData.deptCode;
      GetGrantColumnList(this.queueForm).then(res => {
        if (res.code === 200){
          this.dictTableData = res.data;
        }
      })
    },
    createInitializationCheck(empNo){
      const loading = this.$loading({
        lock: true,
        text: '页面正在努力初始化中!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      InitializationCheck(empNo).then(res => {
        if (res.code === 200){
          this.bodyStatus = true;
          this.staffData = res.data;
          this.getGrantColumnList();
        }
      }).finally(() => {
        loading.close();
      })
    },
    resetClick() {
      this.queueForm = {
        name: '',
        empNo: ''
      }
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.my-text {
  letter-spacing: 0.5em;
}
</style>
