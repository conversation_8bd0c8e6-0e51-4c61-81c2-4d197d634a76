import request from '@/utils/request'

export function GetEmergencyDeptLabData(data) {
  return request({
    url: '/LabTestEmergencyDept/GetEmergencyDeptLabData',
    method: 'post',
    data: data
  })
}

export function GetLabReport(data) {
  return request({
    url: '/LabTestEmergencyDept/GetLabReport',
    method: 'post',
    data: data
  })
}

export function GetMarkUpList(data) {
  return request({
    url: '/LabTestEmergencyDept/GetMarkUpList',
    method: 'post',
    data: data
  })
}

export function ReportResultConfirm(data) {
  return request({
    url: '/LabTestEmergencyDept/ReportResultConfirm',
    method: 'post',
    data: data
  })
}

export function GetLabReportResult(id) {
  return request({
    url: '/LabTestEmergencyDept/GetLabReportResult?id=' + id,
    method: 'get',
  })
}

export function GetEmergencyDeptStaffDict(id) {
  return request({
    url: '/LabTestEmergencyDept/GetEmergencyDeptStaffDict',
    method: 'get',
  })
}
