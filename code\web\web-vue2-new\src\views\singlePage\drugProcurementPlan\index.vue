<template>
  <div class="single-master" v-if="bodyStatus">
    <div class="single-title">药品采购计划</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <div style="display: flex;">
              <div style="width: 25%;">
                <el-form-item style="padding: 0 10px;">
                  <el-radio-group v-model="queueForm.type">
                    <el-radio :label="'0'">全部</el-radio>
                    <el-radio :label="'1'">低于下线</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="子药房:">
                  <el-select v-model="queueForm.drugSourceArray" @change="queueMasterClick" multiple placeholder="请选择">
                    <el-option
                      v-for="item in pharmacyItem"
                      :key="item.VALUE"
                      :label="item.LABEL"
                      :value="item.VALUE"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <div class="element-button">
                    <el-button type="primary" icon="el-icon-check">加入</el-button>
                    <el-button type="primary" icon="el-icon-finished">全部加入</el-button>
                    <el-button type="primary" icon="el-icon-refresh">刷新</el-button>
                    <el-button type="primary" icon="el-icon-download">导出</el-button>
                  </div>
                </el-form-item>
                <el-form-item label="药品查找:">
                  <el-select v-model="queueForm.drugCode" :filter-method="remoteMethodTwo" filterable
                             placeholder="请输入关键字进行检索" clearable  style="margin-left: -2px;">
                    <el-option
                      v-for="(item,index) in drugNew"
                      :key="index"
                      :label="item.LABEL"
                      :value="item.VALUE"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </div>
            <div style="margin-left: 15%">
              <el-form-item label="开始时间:">
                <el-date-picker
                  v-model="queueForm.beginDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="结束时间:">
                <el-date-picker
                  v-model="queueForm.endDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item>
                <div class="element-button">
                  <el-button type="primary">按药库出库数生成采购数</el-button>
                  <el-button type="primary">按药房消耗量生成采购数</el-button>
                </div>
              </el-form-item>
              <el-form-item>
                <el-radio-group v-model="queueForm.fillIn">
                  <el-radio :label="'1'">填计划数量</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>

          </el-form>
        </div>
        <div style="display: flex">
          <procurement-plan-left :table-height="tableHeight" :queue-form="queueForm" :body-width="bodyWidth"
                                 ref="procurementPlanLeftRefs"
          ></procurement-plan-left>
          <procurement-plan-right :table-height="tableHeight" :queue-form="queueForm" :body-width="bodyWidth"
                                  ref="procurementPlanRightRefs"
          ></procurement-plan-right>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ProcurementPlanLeft from './module/procurementPlanLeft.vue'
import ProcurementPlanRight from './module/procurementPlanRight.vue'
import { getDrugProcurementPlanPharmacyItemApi } from '@/api/singlePage/drugProcurementPlan'

export default {
  name: 'index',
  props: [],
  components: { ProcurementPlanRight, ProcurementPlanLeft },
  data() {
    return {
      queueForm: {
        beginDate: this.formatDateMonth(new Date()),
        endDate: this.formatDate(new Date()),
        drugSourceArray: [],
        drugSource: '',
        drugCode: '',
        fillIn: '0',
      },
      drugItem: [],
      drugNew:[],
      pharmacyItem: [],
      tableHeight: undefined,
      bodyWidth: undefined,
      bodyStatus: true
    }
  },
  created() {
    this.getDrugProcurementPlanPharmacyItem()
    this.handleResize()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    queueMasterClick() {
      this.queueForm.drugSource = this.queueForm.drugSourceArray.join(',')
      this.$refs.procurementPlanLeftRefs.getDrugProcurementPlanTableMaster()
    },
    getDrugProcurementPlanPharmacyItem() {
      getDrugProcurementPlanPharmacyItemApi().then(res => {
        this.pharmacyItem = res.data.dept
        this.drugItem = res.data.deug
      })
    },
    remoteMethodTwo(query){
      this.queueForm.drugCode = query;
      if (query !== "") {
        this.drugNew = this.drugItem.filter((item) => {
          // 这里是用的value选项筛选，默认是label
          if (item.CODE.toUpperCase().indexOf(query.toUpperCase()) > -1 ||
            item.LABEL.toLowerCase().indexOf(query.toLowerCase()) > -1){
            return true;
          }
        });
      } else {
        this.drugNew = [];
      }
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
      this.bodyWidth = 'width:' + (window.innerWidth / 2) + 'px'
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    formatDateMonth(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      return `${year}-${month}-01`
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
</style>
