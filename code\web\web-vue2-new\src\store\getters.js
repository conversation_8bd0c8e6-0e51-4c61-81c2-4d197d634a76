import log from '../views/monitor/job/log.vue'

const getters = {
  sidebar: state => state.app.sidebar,
  size: state => state.app.size,
  device: state => state.app.device,
  dict: state => state.dict.dict,
  visitedViews: state => state.tagsView.visitedViews,
  cachedViews: state => state.tagsView.cachedViews,
  loginType: state => state.user.loginType,
  token: state => state.user.token,
  accessToken: state => state.user.accessToken,
  accessId: state => state.user.accessId,
  avatar: state => state.user.avatar,
  name: state => state.user.name,
  introduction: state => state.user.introduction,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  permission_routes: state => state.permission.routes,
  topbarRouters:state => state.permission.topbarRouters,
  defaultRoutes:state => state.permission.defaultRoutes,
  sidebarRouters:state => state.permission.sidebarRouters,
  empNo: state => state.user.id,
  deptCode: state => state.user.deptCode,
  examAuthDeptCodes: state => state.user.authDeptCodes,
  patient: state => state.user.patient,
  optForTable: state => state.user.optForTable,
  rowTable: state => state.user.rowTable,
  reservationTable: state => state.user.reservationTable,
  reservationRowTable: state => state.user.reservationRowTable,
  examButtonStatus: state => state.user.examButtonStatus,
  exportDictExam: state => state.user.exportDictExam,
  appointmentExamNos: state => state.user.appointmentExamNos,
  appointmentDeptDict:state => state.user.appointmentDeptDict,

}
export default getters
