<template>
  <div class="send-master" :style="'height:' + tableHeight + 'px'">
    <div class="send-bottom">
      <div class="send-title"><div class="send-text">发件人：</div><div style="color: #00afff">{{master.senderName}}</div></div>
      <div class="send-title"><div class="send-text">收件人：</div><div class="input-one"><el-input v-model="userStr" @focus="userClick" placeholder="请选择收件人"></el-input></div></div>
      <div class="send-title"><div class="send-text">标题：</div><div class="input-two"><el-input v-model="master.senderTitle" placeholder="请输入命名标题"></el-input></div></div>
      <div class="send-title"><div class="send-text">备注：</div><div class="input-two"><el-input v-model="master.memo"></el-input></div></div>
      <div style="display: flex;justify-content: center;">
        <el-button type="primary" v-if="buttonStatus" style="width: 50%;margin-top: 10px;margin-bottom: 20px;" @click="send">发送</el-button>
        <el-button type="primary" v-if="!buttonStatus" style="width: 50%;margin-top: 10px;" disabled>发送</el-button>
      </div>
    </div>
    <div class="send-top">
      <el-upload class="upload-demo" :multiple="multiple" action="#" :on-preview="handlePreview"
                 :on-remove="handleRemove" :before-remove="beforeRemove" :before-upload="beforeUpload"
                 :http-request="UploadFile" :on-exceed="handleExceed"
                 :file-list="fileList" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text"><em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip"></div>
      </el-upload>
    </div>
    <div class="send-dialog">
      <el-dialog title="人员挑选" :visible.sync="userStatus" width="80%"
                 v-loading="userLoading"
                 element-loading-text="正在加载人员中"
                 element-loading-spinner="el-icon-loading"
                 element-loading-background="rgba(0, 0, 0, 0.8)">
        <div class="dialog-master" :style="'height:' + (tableHeight - 150) + 'px;'">
          <div class="dialog-left">
            <div class="dialog-title">人员信息</div>
            <el-input placeholder="输入关键字进行过滤" v-model="filterText">
            </el-input>
            <el-scrollbar style="height: 90%;overflow-x: hidden;">
              <el-tree :highlight-current="true" class="filter-tree" :data="userList" :props="defaultProps"
                       node-key="label" default-expand-all :filter-node-method="filterNode" ref="tree">
                <span class="custom-tree-node" slot-scope="{ node, data }">
                  <span>{{data.label}}</span>
                  <span v-if="node.isLeaf">
                    <span>
                      <el-button type="text" size="mini" style="margin-left: 10%" icon="el-icon-plus"
                                 @click="insertButton(data)">
                        添加
                      </el-button>
                    </span>
                  </span>
                </span>
              </el-tree>
            </el-scrollbar>
          </div>
          <div class="dialog-right">
            <div class="dialog-title">已选人员</div>
            <el-scrollbar style="height: 94.7%;overflow-x: hidden;margin-top: 2px;">
              <div style="height: 30px;padding: 0 3px;" v-for="(item,index) in userData" :key="index">
                <div style="display: flex;justify-content: space-between;padding: 2px 5px;border: 1px solid #DFECFD">
                  <div style="display: flex;align-items: center">{{index + 1}}</div>
                  <div style="display: flex;align-items: center">{{item.recipientName}}</div>
                  <div><el-button type="text" size="mini" style="margin-left: 10%" icon="el-icon-minus"
                                  @click="deleteButton(item)">
                    删除
                  </el-button></div>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
        <div style="display: flex;justify-content: center;">
          <el-button type="primary" style="width: 50%;margin-top: 10px;letter-spacing: 1em;" @click="accomplish">完成选择</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { md5 } from '@/utils/md5'
import { uploadFile2,userTreeAll,sendApi } from '@/api/singlePage/exchangePlatform'

export default {
  name: 'send',
  props: ['tableHeight','master'],
  components: {},
  data() {
    return {
      fileList: [],
      fileNews: [],
      fileModelList: [],
      multiple: false,
      buttonStatus: true,
      userStr: '',
      userData: [],
      userStatus: false,
      userLoading: true,
      userList:[],
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
    }
  },
  created() {
  },
  mounted() {
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  methods: {
    send(){
      if (!this.userStr){
        return this.$msgbox.alert(
          '<div style="font-size: 22px !important;color: red; text-align: center;font-weight: 600;margin-bottom: 10px;">' +
          '请选择发件人' + '</div>' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
      }
      if (!this.master.senderTitle){
        return this.$msgbox.alert(
          '<div style="font-size: 22px !important;color: red; text-align: center;font-weight: 600;margin-bottom: 10px;">' +
          '请输入发送标题' + '</div>' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
      }
      if (this.fileModelList.length === 0){
        return this.$msgbox.alert(
          '<div style="font-size: 22px !important;color: red; text-align: center;font-weight: 600;margin-bottom: 10px;">' +
          '请选择要发送的文件' + '</div>' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
      }
      let data = {
        master: this.master,
        file: this.fileModelList,
        send: this.userData,
      }
      const loading = this.$loading({
        lock: true,
        text: '休息一下,文件正在努力发送中,请勿操作!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      sendApi(data).then(res => {
        if (res.code === 200){
          this.$msgbox.alert(
            '<div style="font-size: 22px !important;color: red; text-align: center;font-weight: 600;margin-bottom: 10px;">' +
            '发送成功' + '</div><div style="font-size: 20px !important; text-align: center;font-weight: 600;margin-bottom: 10px;">如果要二次发送消息请刷新页面</div>' + '</div>',
            '系统提示',
            {
              confirmButtonText: '确定',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }).then(() => {
          })
          this.buttonStatus = false;
          this.$message.success("发送成功")
        }
      }).finally(() => {
        loading.close();
      })
    },
    accomplish(){
      this.userStr = this.userData.map(x => x.recipientName).join(",");
      this.userStatus = false;
    },
    deleteButton(row){
      let data = [];
      this.userData.forEach(x => {
        if (x.recipientNo !== row.recipientNo){
          data.push(x);
        }
      })
      this.userData = data;
    },
    insertButton(row){
      let status = false;
      this.userData.forEach(x => {
        if (x.recipientNo === row.id){
          status = true;
        }
      })
      if (!status){
        this.userData.push({
          recipientNo: row.id,
          recipientName: row.userName,
          informPhone: row.phone,
        })
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1 || data.label.toUpperCase().indexOf(value.toUpperCase()) !== -1;
    },
    userClick(){
      this.userLoading = true;
      this.userStatus = true;
      userTreeAll().then(res => {
        this.userList = res.data;
        this.filterText = '';
        this.userLoading = false;
      })
    },
    handleExceed(files, fileList) {
    },
    async UploadFile(file) {
      let fileItem = file.file
      const loading = this.$loading({
        lock: true,
        text: '休息一下,文件正在努力上传服务器中!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      const param = new FormData()
      param.append('file', fileItem)
      md5(fileItem).then(res => {
        //计算文件是否重复
        let Flag = this.fileModelList && this.fileModelList.some(item => {
          if (item.fileMd5 === res) {
            this.$message.error('文件重复')
            return true;
          }
        })
        if (!Flag) {
          uploadFile2(param, res).then(response => {
            this.fileList.push(fileItem)
            let data = response.data
            this.fileModelList.push({
              fileName: data.fileName,
              fileType: data.fileType,
              fileSize: data.fileSize,
              filePath: data.fullPath,
              fileMd5: data.md5
            })
            console.log(this.fileModelList)
          }).finally(() => {
            loading.close()
          })
        }
      }).finally(t => {
        loading.close()
      })

    },
    beforeUpload(file) {
    },
    beforeRemove(file, fileList) {
      return this.$msgbox.alert(
        '<div style="font-size: 22px !important;color: red; text-align: center;font-weight: 600;margin-bottom: 10px;">' +
        '确定要移除    ' + file.name + '</div>' + '</div>',
        '系统提示',
        {
          confirmButtonText: '确定',
          type: 'warning',
          dangerouslyUseHTMLString: true
        }).then(() => {
      })
    },
    // 文件移除
    handleRemove(file, fileList) {
      fileList.filter(item => {
        return item.uid !== file.uid
      })
    },
    handlePreview(file) {
    }
  }
}
</script>

<style scoped lang="scss">
.send-master {
  display: flex;
  flex-direction: column;

  .send-top {
    height: 70%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .file-upload {
      display: flex;
      justify-content: center;

      ::v-deep.el-upload-dragger {
        width: 600px;
        height: 180px;
      }
    }
  }

  .send-bottom {
    width: 100%;
    height: 30%;
    border-bottom: 1px solid #DFECFD;
    display: flex;
    flex-direction: column;
    padding: 20px;
    .send-title{
      padding: 5px 0;
      display: flex;
      .send-text{
        display: flex;
        align-items: center;
      }
      .input-one{
        width: 95%;
      }
      .input-two{
        width: 96%;
      }
      @media screen and (max-width: 1600px){
        .input-one{
          width: 93%;
        }
        .input-two{
          width: 94%;
        }
      }
      @media screen and (max-width: 1200px){
        .input-one{
          width: 90%;
        }
        .input-two{
          width: 91%;
        }
      }
    }
  }

  @media screen and (max-height: 900px){
    .send-top{height: 68%;}
    .send-bottom{height: 32%;}
  }
  @media screen and (max-height: 800px){
    .send-top{height: 58%;margin-top: 2%}
    .send-bottom{height: 40%;}
  }
  @media screen and (max-height: 700px){
    .send-top{height: 57%;margin-top: 3%}
    .send-bottom{height: 40%;}
  }
  @media screen and (max-height: 600px){
    .send-top{height: 51%;margin-top: 4%}
    .send-bottom{height: 45%;}
  }
  @media screen and (max-height: 500px){
    .send-top{height: 45%;margin-top: 5%}
    .send-bottom{height: 50%;}
  }

  .send-dialog{
    .dialog-master{
      display: flex;
      width: 100%;
      border: 1px solid #DFECFD;
      .dialog-title{
        font-size: 20px;
        color: #2D3849;
        background-color: #DFECFD;
        text-align: center;
        letter-spacing: 0.6em;
      }
      .dialog-left{
        width: 70%;
        border-right: 1px solid #DFECFD;
      }
      .dialog-right{
        width: 30%;
      }
    }
    ::v-deep.el-dialog__header {
      background: #DFECFD !important;
    }

    ::v-deep.el-dialog__headerbtn .el-dialog__close {
      color: #2D3849 !important;
    }

    ::v-deep.el-dialog__title {
      color: #2D3849 !important;
      letter-spacing: 1em;
    }
  }
}
</style>
