<template>
  <div class="system-home" :style="homeStyle.itemWidth">
    <div class="system-master">
      <div style="width: 12%">
        <navigation :style="homeStyle.itemHeight"  @menu-item="menuClickMonitor"></navigation>
      </div>
      <div style="width: 88%">
        <div v-if="menuIndex === '1'">
          <user-dict-module></user-dict-module>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Navigation from './system/navigation.vue'
import UserDictModule from './system/UserDictModule.vue'

export default {
  name: 'systemIndex',
  props: ['homeStyle'],
  components: { UserDictModule, Navigation },
  data() {
    return {
      menuIndex: ''
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    menuClickMonitor(data) {
      this.menuIndex = data
      console.log(this.menuIndex)
    }
  }
}
</script>

<style scoped lang="scss">
.system-home {
  padding: 0px;
  border: 0;

  .system-master{
    display: flex;
  }
}
</style>
