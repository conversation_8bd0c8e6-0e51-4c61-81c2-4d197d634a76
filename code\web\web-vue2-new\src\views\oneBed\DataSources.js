import man from "@/assets/images/man.png";
import woMan from "@/assets/images/woman.png";
import VIP from "@/assets/images/VIP2.png";
export const bedData = [
  {
    isEmpty: false,
    isVip: true,
    VIPSrc: VIP,
    bedNo: '01',
    patientName: '张三丰之宝',
    gender: '女',
    age: 2,
    // imageSrc: this.manSrc,
    imageSrc: woMan,
    details: [
      '患者ID：123456789',
      '入院时间：2023-05-01',
      '病情：普通',
      '床位类型：普通床位',
      '病区：1-门-3病区',
      '科室：急诊外科',
      '组别：王伟主诊组',
      '医生：小红'
    ]
  },
  {
    isEmpty: false,
    isVip: false,
    VIPSrc: VIP,
    bedNo: '02',
    patientName: '王五',
    gender: '男',
    levelOfCare: 'blue',
    age: 20,
    imageSrc: man,
    details: [
      '患者ID：123478978',
      '入院时间：2014-05-01',
      '病情：普通',
      '床位类型：VIP病房',
      '病区：1-3病区',
      '科室：急诊外科',
      '组别：王伟主诊组',
      '医生：小花'
    ]
  },
  {
    isEmpty: false,
    isVip: true,
    VIPSrc: VIP,
    bedNo: '03',
    patientName: '赵六',
    levelOfCare: 'red',
    gender: '女',
    age: 20,
    imageSrc: woMan,
    details: [
      '患者ID：123478978',
      '入院时间：2014-05-01',
      '病情：普通',
      '床位类型：VIP病房',
      '病区：1-3病区',
      '科室：急诊外科',
      '组别：王伟主诊组',
      '医生：小花'
    ]
  },
  {
    isEmpty: false,
    isVip: false,
    VIPSrc: VIP,
    bedNo: '04',
    patientName: '王五',
    gender: '男',
    levelOfCare: 'white',
    fontColor: 'gray',
    border: "1px solid black",
    age: 20,
    imageSrc: man,
    details: [
      '患者ID：123478978',
      '入院时间：2014-05-01',
      '病情：普通',
      '床位类型：VIP病房',
      '病区：1-3病区',
      '科室：急诊外科',
      '组别：王伟主诊组',
      '医生：小花'
    ]
  },
  {
    isEmpty: false,
    isVip: false,
    VIPSrc: VIP,
    bedNo: '05',
    patientName: '王五',
    levelOfCare: "pink",
    gender: '男',
    age: 20,
    imageSrc: man,
    details: [
      '患者ID：123478978',
      '入院时间：2014-05-01',
      '病情：普通',
      '床位类型：VIP病房',
      '病区：1-3病区',
      '科室：急诊外科',
      '组别：王伟主诊组',
      '医生：小花'
    ]
  },
  {
    isEmpty: true,
    isVip: true,
    VIPSrc: VIP,
    bedNo: '06',
    patientName: '王五',
    levelOfCare: "gray",
    gender: '男',
    age: 20,
    imageSrc: man,
    details: [
      '患者ID：123478978',
      '入院时间：2014-05-01',
      '病情：普通',
      '床位类型：VIP病房',
      '病区：1-3病区',
      '科室：急诊外科',
      '组别：王伟主诊组',
      '医生：小花'
    ]
  },

  {
    isEmpty: true,
    bedNo: '06',
    patientName: '王五',
    levelOfCare: "gray",
    gender: '男',
    age: 20,
    imageSrc: man,
    details: [
      '患者ID：123478978',
      '入院时间：2014-05-01',
      '病情：普通',
      '床位类型：VIP病房',
      '病区：1-3病区',
      '科室：急诊外科',
      '组别：王伟主诊组',
      '医生：小花'
    ]
  },

  {
    isEmpty: true,
    bedNo: '06',
    patientName: '王五',
    levelOfCare: "gray",
    gender: '男',
    age: 20,
    imageSrc: man,
    details: [
      '患者ID：123478978',
      '入院时间：2014-05-01',
      '病情：普通',
      '床位类型：VIP病房',
      '病区：1-3病区',
      '科室：急诊外科',
      '组别：王伟主诊组',
      '医生：小花'
    ]
  },

  {
    isEmpty: true,
    bedNo: '06',
    patientName: '王五',
    levelOfCare: "gray",
    gender: '男',
    age: 20,
    imageSrc: man,
    details: [
      '患者ID：123478978',
      '入院时间：2014-05-01',
      '病情：普通',
      '床位类型：VIP病房',
      '病区：1-3病区',
      '科室：急诊外科',
      '组别：王伟主诊组',
      '医生：小花'
    ]
  },
  {
    isEmpty: true,
    bedNo: '06',
    patientName: '王五',
    levelOfCare: "gray",
    gender: '男',
    age: 20,
    imageSrc: man,
    details: [
      '患者ID：123478978',
      '入院时间：2014-05-01',
      '病情：普通',
      '床位类型：VIP病房',
      '病区：1-3病区',
      '科室：急诊外科',
      '组别：王伟主诊组',
      '医生：小花'
    ]
  },
]
export const careLevels =  [
  // {name: '特级护理', color: 'red', class: 'circle_super'},
  // {name: '一级护理', color: 'pink', class: 'circle_first'},
  // {name: '二级护理', color: 'blue', class: 'circle_second'},
  // {name: '三级护理', color: 'white', class: 'circle_third'}, // 灰色表示三级护理
  // {name: '空床', color: 'gray', class: 'circle_empty'}
  {name: '病危', color: 'red', class: 'circle_super'},
  {name: '病重', color: 'pink', class: 'circle_first'},
  {name: '普通', color: 'blue', class: 'circle_second'},
  // {name: '三级护理', color: 'white', class: 'circle_third'}, // 灰色表示三级护理
  {name: '空床', color: 'gray', class: 'circle_empty'}
]
export const options =  [{
  value: '普通',
  label: '普通'
}, {
  value: '病重',
  label: '病重'
}, {
  value: '病危',
  label: '病危'
}, {
  value: '空床',
  label: '空床'
},
  {
    value: '180',
    label: '180'
  },
  {
    value: '280',
    label: '280'
  },
  {
    value: '380',
    label: '380'
  },
  {
    value: '580',
    label: '580'
  },]
