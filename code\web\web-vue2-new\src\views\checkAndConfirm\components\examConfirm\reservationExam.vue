<template>
  <div class="reservation-exam-home">
    <el-dialog
      title="检查集中预约平台"
      :visible.sync="reservationStatus"
      :width="dialogWidth"
    >
      <div class="exam-dialog-master" :style="dialogHeight">
        <div class="dialog-left">
          <resevation-table :key="tableKey" :style-data="myStyle" :appointment-type="appointmentType"
                            ref="reservationTableRef"
                            @row-table="clickTableMonitor"
                            @emit-create="createTableMonitor"
                            @table-save="tableSaveMonitor"></resevation-table>
        </div>
        <div class="dialog-right">
          <div class="right-title" :style="myStyle.left.user.title">
            预约操作
          </div>
          <div>
            <case-table ref="caseTableRef" :style-data="myStyle" :key="tableKeyTwo" ></case-table>
            <reservation-time ref="reservationTimeRef" :style-data="myStyle" :key="tableKeyThree"
            @save-success="reservationSuccess" @queue-select="callQueueSelect"></reservation-time>
          </div>
        </div>
      </div>
      <div>
        <call-queue-message ref="callQueueMessageRef"></call-queue-message>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ResevationTable from './reservation/resevationTable.vue'
import CaseTable from './reservation/caseTable.vue'
import ReservationTime from './reservation/reservationTime.vue'
import CallQueueMessage from './reservation/callQueueMessage.vue'

export default {
  name: 'reservationExam',
  props: [],
  components: { CallQueueMessage, ReservationTime, CaseTable, ResevationTable },
  data() {
    return {
      reservationStatus: false,
      tableKey: 0,
      tableKeyTwo: 0,
      tableKeyThree: 0,
      dialogWidth: '80%',
      dialogHeight: 'height: 700px;',
      appointmentType: 0,
      myStyle: {
        home: {
          minHeight: "height: 942px;",
        },
        left: {
          scrollbar: 'height: 530px;',
          top: "height: 50px;",
          user: {
            height: "height:200px;",
            info: "height: 150px;",
            title: "height: 35px;font-size: 24px;",
            img: 80,
            size: "font-size: 14px;",
            exam: "height: 50px;font-size: 14px;",
          },
          case: {
            height: "height: 630px;",
            title: "font-size: 20px;",
            span: "font-size:12px;",
            size: 4,
            table: {
              height: "height: 170px;",
              size1: "font-size:22px;",
              szie2: "",
              width1: "width:40%",
              width2: "width:50%",
              one: true,
              two: false,
              three: false,
            },
          },
        },
        right: {
          top: "height: 50px;font-size: 28px;",
          ontSource: "width: 40%",
          twoSource: "width: 40%",
          ontSourceBut: false,
          table: 120,
          img: "width: 20px;margin-top: 5px",
          time: {
            height: "height: 600px",
            select: "font-size: 14px;padding: 10px 40px;",
            width: "width: 10%",
          },
        },
        dialog: {
          ont: {
            width: "70%",
            table: {
              table1: "75",
              table2: "100",
              table3: "80",
              table4: "90",
              table5: "120",
              table6: "100",
              table7: "110",
              table8: "150",
              table9: "78",
            },
          },
          two: {
            dialogWidth: "70%",
            form: {
              width1: "width: 180px;",
              width2: "width: 180px;",
              width3: "width: 180px;",
              width4: "width: 180px;",
            },
            table: {
              height: "650",
              width1: "",
              width2: "80",
              width3: "120",
              width4: "95",
              width5: "100",
              width6: "100",
              width7: "110",
              width8: "110",
            },
          },
          three: {
            dialogWidth: "50%",
          },
        },
        button:{
          one: true,
          two: false,
          three: false,
        }
      },
    }
  },
  created() {
  },
  mounted() {
    this.$nextTick(() => {
      const bodyStyle = document.body.style, // 获取body节点样式
        htmlStyle = document.getElementsByTagName("html")[0].style, // 获取html节点样式
        docEl = document.documentElement,
        docBody = document.body,
        winWidth = docEl.clientWidth || docBody.clientWidth, // 获取浏览器的宽度
        WinHeight = docEl.clientHeight || docBody.clientHeight; // 获取浏览器的高
      this.bodyHeight = "height:" + WinHeight + "px";
      bodyStyle.minWidth = "1014px";
      bodyStyle.minHeight = "768px";
      htmlStyle.minHeight = "768px";
      htmlStyle.minWidth = "1014px";
      if (winWidth <= 1200) {
        this.dialogWidth = "95%";
        this.myStyle = {
          home: {
            minHeight: "height: 753px;",
          },
          left: {
            scrollbar: 'height: 518px;',
            top: "height: 40px;background-size: 100% auto;",
            user: {
              height: "height:130px;",
              info: "height: 100px;",
              title: "height: 25px;font-size: 18px;",
              img: 45,
              size: "font-size: 12px;",
              exam: "height: 29px;font-size: 12px;",
            },
            case: {
              height: "height: 545px;",
              title: "font-size: 15px;",
              span: "font-size:10px;",
              size: 3,
              table: {
                height: "height: 150px;",
                size1: "font-size:16px;",
                size2: "font-size: 12px;",
                width1: "width:40%",
                width2: "width:50%",
                one: false,
                two: false,
                three: true,
              },
            },
          },
          right: {
            top: "height: 40px;font-size: 24px;",
            twoSource: "width: 60%",
            table: 100,
            time: {
              width: "width: 15%;font-size: 14px;height: 105px;",
            },
          },
          dialog: {
            ont: {
              width: "90%",
              table: {
                table1: "70",
                table2: "70",
                table3: "70",
                table4: "70",
                table5: "90",
                table6: "80",
                table7: "80",
                table8: "100",
                table9: "78",
              },
            },
            two: {
              dialogWidth: "90%",
              form: {
                width1: "width: 160px;",
                width2: "width: 100px;",
                width3: "width: 110px;",
                width4: "width: 110px;",
              },
              table: {
                height: "450",
                width1: "",
                width2: "75",
                width3: "80",
                width4: "70",
                width5: "90",
                width6: "100",
                width7: "75",
                width8: "75",
              },
            },
            three: {
              dialogWidth: "80%",
            },
          },
          button:{
            one: false,
            two: false,
            three: true,
          }
        };
      } else if (winWidth <= 1400) {
        this.dialogWidth = "95%";
        this.dialogHeight = 'height: 800px;',
        this.myStyle = {
          home: {
            minHeight: "height: 1010px;",
          },
          left: {
            scrollbar: 'height: 508px;',
            top: "height: 40px;background-size: 100% auto;",
            user: {
              height: "height:200px;",
              info: "height: 150px;",
              title: "margin-top: 27px;height: 35px;font-size: 22px;",
              img: 55,
              size: "font-size: 14px;",
              exam: "height: 50px;font-size: 14px;",
            },
            case: {
              height: "height: 705px;",
              title: "font-size: 20px;",
              span: "font-size:12px;",
              size: 4,
              table: {
                height: "height: 190px;",
                size1: "font-size:20px;",
                szie2: "font-size: 13px",
                width1: "width:40%",
                width2: "width:50%",
                one: false,
                two: true,
                three: false,
              },
            },
          },
          right: {
            top: "height: 50px;font-size: 26px;",
            twoSource: "width: 57%",
            table: 120,
            time: {
              width: "width: 16.5%",
            },
          },
          dialog: {
            ont: {
              width: "80%",
              table: {
                table1: "75",
                table2: "85",
                table3: "70",
                table4: "90",
                table5: "90",
                table6: "90",
                table7: "100",
                table8: "120",
                table9: "78",
              },
            },
            two: {
              dialogWidth: "80%",
              form: {
                width1: "width: 160px;",
                width2: "width: 130px;",
                width3: "width: 140px;",
                width4: "width: 140px;",
              },
              table: {
                height: "600",
                width2: "75",
                width3: "90",
                width4: "80",
                width5: "95",
                width6: "100",
                width7: "90",
                width8: "90",
              },
            },
            three: {
              dialogWidth: "60%",
            },
          },
          button:{
            one: false,
            two: true,
            three: false,
          }
        };
      } else {
        this.dialogWidth = "85%";
        this.dialogHeight = 'height: 775px;',
        this.myStyle = {
          home: {
            minHeight: "height: 942px;",
          },
          left: {
            scrollbar: 'height: 508px;',
            top: "height: 50px;",
            user: {
              height: "height:200px;",
              info: "height: 150px;",
              title: "height: 35px;font-size: 24px;",
              img: 80,
              size: "font-size: 14px;",
              exam: "height: 50px;font-size: 14px;",
            },
            case: {
              height: "height: 630px;",
              title: "font-size: 20px;",
              span: "font-size:12px;",
              size: 4,
              table: {
                height: "height: 170px;",
                size1: "font-size:22px;",
                szie2: "",
                width1: "width:40%",
                width2: "width:50%",
                one: true,
                two: false,
                three: false,
              },
            },
          },
          right: {
            top: "height: 50px;font-size: 28px;",
            twoSource: "width: 55%",
            table: 120,
            time: {
              width: "width: 14%",
            },
          },
          dialog: {
            ont: {
              width: "70%",
              table: {
                table1: "75",
                table2: "100",
                table3: "80",
                table4: "90",
                table5: "120",
                table6: "100",
                table7: "110",
                table8: "150",
                table9: "78",
              },
            },
            two: {
              dialogWidth: "70%",
              form: {
                width1: "width: 180px;",
                width2: "width: 180px;",
                width3: "width: 180px;",
                width4: "width: 180px;",
              },
              table: {
                height: "650",
                width1: "",
                width2: "80",
                width3: "120",
                width4: "95",
                width5: "100",
                width6: "100",
                width7: "110",
                width8: "110",
              },
            },
            three: {
              dialogWidth: "50%",
            },
          },
          button:{
            one: true,
            two: false,
            three: false,
          }
        };
      }
    });
  },
  methods: {
    callQueueSelect(data){
      this.$refs.callQueueMessageRef.callSelectInit(data);
    },
    tableSaveMonitor(status){
      if (status){
        this.$emit("table-save-send",status);
      }
    },
    reservationSuccess(data){
      this.$refs.reservationTableRef.GetTableDataTwo(data);
    },
    reservationCenterMonitor(status,type) {
      if (status) {
        this.appointmentType = type;
        this.reservationStatus = true
        ++this.tableKey;
        this.tableKeyTwo += (1 + this.tableKey);
        this.tableKeyThree += (2 + this.tableKey)
      }
    },
    clickTableMonitor(data){
      this.$refs.caseTableRef.receiveRowTable(data);
      this.$refs.reservationTimeRef.receiveRowTable(data);
    },
    createTableMonitor(data){
      this.$refs.caseTableRef.receiveRowTable(null);
      this.$refs.reservationTimeRef.createReservationTime(data);
    },
    closeAppointmentMonitor(data){
      this.reservationStatus = data;
      this.$store.commit('SET_RESERVATION_TABLE', []);
      this.$store.commit('SET_RESERVATION_ROW_TABLE', {});
    },
  }
}
</script>

<style scoped lang="scss">
.reservation-exam-home {

  ::v-deep.el-dialog__body {
    padding: 5px 5px;
    color: #606266;
    font-size: 14px;
    word-break: break-all;
  }

  .exam-dialog-master {
    display: flex;
    height: 700px;
    .dialog-left{
      width: 30%;
      border: 1px solid #1c84c6;

    }

    .dialog-right{
      width: 70%;
      border: 1px solid #1c84c6;

      .right-title{
        background: #185f7d;
        text-align: center;
        color: #ffffff;
      }

    }
  }

  ::v-deep.el-dialog:not(.is-fullscreen) {
    margin-top: 2vh !important;
  }


}
</style>
