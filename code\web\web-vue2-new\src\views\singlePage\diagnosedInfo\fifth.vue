<template>
  <div class="single-master">
  
    <div class="element-master">
      <div class="my-table">
  
        <el-table :data="fifthList" style="width: 100%" border :height="tableHeight - 550" highlight-current-row>
          <el-table-column prop="recorD_DATE" align="center" label="登记日期" />
  
          <el-table-column prop="fasting" align="center" label="空腹" />
          <el-table-column prop="afteR_BREAKFASET" align="center" label="早餐后" />
          <el-table-column prop="beforE_LUNCH" align="center" label="午餐前" />
          <el-table-column prop="afteR_LUNCH" align="center" label="午餐后">
  
          </el-table-column>
          <el-table-column prop="beforE_DINNER" align="center" label="晚餐前">
  
          </el-table-column>
          <el-table-column prop="afteR_DINNER" align="center" label="晚餐后">
  
          </el-table-column> <el-table-column prop="beforE_SLEEP" align="center" label="睡前">
  
          </el-table-column>
          <el-table-column prop="random" align="center" label="随机">
  
          </el-table-column>
          <el-table-column prop="remarks" align="center" label="备注">
  
          </el-table-column>
        </el-table>
  
      </div>
  
  
  
  
    </div>
  
  </div>
</template>

<script>
import {
  GetParasiteList,
  GetParasiteByTestNo,
  GetParasiteByDocName
} from "@/api/singlePage/parasite";
import {
  GetDiagnosedAndBase,
  GetPrescriptionList,
  GetInspectList,
  GetDisposeList,
  GetBloodSugarList,
  GetAssayList
} from "@/api/singlePage/diagnosedInfo";
export default {
  name: 'parasiteIndex',
  props:
  {
    fifthList: Array,


  },
  data() {
    return {
      currentTabIndex: '0',
      activeName: 'first',
      queueForm: {
        pageNum: 1,
        pageSize: 10,

      },
      baseInfo: [],
      tableDate: [],
      total: 0,
      tableDateTestNo: [],
      tableDateDocName: [],
      titleTestNo: "检验详情信息",
      titleDocName: "医生详情信息",
      testNoDialog: false,
      docNameDialog: false,
      tableHeight: undefined,
    }
  },

  created() {
    //this.getList();
    this.handleResize();
  },
  watch: {
    fifthList: {
      handler(newVal, oldVal) {
        if (newVal) {


        }
      },
      deep: true
    }
  },

  mounted() {
    window.addEventListener('resize', this.handleResize); // 添加监听器


  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize); // 移除监听器
  },

  methods: {




    // 自定义高度变化更新高度
    handleResize() {
      this.tableHeight = window.innerHeight; // 更新高度数据
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.text-click {
  color: #00afff;
}

:hover.text-click {
  cursor: pointer;
  border-bottom: 1px solid #00afff;
}

.my-table {
  ::v-deep.el-table--medium .el-table__cell {
    padding: 0;
  }

  ::v-deep.el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    word-break: break-word;
    background-color: #f8f8f9;
    color: #515a6e;
    height: 30px;
    font-size: 14px;
  }

  ::v-deep.el-table th.el-table__cell>.cell {
    padding: 0;
  }

  ::v-deep.el-table--border .el-table__cell:first-child .cell {
    padding: 0;
  }

  ::v-deep.el-button+.el-button {
    margin-left: 2px;
  }

  ::v-deep.el-table .cell {
    padding: 1px;
  }

  /* ---el-table滚动条公共样式--- */
  // 滚动条的宽度
  ::v-deep.el-table__body-wrapper::-webkit-scrollbar {
    width: 10px; // 横向滚动条
    height: 10px; // 纵向滚动条 必写
  }

  // 滚动条的滑块
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }
}
</style>