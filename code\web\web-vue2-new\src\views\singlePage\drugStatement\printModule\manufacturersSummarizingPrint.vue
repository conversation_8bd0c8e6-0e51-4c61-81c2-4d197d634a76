<template>
  <div>
    <el-dialog title="打印预览" top="2vh" :visible.sync="dialogVisible" width="85%" :show-close="true"
               :close-on-press-escape="true" :close-on-click-modal="true"
    >
      <print-components @page-size="totalSizeClick"
                        :print-data="printDatas"
                        :key="printKey"
                        :print-data-list="printDatas.printDataList"
                        :table-title="printDatas.tableTitle"
                        :page-html="printDatas.pageHtml"
                        :total-page="printDatas.totalPage"
                        :total-size="printDatas.totalSize"
                        :merge-data="printDatas.mergeData"
                        :is-merge="printDatas.isMerge"
                        :title-html="printDatas.titleHtml"
                        :bottom-html="printDatas.bottomHtml"
                        :merge-size="printDatas.mergeSize">
      </print-components>
    </el-dialog>
  </div>
</template>

<script>
import { getDrugStatementTable } from '@/api/singlePage/drugStatement'
import PrintComponents from '../../../../components/print/printComponents.vue'

/**
 * 按厂家汇总
 */
export default {
  name: 'manufacturersSummarizingPrint',
  props: [],
  components: { PrintComponents },
  data() {
    return {
      printDatas: {
        printDataList: [],//打印数据
        tableTitle: [{
          key: '来源',
          title: '来源',
          width: '45%'
        }, {
          key: '零售金额',
          title: '零售金额',
          width: '15%'
        }, {
          key: '进价金额',
          title: '进价金额',
          width: '15%'
        }, {
          key: 'JXCJ',
          title: '进销差价',
          width: '15%'
        }, {
          key: '单据数',
          title: '单据数',
          width: '15%'
        }],//标题数据
        pageHtml: '',//是否page同行
        totalPage: 0,//总行数
        totalSize: 14,//打印页每页显示条数
        mergeData: [],//合并数据
        isMerge: true,//是否合并
        titleHtml: '',//每页title文字（需要带样式）
        bottomHtml: '111',//每页底部文字（需要带样式）
        mergeSize: 2,
      },
      printKey: 0,
      dialogVisible: false,
      store: '',
      lxje: [],
      jjje:[],
      jxcj: [],
      djs: [],
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    totalSizeClick(data) {
      ++this.printKey;
      this.printDatas.totalSize = data
      this.chengeRows(data);
    },
    printData(data) {
      getDrugStatementTable(data).then(res => {
        this.storeDispose(data);
        if (res.code === 200) {
          this.tableData = res.data
          this.tableData.forEach(t => {
            t.JXCJ = parseFloat((t.进价金额 - t.零售金额).toFixed(2))
          })
          this.chengeRows(this.printDatas.totalSize)
          this.dialogVisible = true;
        }
      })
    },
    chengeRows(val) {
      let tableData = this.sliceArr(this.tableData, val) //按照行数将表格数据切割为二维数组
      this.printDatas.printDataList = tableData
      this.printDatas.totalPage = tableData.length //这里拿到的就是总页面数
      tableData.forEach((item, index) => {
        let lxje = 0
        let jjje = 0
        let jxcj = 0
        let djs = 0
        item.forEach((i, j) => {
          lxje += i.零售金额 //这个就是要计算的列的参数名合计，并且这里是按照每页计算的
          jjje += i.进价金额
          jxcj += i.JXCJ
          djs += i.单据数
        })
        this.$set(this.lxje, index, lxje.toFixed(2))
        this.$set(this.jjje, index, jjje.toFixed(2))
        this.$set(this.jxcj, index, jxcj.toFixed(2))
        this.$set(this.djs, index, djs.toFixed(0))
        this.$set(this.printDatas.printDataList, index, item)
      })
      this.printDatas.mergeData = [this.lxje, this.jjje, this.jxcj, this.djs]
    },
    sliceArr(array, size) {
      if (array.length === 0) {
        return []
      }
      const numOfChunks = Math.ceil(array.length / size)
      const chunks = new Array(numOfChunks)
      for (let i = 0, j = 0; i < numOfChunks; i++) {
        chunks[i] = array.slice(j, j + size)
        j += size
      }
      return chunks
    },
    textDisplay(beginDate,endDate,store){
      this.printDatas.titleHtml = '<div style="font-size: 18px;text-align: center">河南宏力医院</div>' +
        '<div style="font-size: 22px;text-align: center">药品供货商情况汇总</div>' +
        '<div style="display: flex;justify-content: space-between;">' +
        '<div style="display: flex;align-items: center;"><div>单位：</div><div style="padding: 5px;">'+store+'</div></div>' +
        '<div style="display: flex;align-items: center;height: 30px;">' +
        '<div>统计日期：</div><div>'+beginDate +'</div><div style="padding: 0 5px;">-</div><div>'+endDate+'</div>' +
        '</div></div>';
      this.printDatas.pageHtml = "<div style='display: flex;justify-content: flex-start'>单据范围：</div>"
      this.printDatas.bottomHtml =
        "<div style='display: flex;justify-content: flex-end;margin-right: 12%;'>" +
        "<div style='width: 500px;'>会计：</div>" +
        "<div>制表：</div><div style='width: 80px;display: flex;'>"+this.$store.getters.name+"</div>" +
        "</div>"
    },
    storeDispose(t){
      let data = [];
      let deptStr = t.deptStr;
      if (deptStr){
        if (deptStr.includes("8101")){
          data.push("西药库")
        }
        if (deptStr.includes("8102")){
          data.push("中成药库")
        }
        if (deptStr.includes("8103")){
          data.push("草药库")
        }
        if (deptStr.includes("8104")){
          data.push("试剂库")
        }
        this.store = data.join("、")
      }else{
        this.store = "";
      }
      let beginDate = t.beginDate + " 00:00:00";
      let endDate = t.endDate + " 23:59:59"
      this.textDisplay(beginDate,endDate,this.store);
    },
  }
}
</script>

<style scoped lang="scss">

</style>
