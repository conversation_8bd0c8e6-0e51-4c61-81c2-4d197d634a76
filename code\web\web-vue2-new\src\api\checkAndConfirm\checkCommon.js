import request from "@/utils/request";

/**
 * 获取就诊卡对应患者信息
 * @param cardNo
 * @returns {*}
 * @constructor
 */
export function CardRecognitionPatientId(cardNo) {
  return request({
    url: "/CheckCommon/CardRecognitionPatientId?cardNo=" + cardNo,
    method: "get",
  });
}

/**
 * 获取知识库初始化主要参数
 * @param patientId 患者id
 * @param deptCode 科室代码
 * @returns {*}
 * @constructor
 */
export function GetKnowledgeBaseInitData(patientId,deptCode) {
  return request({
    url: "/CheckCommon/GetKnowledgeBaseInitData?patientId=" + patientId + "&deptCode=" + deptCode,
    method: "get",
  });
}

/**
 * 获取检查项目信息
 * @returns {*}
 * @constructor
 */
export function GetExamItemDict() {
  return request({
    url: "/CheckCommon/GetExamItemDict",
    method: "get",
  });
}

/**
 * 获取计价项目信息
 * @param type 计价项目类型
 * @returns {*}
 * @constructor
 */
export function GetValuationItemDict(type) {
  return request({
    url: "/CheckCommon/GetValuationItemDict?type=" + type,
    method: "get",
  });
}

/**
 * 获取计价项目详情信息
 * @param data
 * @returns {*}
 * @constructor
 */
export function GetValuationItemParticulars(data) {
  return request({
    url: "/CheckCommon/GetValuationItemParticulars",
    method: "post",
    data: data,
  });
}

/**
 * 保存计价项目信息
 * @param data
 * @returns {*}
 * @constructor
 */
export function SaveValuationItem(data) {
  return request({
    url: "/CheckCommon/SaveValuationItem",
    method: "post",
    data: data,
  });
}

/**
 * 获取项目localId
 * @param deptCode 执行科室
 * @param examNo 检查好
 * @returns {*}
 * @constructor
 */
export function GetLocalId(data) {
  return request({
    url: "/CheckCommon/GetLocalId",
    method: "post",
    data: data,
  });
}

/**
 * 获取 医技科室树结构
 * @returns {*}
 * @constructor
 */
export function GetMedicalLaboratoryDeptTree() {
  return request({
    url: "/CheckCommon/GetMedicalLaboratoryDeptTree",
    method: "get",
  });
}

export function GetExportDeptDict() {
  return request({
    url: "/CheckCommon/GetExportDeptDict",
    method: "get",
  });
}


export function VerifyPatientIdIntegrality(data) {
  return request({
    url: "/CheckCommon/VerifyPatientIdIntegrality",
    method: "post",
    data: data,
  });
}

export function VerifyPatientAppointMsg(data) {
  return request({
    url: "/CheckCommon/VerifyPatientAppointMsg",
    method: "post",
    data: data,
  });
}

export function VerifyPatientReminder(patientId) {
  return request({
    url: "/CheckCommon/VerifyPatientReminder?patientId=" + patientId,
    method: "get",
  });
}

export function GetExamItemNoList(examNo) {
  return request({
    url: "/CheckCommon/GetExamItemNoList?examNo=" + examNo,
    method: "get",
  });
}

/**
 * 获取卡控权限
 * @returns {*}
 * @constructor
 */
export function VerifyCdssCntrol() {
  return request({
    url: "/CheckCommon/VerifyCdssCntrol",
    method: "get",
  });
}

/**
 * 获取cdss初始化权限
 * @returns {*}
 * @constructor
 */
export function VerifyCdssInit() {
  return request({
    url: "/CheckCommon/VerifyCdssInit",
    method: "get",
  });
}

