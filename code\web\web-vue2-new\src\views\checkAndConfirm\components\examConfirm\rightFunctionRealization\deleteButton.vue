<template>
  <div>
    <!-- 删除按钮操作 -->
  </div>
</template>

<script>
import { DeleteButton } from "@/api/checkAndConfirm/logisticsReminder";

export default {
  name: 'deleteButton',
  props: [],
  components: {},
  data() {
    return {}
  },

  created() {
  },

  mounted() {
  },

  methods: {
    // 按钮点击校验(判断点击按钮调用vistry方法)
    Delete(status) {
      if (status) {
        // 如果status不为null则调用vistry方法
        this.vistry();
      } else {
        this.$message({
          dangerouslyUseHTMLString: true,
          message: '请选择要删除的某一列数据'
        });
      }
    },

    // 删除按钮操作
    vistry() {
      let rowTable = this.$store.getters.rowTable;
      console.log(rowTable)
      this.$confirm('确定要删除患者姓名为' + rowTable.name + '检查项目为《' + rowTable.device + '》的数据吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteButton(rowTable).then(res => {
          this.$emit("delete-success",true);
          this.$message.success(res.message);
        })
      }).catch(() => { });
    },
  },
}
</script>

<style scoped></style>
