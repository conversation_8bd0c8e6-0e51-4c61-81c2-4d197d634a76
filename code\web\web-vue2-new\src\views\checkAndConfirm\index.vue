<template>
  <div class="confirm-home" :style="this.masterStyle.home">
    <div class="confirm-pading">
      <!--      导航信息-->
      <confirm-top @route-value="receiveRouteValue" ref="confirmTopRef"></confirm-top>
      <!--      检查确认代码-->
      <div class="" v-if="routeValue === 'examConfirm'">
        <exam-confirm-index @path-skip="receiveRouteValue"></exam-confirm-index>
      </div>
      <div class="" v-else-if="routeValue === 'reservationCenter'">
        <reservation-center-two></reservation-center-two>
      </div>
      <div class="" v-else-if="routeValue === 'perinatalHealthCare'">
        <four-dimensional-reservation></four-dimensional-reservation>
      </div>
      <div class="" v-else-if="routeValue === 'configurationCenter'">
        <queue-item-dict-two :emp-no="this.$store.getters.empNo"></queue-item-dict-two>
      </div>
      <div class="" v-else-if="routeValue === 'systemConfig'">
        <system-index :home-style="this.masterStyle"></system-index>
      </div>
      <div class="" v-else>
        <exam-confirm-index></exam-confirm-index>
      </div>
    </div>
  </div>
</template>

<script>
import confirmTop from './components/confirmTop.vue'
import examConfirmIndex from './components/examConfirmIndex.vue'
import reservationCenterTwo from './components/reservationCenterTwo.vue'
import queueItemDictTwo from './components/queueItemDictTwo.vue'
import FourDimensionalReservation from './components/fourDimensionalReservation.vue'
import SystemIndex from './components/systemIndex.vue'

export default {
  name: 'index',
  props: [],
  components: { SystemIndex, FourDimensionalReservation, confirmTop, examConfirmIndex, reservationCenterTwo, queueItemDictTwo },
  data() {
    return {
      routeValue: '',
      windowWidth: 0,
      windowHeight: 0,
      windowSize: {
        width: 0,
        height: 0
      },
      masterStyle: {
        home: '',
        itemHeight: '',
        itemWidth: '',
      }
    }
  },
  created() {
    this.handleResize()
  },
  methods: {
    handleResize() {
      // 直接更新windowSize对象
      // 更新响应式数据，如果需要的话
      this.windowWidth = window.innerWidth
      this.windowHeight = window.innerHeight
      this.masterStyle.home = 'height: ' + this.windowHeight + 'px;width: ' + this.windowWidth + 'px;'
      this.masterStyle.itemHeight = 'height: ' + (this.windowHeight - 60) + 'px;'
      this.masterStyle.itemWidth = 'width:' + this.windowWidth +'px;'
      // 更新body元素的样式
      // document.body.style.width = `${this.windowWidth}px`;
      // document.body.style.height = `${this.windowWidth}px`;
    },
    receiveRouteValue(data) {
      this.routeValue = data
      // this.$refs.confirmTopRef.routeItemAssignment(data);
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>

<style scoped lang="scss">
.confirm-home {
  padding: 2px;
  padding-right: 10px;

  .confirm-pading {
    height: 100%;
    border: 1px solid silver;
    box-shadow: 5px 5px 10px silver
  }
}
</style>
