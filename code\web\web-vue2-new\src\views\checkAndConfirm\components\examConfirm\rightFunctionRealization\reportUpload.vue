<template>
  <div class="upload-home">
    <el-dialog :title="title" :visible.sync="uploadStatus" width="80%">
      <div class="upload-master">
        <div class="upload-radio">
          <el-radio-group v-model="itemName" size="medium" @change="groupReplace">
            <span v-for="(item,index) in uploadData" :key="index">
               <el-radio-button class="two-top" :label="item.EXAM_ITEM"></el-radio-button>
            </span>
          </el-radio-group>
        </div>
        <div class="line-master">
          <div class="upload-line">
            <div class="upload-item">
              <div class="upload-title">患者姓名：</div>
              <div class="upload-text">{{ thisRowData.NAME }}</div>
            </div>
            <div class="upload-item">
              <div class="upload-title">性别：</div>
              <div class="upload-text">{{ thisRowData.SEX }}</div>
            </div>
          </div>
          <div class="upload-line">
            <div class="upload-item">
              <div class="upload-title">ID号：</div>
              <div class="upload-text">{{ thisRowData.PATIENT_ID }}</div>
            </div>
            <div class="upload-item">
              <div class="upload-title">住院次数：</div>
              <div class="upload-text">{{ thisRowData.VISIT_ID }}</div>
            </div>
          </div>
          <div class="upload-line">
            <div class="upload-item">
              <div class="upload-title">检查类型：</div>
              <div class="upload-text">{{ thisRowData.EXAM_CLASS }}</div>
            </div>
            <div class="upload-item">
              <div class="upload-title">检查项目：</div>
              <div class="upload-text">{{ thisRowData.EXAM_SUB_CLASS }}</div>
            </div>
          </div>
          <div class="upload-line">
            <div class="upload-item" style="width: 400px;">
              <div class="upload-title" style="width: 130px;">检查名称：</div>
              <div class="upload-text">{{ thisRowData.EXAM_ITEM }}</div>
            </div>
          </div>
          <el-form ref="form" :rules="rules" :model="thisRowData" label-width="100px" size="mini">
            <div style="width: 70%">
              <el-form-item label="所见：" prop="description">
                <el-input type="textarea" size="mini" :show-word-limit="showWordLimit"
                          :autosize="{ minRows: 2, maxRows: 3 }"
                          placeholder="请输入所见" v-model="thisRowData.description"
                >
                </el-input>
              </el-form-item>
              <el-form-item label="诊断：" prop="impression">
                <el-input type="textarea" size="mini" :show-word-limit="showWordLimit"
                          :autosize="{ minRows: 2, maxRows: 3 }"
                          placeholder="请输入诊断" v-model="thisRowData.impression"
                >
                </el-input>
              </el-form-item>
            </div>
            <div style="display: flex">
              <div style="width: 50%;margin-left: 10%;">
                <el-form-item label="是否异常：" prop="isAbnormal">
                  <el-radio-group v-model="thisRowData.isAbnormal">
                    <el-radio :label="0">否</el-radio>
                    <el-radio :label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="危险值：" prop="isCritical">
                  <el-input-number v-model="thisRowData.isCritical" controls-position="right" :min="0" :max="10"
                  ></el-input-number>
                </el-form-item>
                <el-form-item label="确认医师：" prop="confirmDoct">
                  <el-input style="width: 150px;" v-model="thisRowData.confirmDoct" placeholder="请输入确认医师"
                  ></el-input>
                </el-form-item>
                <el-form-item label="上报人：">
                  <el-input style="width: 150px;" v-model="thisRowData.DOCTORNAME" disabled placeholder="请输入确认医师"
                  ></el-input>
                </el-form-item>
              </div>
              <div>
                <div class="uploadButton" v-if="uploadBoor">
                  <el-button type="primary" size="mini" @click="uploadButtons('form')">上传报告</el-button>
                </div>
                <div class="uploadButton" v-if="!uploadBoor">
                  <el-upload class="upload-demo" action="#" :on-change="onChange" :before-remove="beforeRemove" multiple accept=".pdf"
                             :auto-upload="false" :file-list="fileList">
                    <el-button size="small" type="primary">PDF报告上传</el-button>
                    <div slot="tip" class="el-upload__tip">只能上传pdf文件,最大文件限制10MB</div>
                  </el-upload>
                </div>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { uploadMsgInitialize,UploadPdf,DelPdf } from '@/api/checkAndConfirm/uploadPdf'

export default {
  name: 'reportUpload',
  props: [],
  components: {},
  data() {
    return {
      uploadBoor: true,
      showWordLimit: true,
      uploadStatus: false,
      title: '报告上传',
      uploadData: [],
      fileList: [],
      itemName: '',
      thisRowData: {},
      rules: {
        description: [
          { required: true, message: '请输入所见', trigger: 'blur' }
        ],
        impression: [
          { required: true, message: '请输入诊断', trigger: 'blur' }
        ],
        isAbnormal: [
          { required: true, message: '请选择是否异常', trigger: 'blur' }
        ],
        isCritical: [
          { required: true, message: '请输入危险值', trigger: 'blur' }
        ],
        confirmDoct: [
          { required: true, message: '请输入确认报告医师', trigger: 'blur' }
        ],
      }
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    uploadButtons(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.uploadBoor = false;
        }
      });
    },
    deletePdf(examNo, examItemNo) {
      DelPdf(examNo, examItemNo).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg)
        } else {
          this.$message.error('操作失败')
        }
      })
    },
    beforeRemove(file, fileList) {
      this.$confirm(`此操作将永久删除${file.name}文件, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deletePdf(this.thisRowData.EXAM_NO, this.thisRowData.EXAM_ITEM_NO);
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });

    },
    onChange(a, fileList) {
      let data = this.thisRowData;
      this.fileList = fileList;
      const param = new FormData();
      param.append('file', this.fileList[0].raw);
      param.append('examNo', data.EXAM_NO);
      param.append('empNo', this.$store.getters.empNo);
      param.append('empName', data.DOCTORNAME);
      param.append('description', data.description);
      param.append('impression', data.impression);
      param.append('isAbnormal', data.isAbnormal);
      param.append('isCritical', data.isCritical);
      param.append('confirmDoct', data.confirmDoct);
      param.append("examPara", data.EXAM_ITEM);
      param.append("examItemNo", data.EXAM_ITEM_NO);
      console.log(param)
      const loading = this.$loading({
        lock: true,
        text: '休息一下,报告正在上传中(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      UploadPdf(param).then(res => {
        if (res.code === 200) {
          loading.close();
          this.$message.success("文件上传成功!")
        }
      }).catch(() => {
        loading.close();
      })
    },
    groupReplace(item) {
      this.thisRowData = item;
      this.thisRowData.isAbnormal = 0;
      this.thisRowData.isCritical = 0;
    },
    uploadVerify(data) {
      if (data) {
        this.getUploadData(data.examNo)
      }
    },
    getUploadData(examNo) {
      let empNo = this.$store.getters.empNo
      uploadMsgInitialize(examNo, empNo).then(res => {
        if (res.data.length > 0) {
          this.uploadData = res.data;
          this.thisRowData = res.data[0];
          this.thisRowData.isAbnormal = 0;
          this.thisRowData.isCritical = 0;
          this.itemName = res.data[0].EXAM_ITEM;
          this.uploadStatus = true;
          //查询上传相关信息
        } else {
          this.$msgbox.alert(
            '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
            '暂未查询到当前检查号：' + examNo + ' 相关数据，报告无法上传</div>',
            '系统提示',
            {
              confirmButtonText: '确定',
              type: 'warning',
              dangerouslyUseHTMLString: true
            }).then(() => {
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.upload-home{
  .upload-master {
    border: 1px solid #DEFBFB;
    border-radius: 10px;
    background-color: #DEFBFB;
    margin: 0 30px;
    padding-bottom: 10px;

    .upload-radio {
      display: flex;
      justify-content: center;
      margin: 10px 0;
    }

    .line-master {
      border: 1px solid #3A71A8;
      margin: 0 50px;
      border-radius: 5px;

      .upload-line {
        display: flex;
        justify-content: space-evenly;

        .upload-item {
          padding: 10px 0;
          display: flex;
          width: 200px;

          .upload-title {
            font-size: 18px;
          }

          .upload-text {
            display: flex;
            align-items: center;
            font-size: 16px;
            color: red;
          }
        }
      }
    }

  }
  ::v-deep.el-dialog:not(.is-fullscreen) {
    margin-top: 1vh !important;
  }
}
</style>
