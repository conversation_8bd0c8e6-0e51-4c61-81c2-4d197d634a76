<template>
  <div class="single-master" v-if="bodyStatus">
    <div class="single-title">药品报表查询</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <div style="display: flex">
              <div>
                <div>
                  <el-form-item label="开始时间:">
                    <el-date-picker
                      v-model="queueForm.beginDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      format="yyyy-MM-dd"
                      placeholder="选择日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item label="结束时间:">
                    <el-date-picker
                      v-model="queueForm.endDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      format="yyyy-MM-dd"
                      placeholder="选择日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </div>
                <div style="display: flex">
                  <el-form-item style="width: 400px;">
                    <el-radio-group @change="groupClick" v-model="queueForm.type" style="display: flex;flex-direction: column;">
                      <el-radio :label="'4'" style="height: 22px;">按医药公司供货情况查询</el-radio>
                      <el-radio :label="'5'" style="height: 30px;width: 120px;display: flex;align-items: center"
                                class="muInput">
                        按供货商查询：
                        <el-select v-model="queueForm.supplier" :filter-method="remoteMethodOne" filterable
                                   placeholder="请输入关键字进行检索" clearable  style="margin-left: 40px;">
                          <el-option
                            v-for="(item,index) in suppliersNew"
                            :key="index"
                            :label="item.VALUE"
                            :value="item.VALUE"
                          >
                          </el-option>
                        </el-select>
                      </el-radio>
                      <el-radio :label="'6'" style="height: 30px;width: 120px;display: flex;align-items: center"
                                class="muInput">
                        按药品历史价格查询：
                        <el-select v-model="queueForm.drugCode" :filter-method="remoteMethodTwo" filterable
                                   placeholder="请输入关键字进行检索" clearable  style="margin-left: -2px;">
                          <el-option
                            v-for="(item,index) in drugNew"
                            :key="index"
                            :label="item.LABEL"
                            :value="item.VALUE"
                          >
                          </el-option>
                        </el-select>
                      </el-radio>
                      <el-radio :label="'7'" style="height: 30px;width: 120px;display: flex;align-items: center"
                                class="muInput">
                        按厂家查询：
                        <el-select v-model="queueForm.manufacturers" filterable
                                   placeholder="请输入关键字进行检索" clearable  style="margin-left: 54px;">
                          <el-option
                            v-for="(item,index) in manufacturers"
                            :key="index"
                            :label="item.VALUE"
                            :value="item.VALUE"
                          >
                          </el-option>
                        </el-select>
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item style="display: flex;align-items: center;margin-left: 70px;">
                    <div class="element-button my-button">
                      <el-button type="primary" icon="el-icon-search" @click="getTable">查询</el-button>
                      <el-button type="primary" icon="el-icon-download" @click="exportTable">导出</el-button>
                      <el-button type="primary" icon="el-icon-document-checked" @click="printTable">打印预览</el-button>
                    </div>
                  </el-form-item>
                </div>
              </div>
              <div style="margin-left: -290px;">
                <el-form-item>
                  <el-radio-group @change="groupClick" v-model="queueForm.type" style="display: flex;flex-direction: column;">
                    <el-radio :label="'1'" style="height: 25px;display: flex;">
                      按供货商汇总:
                      <div v-if="queueForm.type === '1' || queueForm.type === '2' || queueForm.type === '3'" style="float: right;margin-left: 30px; width: 220px;">
                        <el-checkbox-group v-model="queueForm.deptCode" style="display: flex;flex-wrap: wrap">
                          <el-checkbox label="8101">西药库</el-checkbox>
                          <el-checkbox label="8102">中成药库</el-checkbox>
                          <el-checkbox label="8103">草药库</el-checkbox>
                          <el-checkbox label="8104">试剂库</el-checkbox>
                        </el-checkbox-group>
                      </div>
                      <div v-else style="float: right;margin-left: 10px; width: 220px;">
                        <el-checkbox-group disabled style="display: flex;flex-wrap: wrap">
                          <el-checkbox label="8101">西药库</el-checkbox>
                          <el-checkbox label="8102">中成药库</el-checkbox>
                          <el-checkbox label="8103">草药库</el-checkbox>
                          <el-checkbox label="8104">试剂库</el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </el-radio>
                    <el-radio :label="'2'" style="height: 25px;">按厂家汇总</el-radio>
                    <el-radio :label="'3'" style="height: 25px;">按药品名称汇总</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>

            </div>

          </el-form>
        </div>
      </div>
      <div>
        <supplier-all v-if="queueForm.type === '1'" ref="supplierAllRefs"
                      :query-form="queueForm" :table-height="tableHeight" :key="tableKey"></supplier-all>
        <manufacturers-summarizing v-if="queueForm.type === '2'" ref="manufacturersSummarizingRefs"
                                   :query-form="queueForm" :table-height="tableHeight" :key="tableKey"></manufacturers-summarizing>
        <drug-name-summarizing v-if="queueForm.type === '3'" ref="drugNameSummarizingRefs"
                               :query-form="queueForm" :table-height="tableHeight" :key="tableKey"></drug-name-summarizing>
        <supplier-condition v-if="queueForm.type === '4'" ref="supplierConditionRefs"
                            :query-form="queueForm" :table-height="tableHeight" :key="tableKey"></supplier-condition>
        <supplier v-if="queueForm.type === '5'" ref="supplierRefs"
                  :query-form="queueForm" :table-height="tableHeight" :key="tableKey"></supplier>
        <drug-history v-if="queueForm.type === '6'" ref="drugHistoryRefs"
                      :query-form="queueForm" :table-height="tableHeight" :key="tableKey"></drug-history>
        <manufacturers v-if="queueForm.type === '7'" ref="manufacturersRefs"
                       :query-form="queueForm" :table-height="tableHeight" :key="tableKey"></manufacturers>
      </div>

      <div>
        <drug-history-pring ref="drugHistoryPrintRefs"></drug-history-pring>
        <drug-name-summarizing-print ref="drugNameSummarizingPrintRefs"></drug-name-summarizing-print>
        <manufacturers-print ref="manufacturersPrintRefs"></manufacturers-print>
        <manufacturers-summarizing-print ref="manufacturersSummarizingPrintRefs"></manufacturers-summarizing-print>
        <supplier-print ref="supplierPrintRefs"></supplier-print>
        <supplier-all-print ref="supplierAllPrintRefs"></supplier-all-print>
        <supplier-condition-print ref="supplierConditionPrintRefs"></supplier-condition-print>
      </div>
    </div>
  </div>
</template>

<script>
import {getDrugStatementItemDict,exportDrugStatementItemDict,getUserInfoByEmpNo} from '@/api/singlePage/drugStatement';
import SupplierAll from './module/supplierAll.vue'
import Manufacturers from './module/manufacturers.vue'
import DrugNameSummarizing from './module/drugNameSummarizing.vue'
import SupplierCondition from './module/supplierCondition.vue'
import Supplier from './module/supplier.vue'
import DrugHistory from './module/drugHistory.vue'
import ManufacturersSummarizing from './module/manufacturersSummarizing.vue'
import { excelDownloadXLSX } from "@/utils/BlobUtils"
import DrugHistoryPring from './printModule/drugHistoryPring.vue'
import DrugNameSummarizingPrint from './printModule/drugNameSummarizingPrint.vue'
import ManufacturersPrint from './printModule/manufacturersPrint.vue'
import ManufacturersSummarizingPrint from './printModule/manufacturersSummarizingPrint.vue'
import SupplierPrint from './printModule/supplierPrint.vue'
import SupplierAllPrint from './printModule/supplierAllPrint.vue'
import SupplierConditionPrint from './printModule/supplierConditionPrint.vue'
export default {
  name: 'inquires',
  props: [],
  components: {
    SupplierConditionPrint,
    SupplierAllPrint,
    SupplierPrint,
    ManufacturersSummarizingPrint,
    ManufacturersPrint,
    DrugNameSummarizingPrint,
    DrugHistoryPring,
    ManufacturersSummarizing,
    DrugHistory,
    Supplier,
    SupplierCondition,
    DrugNameSummarizing,
    Manufacturers,
    SupplierAll
  },
  data() {
    return {
      suppliers: [{ NAME:'111', CODE: '111' }],
      suppliersNew: [],
      drug: [{ VALUE:'123', CODE: '123',LABEL: '11'}],
      drugNew: [],
      manufacturers: [{VALUE: '11231'}],
      queueForm: {
        beginDate: this.formatDateMonth(new Date()),
        endDate: this.formatDate(new Date()),
        type: '4',
        deptCode: [],
        deptStr: '',
        supplier: '',
        drugCode: '',
        manufacturers: '',
      },
      bodyStatus: false,
      tableHeight: undefined,
      tableKey: 0,
      empNo: '',
    }
  },
  created() {
    this.empNo = this.$route.query && this.$route.query.emp_no;
    this.handleResize()
    this.getUserInfo(this.empNo)
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    getUserInfo(empNo){
      if (empNo){
        getUserInfoByEmpNo(empNo).then(res => {
          if (res.code === 200){
            this.$store.commit('SET_NAME', res.data.NAME);
            this.getDrugStatementItem();
            this.bodyStatus = true;
          }
        })
      }else{
        this.bodyStatus = false;
        this.$msgbox.alert(
          '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
          '登录失败!!!' + '</div>' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
      }
    },
    printTable(){
      let form = this.queueForm;
      let type = form.type;
      this.queueForm.deptStr = form.deptCode.join();
      if (type === '1'){
        this.$refs.supplierAllPrintRefs.printData(form);
      }else if (type === '2'){
        this.$refs.manufacturersSummarizingPrintRefs.printData(form);
      }else if (type === '3'){
        this.$refs.drugNameSummarizingPrintRefs.printData(form);
      }else if (type === '4'){
        this.$refs.supplierConditionPrintRefs.printData(form);
      }else if (type === '5'){
        this.$refs.supplierPrintRefs.printData(form);
      }else if (type === '6'){
        this.$refs.drugHistoryPrintRefs.printData(form);
      }else if (type === '7'){
        this.$refs.manufacturersPrintRefs.printData(form);
      }
    },
    exportTable(){
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力导出中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      exportDrugStatementItemDict(this.queueForm).then(res => {
        let type = this.queueForm.type;
        let fileName = "";
        if ('1' === type){
          fileName = "药品供货商情况汇总（供货商）";
        }else if ('2' === type){
          fileName = "药品供货商情况汇总（厂家）";
        }else if ('3' === type){
          fileName = "药品名称汇总";
        }else if ('4' === type){
          fileName = "医药公司供货情况";
        }else if ('5' === type){
          fileName = "河南宏力医院药品入库凭证（供货商）";
        }else if ('6' === type){
          fileName = "药品历史价格统计";
        }else if ('7' === type){
          fileName = "河南宏力医院药品入库凭证（厂家）";
        }
        excelDownloadXLSX(res, fileName)
      }).finally(() => {
        loading.close();
      })
    },
    groupClick(){
      ++this.tableKey;
    },
    remoteMethodTwo(query){
      this.queueForm.drugCode = query;
      if (query !== "") {
        this.drugNew = this.drug.filter((item) => {
          // 这里是用的value选项筛选，默认是label
          if (item.CODE.toUpperCase().indexOf(query.toUpperCase()) > -1 ||
            item.LABEL.toLowerCase().indexOf(query.toLowerCase()) > -1){
            return true;
          }
        });
      } else {
        this.drugNew = [];
      }
    },
    remoteMethodOne(query){
      this.queueForm.supplier = query;
      if (query !== "") {
        this.suppliersNew = this.suppliers.filter((item) => {
          // 这里是用的value选项筛选，默认是label
          if (item.CODE.toUpperCase().indexOf(query.toUpperCase()) > -1 ||
            item.VALUE.toLowerCase().indexOf(query.toLowerCase()) > -1){
            return true;
          }
        });
      } else {
        this.suppliersNew = [];
      }
    },
    getTable(){
      let type = this.queueForm.type;
      this.queueForm.deptStr = this.queueForm.deptCode.join();
      if (type === '1'){
        this.$refs.supplierAllRefs.getTable();
      }else if (type === '2'){
        this.$refs.manufacturersSummarizingRefs.getTable();
      }else if (type === '3'){
        this.$refs.drugNameSummarizingRefs.getTable();
      }else if (type === '4'){
        this.$refs.supplierConditionRefs.getTable();
      }else if (type === '5'){
        this.$refs.supplierRefs.getTable();
      }else if (type === '6'){
        this.$refs.drugHistoryRefs.getTable();
      }else if (type === '7'){
        this.$refs.manufacturersRefs.getTable();
      }
    },
    getDrugStatementItem(){
      getDrugStatementItemDict().then(res => {
        if (res.code === 200){
          this.suppliers = res.data.suppliers;
          this.drug = res.data.drug;
          this.manufacturers = res.data.manufacturers;
        }
      })
    },
    handleResize() {
      this.tableHeight = window.innerHeight  - 250 // 更新高度数据
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    formatDateMonth(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      return `${year}-${month}-01`
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
.muInput {
  ::v-deep.el-input--medium .el-input__inner {
    width: 200px !important;
    height: 28px !important;
    line-height: 30px !important;
  }

  ::v-deep.el-input--suffix .el-input__inner {
    padding-right: 5px !important;
    padding-left: 5px !important;
  }
}

</style>
