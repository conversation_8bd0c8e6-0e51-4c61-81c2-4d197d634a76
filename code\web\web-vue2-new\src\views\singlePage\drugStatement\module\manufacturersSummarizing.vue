<template>
  <div class="statement-home" :style="'height:' + (tableHeight) + 'px'" >
    <div class="statement-title">河南宏力医院</div>
    <div class="statement-text">药品供货商情况汇总</div>
    <div class="statement-data">
      <div class="statement-item" style="display: flex;align-items: center">
        <div>单位：</div>
        <div style="padding: 5px;">{{store}}</div>
      </div>
      <div class="statement-item" style="height: 30px;display: flex;align-items: center">
        <div >统计日期：</div>
        <div >{{beginDate}}</div>
        <div style="padding: 0 5px;">-</div>
        <div >{{endDate}}</div>
      </div>
    </div>
    <div class="statement-data">
      <div class="statement-item">
        <div>单据范围：</div>
        <div></div>
      </div>
      <div class="statement-item" style="margin-right: 50px;">
        <div>第1页</div>
        -
        <div>共1页</div>
      </div>
    </div>
    <div class="statement-table">
      <div class="table-title">
        <div class="statement-master">
          <div class="statement-item" :style="item.width" v-for="(item,index) in tableTitle" :key="index">
            {{ item.title }}
          </div>
        </div>
      </div>
      <div class="statement-list-master" v-loading="loading">
        <el-scrollbar style="width: 100%; overflow-x: hidden" :style="'height:' + (tableHeight - 200) + 'px'">
          <div class="statement-line" v-for="(item,index) in tableDate" :key="index">
            <div class="statement-column" :style="tableTitle[0].width">
              {{ item.来源 }}
            </div>
            <div class="statement-column" :style="tableTitle[1].width">
              {{ item.零售金额 }}
            </div>
            <div class="statement-column" :style="tableTitle[2].width">
              {{ item.进价金额 }}
            </div>
            <div class="statement-column" :style="tableTitle[3].width">
              {{ (item.进价金额 - item.零售金额).toFixed(2) }}
            </div>
            <div class="statement-column" :style="tableTitle[4].width">
              {{ item.单据数 }}
            </div>
          </div>
        </el-scrollbar>
      </div>
      <div class="statement-table">
        <div class="table-title">
          <div class="statement-master">
            <div class="statement-item" :style="tableTitle[0].width">合计</div>
            <div class="statement-item" :style="tableTitle[1].width">{{collectData.one? collectData.one.toFixed(2) : 0 }}</div>
            <div class="statement-item" :style="tableTitle[2].width">{{collectData.two? collectData.two.toFixed(2) : 0}}</div>
            <div class="statement-item" :style="tableTitle[3].width">
              {{collectData.one && collectData.two? (collectData.two - collectData.one).toFixed(2) : 0 }}
            </div>
            <div class="statement-item" :style="tableTitle[4].width">{{ collectData.three }}</div>
          </div>
        </div>
      </div>
      <div style="display: flex;justify-content: flex-end;margin-right: 12%;">
        <div style="display: flex">
          <div>会计：</div>
          <div style="width: 120px;"></div>
        </div>
        <div style="display: flex">
          <div>制表：</div>
          <div>{{this.$store.getters.name}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDrugStatementTable } from '@/api/singlePage/drugStatement'
/**
 * 按厂家汇总
 */
export default {
  name: 'manufacturersSummarizing',
  props: ['tableHeight','queryForm'],
  components: {},
  data() {
    return {
      tableDate: [],
      collectData: {},
      beginDate:this.queryForm.beginDate + " 00:00:00",
      endDate: this.queryForm.beginDate + " 23:59:59",
      store: '',
      loading: false,
      tableTitle: [{
        title: '来源',
        width: 'width: 40%'
      }, {
        title: '零售金额',
        width: 'width: 16%'
      }, {
        title: '进价金额',
        width: 'width: 16%'
      }, {
        title: '进销差价',
        width: 'width: 16%'
      }, {
        title: '单据数',
        width: 'width: 12%'
      }]
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    getTable() {
      this.loading = true
      this.tableDate = [];
      getDrugStatementTable(this.queryForm).then(res => {
        if (res.code === 200) {
          this.tableDate = res.data
          if (res.data.length > 0){
            this.collectData = {
              one: res.data.map(x => x.零售金额).reduce((acc, num) => acc + num),
              two: res.data.map(x => x.进价金额).reduce((acc, num) => acc + num),
              three: res.data.map(x => x.单据数).reduce((acc, num) => acc + num)
            }
          }else{
            this.collectData = {};
          }

          this.storeDispose();
        }
      }).finally(() => {
        this.loading = false
      })
    },
    storeDispose(){
      let data = [];
      let deptStr = this.queryForm.deptStr;
      if (deptStr){
        if (deptStr.includes("8101")){
          data.push("西药库")
        }
        if (deptStr.includes("8102")){
          data.push("中成药库")
        }
        if (deptStr.includes("8103")){
          data.push("草药库")
        }
        if (deptStr.includes("8104")){
          data.push("试剂库")
        }
        this.store = data.join("、")
      }else{
        this.store = "";
      }
    },
  }
}
</script>

<style scoped lang="scss">
.statement-home {
  width: 60%;

  .statement-title {
    font-size: 18px;
    text-align: center;
  }

  .statement-text {
    font-size: 22px;
    text-align: center;
  }

  .statement-data {
    display: flex;
    justify-content: space-between;

    .statement-item {
      display: flex;
    }
  }

  .statement-table {
    border: 1px solid black;
    height: 35px;

    .statement-master {
      display: flex;

      .statement-item {
        border-right: 1px solid black;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 34px;
      }
    }
  }

  .statement-list-master {
    border: 1px solid black;

    .statement-line {
      border-bottom: 1px solid black;
      display: flex;

      .statement-column {
        border-right: 1px solid black;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        white-space: nowrap; /* 确保文本在一行内显示 */
        overflow: hidden; /* 隐藏超出容器的内容 */
      }
    }
  }
}
</style>
