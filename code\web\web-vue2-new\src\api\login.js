import request from '@/utils/request'
const baseURL = "system";


// 登录方法
export function login(username, password) {
  const data = {
    username,
    password,
  }
  return request({
    url: baseURL + '/Stafflogin',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}


// 自动登录方法
export function autoLogin(appId,accessToken) {
  return request({
    url: baseURL + '/AutoLogin?appId='+appId+'&accessToken=' + accessToken,
    headers: {
      isToken: false
    },
    method: 'get',
  })
}


// 注册方法
export function register(data) {
  return request({
    url: '/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: baseURL + '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: baseURL + '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: baseURL + '/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}
