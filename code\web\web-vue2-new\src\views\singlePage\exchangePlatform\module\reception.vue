<template>
  <div class="single-master">
    <div class="single-title">接收列表</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="开始时间:">
              <el-date-picker
                @change="getList"
                v-model="queueForm.beginDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                :clearable="false"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker
                @change="getList"
                v-model="queueForm.endDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                :clearable="false"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div class="element-table">
          <el-table v-loading="loading" :data="tableData" style="width: 100%" border :height="(tableHeight - 185)">
            <el-table-column type="index" width="50" align="center"></el-table-column>
            <el-table-column prop="senderTitle" align="center" label="标题"></el-table-column>
            <el-table-column prop="senderName" align="center" label="发送人" width="80"></el-table-column>
            <el-table-column prop="senderDate" align="center" label="接收日期" width="90"></el-table-column>
            <el-table-column prop="memo" align="center" label="备注" width="160"></el-table-column>
            <el-table-column prop="FIRM_ID" align="center" label="文件信息" width="120">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-s-order" @click="newsOpen(scope.row)"></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="element-drawer">
          <el-drawer
            title="文件信息"
            :visible.sync="drawer"
            size="60%"
          >
            <el-scrollbar style="height: 94.7%;overflow-x: hidden;margin-top: 2px;">
              <div style="height: 30px;padding: 0 3px;" v-for="(item,index) in fileList" :key="index">
                <div style="display: flex;padding: 2px 5px;border: 1px solid #DFECFD">
                  <div style="display: flex;align-items: center;font-size: 18px;color: #00afff">
                    {{item.fileName}}
                  </div>
                  <div style="display: flex;align-items: center;font-size: 12px;padding: 0 20px;color: #00afff;margin-top: 5px;">
                    {{(item.fileSize / 1024 / 1024) > 1? (item.fileSize / 1024 / 1024).toFixed(2) + 'MB' : (item.fileSize / 1024).toFixed(2) + 'KB'  }}
                  </div>
                  <div style="width: 20%"><el-button type="text" size="mini" style="margin-left: 10%" icon="el-icon-s-promotion"
                                  @click="downloadButton(item)">
                    下载
                  </el-button></div>
                </div>
              </div>
            </el-scrollbar>
          </el-drawer>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getReceptionList,getFileInfo} from '@/api/singlePage/exchangePlatform'
export default {
  name: 'reception',
  props: ['employeeId','tableHeight'],
  components: {},
  data() {
    return {
      queueForm: {
        beginDate: this.formatDateMonth(new Date()),
        endDate: this.formatDate(new Date()),
        userId: this.employeeId,
      },
      loading: true,
      drawer: false,
      tableData: [],
      fileList: [],
    }
  },
  created() {
    this.getList();
  },
  methods: {
    downloadButton(row){
      if (row.status === '0'){
        window.open(row.filePath, '_blank')
      }else{
        this.$msgbox.alert(
          '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
          '文件链接已失效' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
      }
    },
    newsOpen(row){
      this.fileList = [];
      getFileInfo(row.id).then(res => {
        if (res.code === 200){
          this.fileList = res.data;
          this.drawer = true;
        }
      })
    },
    getList(){
      this.loading = true;
      getReceptionList(this.queueForm).then(res => {
        this.tableData = res.data;
        this.loading = false;
      })
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    formatDateMonth(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      return `${year}-${month}-01`
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../../assets/styles/singlePage2";
</style>
