<template>
    <div class="single-master">
        <div class="single-title">质量改进项目模块</div>
        <div class="single-element">
            <div class="element-master">
                <!-- form -->
                <div class="element-form">
                    <el-form :inline="true" :model="queueForm">
                        <el-form-item label="质量改进项目名称:">
                            <el-input placeholder="请输入质量改进项目名称" v-model="queueForm.qualityImprovement" clearable />
                        </el-form-item>
                        <el-form-item label="科室名称:">
                            <el-select v-model="queueForm.deptName" filterable clearable placeholder="请选择">
                                <el-option v-for="item in deptNameDict" :key="item.value" :label="item.label"
                                    :value="item.label">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="年度:">
                            <el-date-picker v-model="queueForm.yearTime" type="year" placeholder="选择年" value-format="yyyy"
                                format="yyyy" />
                        </el-form-item>
                        <el-form-item>
                            <div class="element-button">
                                <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
                                <el-button type="primary" icon="el-icon-plus" @click="add">新增</el-button>
                                <el-button type="primary" icon="el-icon-delete" @click="del">删除</el-button>
                                <el-button type="primary" icon="el-icon-download" @click="templateDownload">模版下载</el-button>
                                <el-button type="primary" icon="el-icon-share" @click="indexOne">首页</el-button>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <!-- table -->
                <div class="element-table">
                    <div class="my-table">
                        <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 117"
                            highlight-current-row @row-click="handleRowClick">
                            <el-table-column type="index" align="center" />
                            <el-table-column prop="qualityimprovement" align="center" label="质量改进项目名称" />
                            <el-table-column prop="deptname" align="center" label="科室名称" />
                            <el-table-column prop="yeartime" align="center" label="年度" />
                            <el-table-column prop="filenamE1" align="center" label="质量改善项目申报书">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.fileurL1 !== null">
                                        {{ (scope.row.filenamE1) }}
                                    </span>
                                    <span v-else style="color: red;">
                                        未上传文件
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="filenamE2" align="center" label="质量改善项目过程评价">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.fileurL2 !== null">
                                        {{ scope.row.filenamE2 }}
                                    </span>
                                    <span v-else style="color: red;">
                                        未上传文件
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="filenamE3" align="center" label="质量改善项目年终总结">
                                <template slot-scope="scope">
                                    <span v-if="scope.row.fileurL3 !== null">
                                        {{ scope.row.filenamE3 }}
                                    </span>
                                    <span v-else style="color: red;">
                                        未上传文件
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column fixed="right" align="center" label="附件查看">
                                <template slot-scope="scope">
                                    <el-dropdown trigger="click" @command="handleCommand">
                                        <el-button type="text"><span style="color: red;">查看附件</span></el-button>
                                        <el-dropdown-menu slot="dropdown">
                                            <el-dropdown-item
                                                :command="{ action: 'downloadAttachment1', row: scope.row, attachmentName: '质量改善项目申报书' }">
                                                质量改善项目申报书
                                            </el-dropdown-item>
                                            <el-dropdown-item
                                                :command="{ action: 'downloadAttachment2', row: scope.row, attachmentName: '质量改善项目过程评价' }">
                                                质量改善项目过程评价
                                            </el-dropdown-item>
                                            <el-dropdown-item
                                                :command="{ action: 'downloadAttachment3', row: scope.row, attachmentName: '质量改善项目年终总结' }">
                                                质量改善项目年终总结
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </el-dropdown>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
                <!-- 附件查看drawer -->
                <el-drawer :title="titleDrawer" :visible.sync="drawerOpen" :before-close="handleCloseDra"
                    :direction="directionDra" size="85%">
                    <iframe :src="srcFileUrl" frameborder="no" style="width: 100%; height: 100%" scrolling="auto" />
                </el-drawer>
                <!-- 模板下载drawer -->
                <el-drawer :title="titleDrawerTemplate" :visible.sync="drawerTemplate" :before-close="handleCloseTemplate"
                    size="50%">
                    <h2>
                        质量改善项目申报书:
                        <el-link
                            href="http://localhost:8088/20250110/21d57707738841cbbab81b2d0568e06a/%E6%B2%B3%E5%8D%97%E5%AE%8F%E5%8A%9B%E5%8C%BB%E9%99%A2%E9%83%A8%E9%97%A8%E9%87%8D%E7%82%B9%E8%B4%A8%E9%87%8F%E6%94%B9%E5%96%84%E9%A1%B9%E7%9B%AE%E7%94%B3%E6%8A%A5%E4%B9%A6.pdf"
                            target="_blank">
                            质量改善项目申报书
                        </el-link>
                    </h2>
                    <h2>
                        质量改善项目过程评价:
                        <el-link
                            href="http://localhost:8088/20250110/21d57707738841cbbab81b2d0568e06a/%E6%B2%B3%E5%8D%97%E5%AE%8F%E5%8A%9B%E5%8C%BB%E9%99%A2%E9%83%A8%E9%97%A8%E9%87%8D%E7%82%B9%E8%B4%A8%E9%87%8F%E6%94%B9%E5%96%84%E9%A1%B9%E7%9B%AE%E7%94%B3%E6%8A%A5%E4%B9%A6.pdf"
                            target="_blank">
                            质量改善项目过程评价
                        </el-link>
                    </h2>
                    <h2>
                        质量改善项目年终总结:
                        <el-link
                            href="http://localhost:8088/20250110/21d57707738841cbbab81b2d0568e06a/%E6%B2%B3%E5%8D%97%E5%AE%8F%E5%8A%9B%E5%8C%BB%E9%99%A2%E9%83%A8%E9%97%A8%E9%87%8D%E7%82%B9%E8%B4%A8%E9%87%8F%E6%94%B9%E5%96%84%E9%A1%B9%E7%9B%AE%E7%94%B3%E6%8A%A5%E4%B9%A6.pdf"
                            target="_blank">
                            质量改善项目年终总结
                        </el-link>
                    </h2>
                </el-drawer>
                <!-- 新增dialog -->
                <el-dialog :visible.sync="dialogOpen" :title="titleDialogAdd" width="75%" :before-close="handleCloseAdd">
                    <el-form :inline="true" :model="queueFormAdd" :rules="rules" ref="dataForm">
                        <el-form-item label="质量改进项目名称:" prop="qualityImprovement">
                            <el-input placeholder="请输入质量改进项目名称" v-model="queueFormAdd.qualityImprovement" clearable />
                        </el-form-item>
                        <el-form-item label="科室名称:" prop="deptName">
                            <el-select v-model="queueFormAdd.deptName" filterable clearable placeholder="请选择">
                                <el-option v-for=" item  in  deptNameDict " :key="item.value" :label="item.label"
                                    :value="item.label">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="年度:" prop="yearTime">
                            <el-date-picker v-model="queueFormAdd.yearTime" type="year" placeholder="选择年"
                                value-format="yyyy" format="yyyy" />
                        </el-form-item>
                        <div>
                            <span>
                                <el-form-item>
                                    <input v-model="attachment1" type="text" class="unit_input" />
                                    <el-upload class="upload-demo" ref="upload" action="#" :multiple="false"
                                        :show-file-list="true" :before-upload="beforeUpload" accept=".pdf"
                                        :on-preview="handlePreview" :on-exceed="handleExceed" :on-change="onChange1"
                                        :file-list="fileList1" :auto-upload="false" :limit="10" drag>
                                        <i class="el-icon-upload"></i>
                                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                    </el-upload>
                                </el-form-item>
                            </span>
                            <span style="margin-left: 200px;">
                                <el-form-item>
                                    <input v-model="attachment2" type="text" class="unit_input" readonly />
                                    <el-upload class="upload-demo" ref="upload" action="#" :multiple="false"
                                        :show-file-list="true" :before-upload="beforeUpload" accept=".pdf"
                                        :on-preview="handlePreview" :on-exceed="handleExceed" :on-change="onChange2"
                                        :file-list="fileList2" :auto-upload="false" :limit="10" drag>
                                        <i class="el-icon-upload"></i>
                                        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                    </el-upload>
                                </el-form-item>
                            </span>
                        </div>
                        <div>
                            <el-form-item>
                                <input v-model="attachment3" type="text" class="unit_input" readonly />
                                <el-upload class="upload-demo" ref="upload" action="#" :multiple="false"
                                    :show-file-list="true" :before-upload="beforeUpload" accept=".pdf"
                                    :on-preview="handlePreview" :on-exceed="handleExceed" :on-change="onChange3"
                                    :file-list="fileList3" :auto-upload="false" :limit="10" drag>
                                    <i class="el-icon-upload"></i>
                                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                </el-upload>
                            </el-form-item>
                        </div>
                    </el-form>
                    <span slot="footer">
                        <el-button type="primary" @click="saveUpload">确 定</el-button>
                    </span>
                </el-dialog>
            </div>
        </div>
    </div>
</template>

<script>
import {
    GetHisDeptDict,
    ImprovementProjectFile,
    GetImprovementProjectFile,
    ImprovementProjectFileDel
} from "@/api/singlePage/qualityCommittee/improvementProject";
import {
    MeetingManagementFileOne
} from "@/api/singlePage/qualityCommittee/meetingManagement";
import env from '@/utils/ApiConfig';
export default {
    name: '',
    props: [],
    data() {
        return {
            envUrl: env.get_file_url(),
            deptNameDict: [], // 科室字典数据
            tableDate: [], // 主页table
            queueForm: { // 主页传递的参数
                qualityImprovement: '',
                deptName: '',
                yearTime: ''
            },
            dialogOpen: false, // 新增弹框标识
            titleDialogAdd: '新增弹框',
            queueFormAdd: {// 新增弹框传递的参数
                qualityImprovement: '',
                deptName: '',
                yearTime: ''
            },
            extraFunctionKey: 0,
            srcFileUrl: '', // 文件路径
            drawerOpen: false, // 查看附件弹框
            directionDra: 'btt', // 查看附件弹框打开方式 (ltr:从左往右开,rtl:从右往左开,ttb:从上往下开,btt:从下往上开,)
            titleDrawer: '文件查看', // 查看附件标题
            attachment1: '质量改善项目申报书上传',
            attachment2: '质量改善项目过程评价上传',
            attachment3: '质量改善项目年终总结上传',
            fileList1: [], // 质量改善项目申报书文件上传
            fileList2: [], // 质量改善项目过程评价文件上传
            fileList3: [], // 质量改善项目年终总结文件上传
            delForm: { // 删除按钮参数
            },
            drawerTemplate: false, // 模板下载弹框
            titleDrawerTemplate: '模版下载弹框',
            rules: {
                qualityImprovement: [
                    { required: true, message: '请输入质量改进项目名称', trigger: 'blur' },
                ],
                deptName: [
                    { required: true, message: '请选择科室名称', trigger: 'blur' },
                ],
                yearTime: [
                    { required: true, message: '请输入年度', trigger: 'blur' },
                ],
            }
        }
    },

    created() {
        this.getList();
        this.handleResize();
    },

    mounted() {
        window.addEventListener('resize', this.handleResize); // 添加监听器
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize); // 移除监听器
    },

    methods: {
        // 初始化数据
        getList() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            // 查询质量改进项目上传文件
            GetImprovementProjectFile(this.queueForm).then(res => {
                this.tableDate = res.data.list;
            }).finally(() => {
                loading.close();
            });
            // 获取科室数据
            GetHisDeptDict().then(res => {
                this.deptNameDict = res.data.getCommitteeNameDictRetVueModels;
            }).finally(() => {
                loading.close();
            });
        },

        // 删除按钮操作
        del() {
            if (this.delForm.fileid === undefined) {
                this.showMessageA("请选择要删除的数据！");
            } else {
                let fileid = this.delForm.fileid;
                this.$modal.confirm(
                    '确认要删除质量改进项目名称为: ' + this.delForm.qualityImprovement +
                    '科室为:' + this.delForm.deptName +
                    '年度为:' + this.delForm.yearTime +
                    '的数据吗?')
                    .then(function () {
                        return ImprovementProjectFileDel(fileid);
                    })
                    .then(() => {
                        this.showMessageA("删除数据成功");
                        this.reset();
                        this.getList();
                    })
                    .catch(() => {
                        this.reset();
                        this.showMessageA("删除数据失败");
                    });
            }
        },

        // 新增按钮操作
        add() {
            this.dialogOpen = true;
        },

        // 新增弹框确定按钮操作
        saveUpload: function () {
            this.$refs["dataForm"].validate((valid) => {
                if (valid) {
                    // 判断是否上传文件
                    if (this.fileList1.length === 0) {
                        this.showMessageA("请上传附件1文件");
                    } else if (this.fileList2.length === 0) {
                        this.showMessageA("请上传附件2文件");
                    } else if (this.fileList3.length === 0) {
                        this.showMessageA("请上传附件3文件");
                    } else {
                        let formData = new FormData();
                        formData.append('qualityImprovement', this.queueFormAdd.qualityImprovement);
                        formData.append('deptName', this.queueFormAdd.deptName);
                        formData.append('yearTime', this.queueFormAdd.yearTime);
                        formData.append('fileUrlA', this.fileList1[0].fileUrlA.split("wwwroot")[1]);
                        formData.append('fileNameA', this.fileList1[0].fileNameA);
                        formData.append('attachmentA', this.fileList1[0].attachmentA);
                        formData.append('fileUrlB', this.fileList2[0].fileUrlB.split("wwwroot")[1]);
                        formData.append('fileNameB', this.fileList2[0].fileNameB);
                        formData.append('attachmentB', this.fileList2[0].attachmentB);
                        formData.append('fileUrlC', this.fileList3[0].fileUrlC.split("wwwroot")[1]);
                        formData.append('fileNameC', this.fileList3[0].fileNameC);
                        formData.append('attachmentC', this.fileList3[0].attachmentC);
                        ImprovementProjectFile(formData).then((response) => {
                            if (response.code == 200 && response.data === null) {
                                this.showMessageA("上传文件成功");
                                this.fileList1 = [];
                                this.fileList2 = [];
                                this.fileList3 = [];
                                this.queueFormAdd = {};
                                this.dialogOpen = false;
                                this.getList();
                            } else if (response.data === '无') {
                                this.showMessageA("已存在,请勿重复添加!!!");
                            } else {
                                this.showMessageA("上传文件失败,请联系信息科!!!");
                            }
                        }).catch((error) => {
                            // 处理错误
                        });
                    }
                }
            });
        },

        /** 文件上传功能限制 */
        // 文件上传-限制文件大小(公共)
        beforeUpload(file) {
            const isLt10M = file.size / 1024 < 50;
            if (!isLt10M) {
                this.$message.error('上传文件过大 ');
                return false;
            }
        },
        // 附件上传-上传数量提示 共选择了
        handleExceed(files, fileList) {
            this.$message.warning(`当前最多选择 10 个文件上传,超出文件最大数量限制`);
        },
        // 附件1上传-文件选取完文件触发事件
        onChange1(file, fileList1) {
            let formData = new FormData();
            formData.append('files[]', file.raw);
            formData.append('fileName', file.name);
            MeetingManagementFileOne(formData).then(res => {
                this.fileList1.push({
                    fileUrlA: res.data.path,
                    fileNameA: res.data.fileName,
                    attachmentA: '质量改善项目申报书上传'
                });
            });
        },
        // 附件2上传-文件选取完文件触发事件
        onChange2(file, fileList2) {
            let formData = new FormData()
            formData.append('files[]', file.raw);
            formData.append('fileName', file.name);
            MeetingManagementFileOne(formData).then(res => {
                this.fileList2.push({
                    fileUrlB: res.data.path,
                    fileNameB: res.data.fileName,
                    attachmentB: '质量改善项目过程评价上传'
                })
            });
        },
        // 附件3上传-文件选取完文件触发事件
        onChange3(file, fileList3) {
            let formData = new FormData()
            formData.append('files[]', file.raw);
            formData.append('fileName', file.name);
            MeetingManagementFileOne(formData).then(res => {
                this.fileList3.push({
                    fileUrlC: res.data.path,
                    fileNameC: res.data.fileName,
                    attachmentC: '质量改善项目年终总结上传'
                })
            });
        },
        handlePreview(file) { },

        // 附件查看操作
        handleCommand(command) {
            if (command.action === 'downloadAttachment1' || command.action === 'downloadAttachment2'
                || command.action === 'downloadAttachment3' || command.action === 'downloadAttachment4') {
                // 使用command对象中的信息来处理下载逻辑
                this.downloadAttachment(command.row, command.attachmentName);
            }
        },
        downloadAttachment(row, attachmentName) {
            // 接收附件名称和改行的数据
            if (attachmentName === '质量改善项目申报书' && row.fileurL1 !== null) {
                ++this.extraFunctionKey;
                this.drawerOpen = true;
                this.srcFileUrl = this.envUrl + row.fileurL1; // 获取文件路由
            } else if (attachmentName === '质量改善项目过程评价' && row.fileurL2 !== null) {
                ++this.extraFunctionKey;
                this.drawerOpen = true;
                this.srcFileUrl = this.envUrl + row.fileurL2; // 获取文件路由
            } else if (attachmentName === '质量改善项目年终总结' && row.fileurL3 !== null) {
                ++this.extraFunctionKey;
                this.drawerOpen = true;
                this.srcFileUrl = this.envUrl + row.fileurL3; // 获取文件路由
            }
        },

        // 模板下载按钮操作
        templateDownload() {
            this.drawerTemplate = true;
        },

        // 首页按钮操作
        indexOne() {
            this.$router.push('/singlePage/qualityCommittee/transfer')
        },

        // 重置按钮操作
        reset() {
            this.queueForm = {};
            this.queueFormAdd = {};
        },

        // 新增弹框取消提示
        handleCloseAdd(done) {
            this.$confirm('确认关闭新增弹框吗')
                .then(_ => {
                    done();
                    this.reset();
                    this.getList();
                })
                .catch(_ => { });
        },

        // 附件查看弹框关闭提示消息
        handleCloseDra(done) {
            this.$confirm('确认关闭文件查看弹框吗')
                .then(_ => {
                    done();
                    this.reset();
                    this.getList();
                })
                .catch(_ => { });
        },

        // 模版下载弹框关闭提示消息
        handleCloseTemplate(done) {
            this.$confirm('确认关闭模版下载弹框吗')
                .then(_ => {
                    done();
                    this.reset();
                    this.getList();
                })
                .catch(_ => { });
        },

        // 单击某一行操作
        handleRowClick(row) {
            this.delForm.fileid = row.fileid;
            this.delForm.qualityImprovement = row.qualityimprovement;
            this.delForm.yearTime = row.yeartime;
            this.delForm.deptName = row.deptname;
        },

        // 提示信息封装
        showMessageA(message) {
            const h = this.$createElement;
            this.$notify({
                title: '提示信息！',
                message: h('i', { style: 'color: teal' }, message)
            });
        },

        // 自定义高度变化更新高度
        handleResize() {
            this.tableHeight = window.innerHeight; // 更新高度数据
        },
    }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.my-table {
    ::v-deep.el-table--medium .el-table__cell {
        padding: 0;
    }

    ::v-deep.el-table .el-table__header-wrapper th,
    .el-table .el-table__fixed-header-wrapper th {
        word-break: break-word;
        background-color: #4f617238;
        color: #303133;
        height: 30px;
        font-size: 10px;
    }

    ::v-deep.el-table th.el-table__cell>.cell {
        padding: 0;
    }

    ::v-deep.el-table--border .el-table__cell:first-child .cell {
        padding: 0;
    }

    ::v-deep.el-button+.el-button {
        margin-left: 2px;
    }

    ::v-deep.el-table .cell {
        padding: 1px;
    }

    // 滚动条的滑块
    ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: rgb(13, 192, 132);
        border-radius: 1px;
    }
}

.scroll-container {
    height: 550px;
    /* 设置容器的高度 */
}

.unit_input {
    border: none;
    border-bottom: 0px solid rgb(0, 0, 0);
    outline: none;
    width: 200px;
    font-size: 15px;
    font-weight: 100px;
    margin-left: 3px;
}
</style>

<style>
.el-upload-dragger {
    width: 193px;
    height: 136px;
}
</style>