import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
 noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
 icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
 breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
 activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
 }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect"),
      },
    ],
  },
  {
    path: "/appointmentRegister",
    component: () => import("@/views/appointment/register"),
    meta: {
      title: "医生端住院预约检查平台",
      icon: "dashboard",
      affix: true,
    },
  },
  {
    path: "/appointmentOutpatient",
    component: () => import("@/views/appointment/outpatient"),
    meta: {
      title: "医生端门诊预约检查平台",
      icon: "dashboard",
      affix: true,
    },
  },
  {
    path: "/outpPatologyApply",
    component: () => import("@/views/appointment/outpPatologyApply"),
    meta: {
      title: "医生端门诊预约检查平台",
      icon: "dashboard",
      affix: true,
    },
  },
  {
    path: "/reservationCenter",
    component: () => import("@/views/appointment/reservationCenter"),
    meta: {
      title: "预约中心",
      icon: "dashboard",
      affix: true,
    },
  },
  {
    path: "/ward-appoint",
    component: () => import("@/views/appointment/wardAppoint"),
    meta: {
      title: "病区预约检查列表",
      icon: "dashboard",
      affix: true,
    },
  },
  {
    path: "/queueItemDict",
    component: () => import("@/views/appointment/queueItemDict"),
    meta: {
      title: "预约中心配置",
      icon: "dashboard",
      affix: true,
    },
  },
  {
    path: "/reservationCenterNurseHospital",
    component: () => import("@/views/appointment/reservationCenterIn"),
    meta: {
      title: "住院护士站预约中心",
      icon: "dashboard",
      affix: true,
    },
  },
  {
    path: "/oneBed",
    component: () => import("@/views/oneBed/oneBedIndex"),
    meta: {
      title: "全院一张床",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/",
    component: () => import("@/views/checkAndConfirm/index"),
    meta: {
      title: "检查确认首页",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/inspectionResult/register",
    component: () => import("@/views/singlePage/inspectionResult/register"),
    meta: {
      title: "河南宏力医院检查检验结果线下互认登记备查本",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/maternalDataReporting/inquire",
    component: () => import("@/views/singlePage/maternalDataReporting/inquire"),
    meta: {
      title: "孕产妇数据上报窗口",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/emergencyClinic/dispatchRoomPatient",
    component: () => import("@/views/singlePage/emergencyClinic/dispatchRoomPatient"),
    meta: {
      title: "急诊科调度室患者查询",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/contagion/reportCardReview",
    component: () => import("@/views/singlePage/contagion/reportCardReview"),
    meta: {
      title: "传染病报告卡审核",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/parasite/parasiteIndex",
    component: () => import("@/views/singlePage/parasite/parasiteIndex"),
    meta: {
      title: "寄生虫查询",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/positiveReport/inquire",
    component: () => import("@/views/singlePage/positiveReport/inquire"),
    meta: {
      title: "阳性上报",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/tuberculosisStatement/inquire",
    component: () => import("@/views/singlePage/tuberculosisStatement/inquire"),
    meta: {
      title: "结合报表",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/drugEntry/inquire",
    component: () => import("@/views/singlePage/drugEntry/inquires"),
    meta: {
      title: "药品录入",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/pdfSelect",
    component: () => import("@/views/singlePage/pdf/pdfSelect"),
    meta: {
      title: "pdf报告查看",
      icon: "dashboard",
      affix: true,
    },
  },  {
    path: "/singlePage/examAppointsList",
    component: () => import("@/views/singlePage/queue/queuePageList"),
    meta: {
      title: "医技科室患者排队信息",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/queueInfo",
    component: () => import("@/views/singlePage/queue/ExamAppointsPageList"),
    meta: {
      title: "医技科室患者排队信息",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/drugStatement/inquires",
    component: () => import("@/views/singlePage/drugStatement/inquires"),
    meta: {
      title: "药品报表",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/drugUsageDetails/index",
    component: () => import("@/views/singlePage/drugUsageDetails/index"),
    meta: {
      title: "药品使用明细",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/tumorReportCard/index",
    component: () => import("@/views/singlePage/tumorReportCard/index"),
    meta: {
      title: "肿瘤报卡查询",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/newbornStatistics/index",
    component: () => import("@/views/singlePage/newbornStatistics/index"),
    meta: {
      title: "新生儿报表",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/refundBank/index",
    component: () => import("@/views/singlePage/refundBank/index"),
    meta: {
      title: "退款银行登记列表",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/waterElectricityFee/index",
    component: () => import("@/views/singlePage/waterElectricityFee/index"),
    meta: {
      title: "职工水电费缴费情况查询",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/diagnosedInfo/index",
    component: () => import("@/views/singlePage/diagnosedInfo/index"),
    meta: {
      title: "医生工作站》》已诊患者信息查询",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/drugProcurementPlan/index",
    component: () => import("@/views/singlePage/drugProcurementPlan/index"),
    meta: {
      title: "药品采购计划",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/test",
    component: () => import("@/views/singlePage/test"),
    meta: {
      title: "测试页面",
      icon: "交换中心",
      affix: true,
    },
  }, {
    path: "/exchangePlatform/index",
    component: () => import("@/views/singlePage/exchangePlatform/index"),
    meta: {
      title: "交换中心",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/oa/lkoa/newsAudit/index",
    component: () => import("@/views/singlePage/journalismAudit/index"),
    meta: {
      title: "新闻审核（公司）",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/oa/lkoa/newsAudit/outerNet",
    component: () => import("@/views/singlePage/journalismAudit/outerNet"),
    meta: {
      title: "新闻审核（外网）",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/qualityCommittee/transfer",
    component: () => import("@/views/singlePage/qualityCommittee/transfer"),
    meta: {
      title: "委员会管理模块路由跳转",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/qualityCommittee/systemManagement",
    component: () => import("@/views/singlePage/qualityCommittee/systemManagement"),
    meta: {
      title: "制度管理模块",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/qualityCommittee/problemManagement",
    component: () => import("@/views/singlePage/qualityCommittee/problemManagement"),
    meta: {
      title: "问题管理模块",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/qualityCommittee/meetingManagement",
    component: () => import("@/views/singlePage/qualityCommittee/meetingManagement"),
    meta: {
      title: "会议管理模块",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/qualityCommittee/improvementProject",
    component: () => import("@/views/singlePage/qualityCommittee/improvementProject"),
    meta: {
      title: "质量改进项目模块",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/qualityCommittee/frameworkManagement",
    component: () => import("@/views/singlePage/qualityCommittee/frameworkManagement"),
    meta: {
      title: "框架管理模块",
      icon: "dashboard",
      affix: true,
    },
  }, {
    path: "/singlePage/test/index",
    component: () => import("@/views/singlePage/test/index"),
    meta: {
      title: "测试布局界面",
      icon: "dashboard",
      affix: true,
    },
  },{
    path: "/singlePage/nurseRefundConfigCenter/index",
    component: () => import("@/views/singlePage/nurseRefundConfigCenter/index"),
    meta: {
      title: "测试布局界面",
      icon: "dashboard",
      affix: true,
    },
  },{
    path: "/singlePage/labTestEmergencyDept/index",
    component: () => import("@/views/singlePage/labTestEmergencyDept/index"),
    meta: {
      title: "急诊科检验项目确认",
      icon: "dashboard",
      affix: true,
    },
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  // {
  //   path: "",
  //   component: Layout,
  //   redirect: "index",
  //   children: [
  //     {
  //       path: "index",
  //       component: () => import("@/views/index"),
  //       name: "Index",
  //       meta: { title: "首页", icon: "dashboard", affix: true },
  //     },
  //   ],
  // },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:user:edit"],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: { title: "分配角色", activeMenu: "/system/user" },
      },
    ],
  },
  {
    path: "/system/role-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:role:edit"],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/system/role/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户", activeMenu: "/system/role" },
      },
    ],
  },
  {
    path: "/system/dict-data",
    component: Layout,
    hidden: true,
    permissions: ["system:dict:list"],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/system/dict" },
      },
    ],
  },
  {
    path: "/monitor/job-log",
    component: Layout,
    hidden: true,
    permissions: ["monitor:job:list"],
    children: [
      {
        path: "index/:jobId(\\d+)",
        component: () => import("@/views/monitor/job/log"),
        name: "JobLog",
        meta: { title: "调度日志", activeMenu: "/monitor/job" },
      },
    ],
  },
  {
    path: "/tool/gen-edit",
    component: Layout,
    hidden: true,
    permissions: ["tool:gen:edit"],
    children: [
      {
        path: "index/:tableId(\\d+)",
        component: () => import("@/views/tool/gen/editTable"),
        name: "GenEdit",
        meta: { title: "修改生成配置", activeMenu: "/tool/gen" },
      },
    ],
  },
];

// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((err) => err);
};
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch((err) => err);
};

export default new Router({
  mode: "history", // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
});
