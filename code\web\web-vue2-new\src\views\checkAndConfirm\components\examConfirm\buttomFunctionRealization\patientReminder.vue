<template>
  <div class="single-master">
    <el-dialog
      :visible.sync="reminderStatus"
      :title="title"
      width="70%"
    >
      <div class="element-table">
        <el-table :data="tableDate" style="width: 100%" max-height="550" border>
          <el-table-column prop="msG_NO" align="center" label="编号" width="60"></el-table-column>
          <el-table-column prop="msG_TXT" align="center" label="提醒结果"></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {VerifyPatientReminder} from "@/api/checkAndConfirm/checkCommon"
/**
 * 患者提醒校验
 */
export default {
  name: 'patientReminder',
  props: [],
  components: {},
  data() {
    return {
      title: '患者提醒',
      reminderStatus: false,
      tableDate: []
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    verify(patientId) {
      if (patientId) {
        this.reminderTrigger(patientId)
      }
    },
    reminderTrigger(patientId) {
      VerifyPatientReminder(patientId).then(t => {
        if (t.code === 200){
          let data = t.data;
          if (data.length > 0){
            this.tableDate = data;
            this.reminderStatus = true;
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../../../assets/styles/singlePage";
</style>
