<template>
  <div class="returnPremium-home">
    <el-dialog
      title="项目退费"
      :visible.sync="returnPremiumStatus"
      width="75%"
    >
      <div v-if="outpPatient" class="return-outp-table">
        <div class="outp-title">
          <div class="basics">基本信息：</div>
          <div class="title-item">
            <div>ID号：</div>
            <div class="item-text">{{examData.patient[0].patientid}}</div>
          </div>
          <div class="title-item">
            <div>姓名：</div>
            <div class="item-text">{{examData.patient[0].name}}</div>
          </div>
          <div class="title-item">
            <div>金额：</div>
            <div class="item-text">{{examData.patient[0].totalcharges}}</div>
          </div>
        </div>
        <div class="outp-master-table">
          <div class="top-left-table">
            <el-table :data="examData.examItemList" highlight-current-row @row-click="examItemTableClick"
                      border style="width: 100%" height="317">
              <el-table-column align="center" prop="examNo" label="检查号" width="100"></el-table-column>
              <el-table-column align="center" prop="examItemNo" label="序号" width="45"></el-table-column>
              <el-table-column align="center" prop="examItem" label="项目" ></el-table-column>
              <el-table-column align="center" prop="costs" label="金额" width="65"></el-table-column>
            </el-table>
          </div>
          <div class="top-right-table">
            <el-table :data="examData.outpBillItemsList" border style="width: 100%" height="317">
              <el-table-column align="center" prop="rcptNo" label="流水号" width="150"></el-table-column>
              <el-table-column align="center" prop="itemName" label="费用明细"></el-table-column>
            </el-table>
          </div>
          <div class="button-left-table">
            <el-table :data="examData.examBillItemList" border style="width: 100%" height="237">
              <el-table-column align="center" prop="itemName" label="收费项目" ></el-table-column>
              <el-table-column align="center" prop="costs" label="金额" width="80"></el-table-column>
            </el-table>
          </div>
          <div class="button-right-table">
            <div class="right-table" >
              <el-table :data="examData.cause" highlight-current-row @row-click="memoTableClick"
                        border style="width: 100%" height="237">
                <el-table-column align="center" prop="no" label="序号" width="60"></el-table-column>
                <el-table-column align="center" prop="memo" label="退费原因" ></el-table-column>
              </el-table>
            </div>
            <div class="right-input">
              <div class="input-title">退费原因</div>
              <el-input
                type="textarea"
                :autosize="{ minRows: 9, maxRows: 15}"
                placeholder="请选择左侧退费原因"
                v-model="textarea2">
              </el-input>
            </div>
          </div>
        </div>
        <div class="outp-button">
          <div>
            <el-button v-if="examData.examItem && textarea2" type="primary" style="width: 120px;" @click="returnPremiumOutp">退费</el-button>
            <el-button v-else type="primary" style="width: 120px;" disabled>退费</el-button>
          </div>

          <el-button type="danger" @click="returnPremiumStatus = false">关闭</el-button>
        </div>
      </div>
      <div v-if="inHospitalPatient" class="return-in-table">
        <el-table ref="returnPremiumInRef" :data="examData.inHospital" @selection-change="InSelectCharge" border style="width: 100%" height="350">
          <el-table-column align="center" type="selection" width="55"></el-table-column>
          <el-table-column align="center" prop="iteM_CLASS" label="类别" width="60">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.iteM_CLASS === 'D'" type="success">检查</el-tag>
              <el-tag v-if="scope.row.iteM_CLASS === 'A'" type="success">西药</el-tag>
              <el-tag v-if="scope.row.iteM_CLASS === 'E'" type="success">治疗</el-tag>
              <el-tag v-if="scope.row.iteM_CLASS === 'I'" type="success">材料</el-tag>
              <el-tag v-if="scope.row.iteM_CLASS === 'Z'" type="success">其他</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="iteM_NAME" label="项目名称"></el-table-column>
          <el-table-column align="center" prop="iteM_SPEC" label="规格" width="80"></el-table-column>
          <el-table-column align="center" prop="amount" label="数量" width="50"></el-table-column>
          <el-table-column align="center" prop="units" label="单位" width="120"></el-table-column>
          <el-table-column align="center" prop="charges" label="应收" width="80"></el-table-column>
          <el-table-column align="center" prop="costs" label="实收" width="80"></el-table-column>
        </el-table>
        <div class="return-button">
          <el-button v-if="inHospitalSaveStatus" type="primary" style="width: 120px;" @click="returnPremiumIn">退费</el-button>
          <el-button v-else-if="!inHospitalSaveStatus" type="primary" style="width: 120px;" disabled>退费</el-button>
          <el-button type="danger" @click="returnPremiumStatus = false">关闭</el-button>
        </div>
      </div>
      <div>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  ReturnPremiumParameterInit,
  InHospitalReturnPremium,
  ReturnPremiumOutpTableClickData,
  OutpHospitalReturnPremium,
  CancelCallQueueMessage} from "@/api/checkAndConfirm/returnPremium"
export default {
  name: 'returnPremium',
  props: [],
  components: {},
  data() {
    return {
      textarea2: '',
      returnPremiumStatus:false,
      examData:{},
      inHospitalPatient: false,
      outpPatient: false,
      inHospitalPitchOnData: [],
      inHospitalSaveStatus: false,
      defaultRowKey: '1',
    }
  },
  methods: {
    memoTableClick(row){
      this.textarea2 = row.memo;
    },
    examItemTableClick(row){
      this.examData.examItem = row;
      ReturnPremiumOutpTableClickData(row.examNo,row.examItemNo).then(res => {
        this.examData.examBillItemList = res.data.examBillItemList;
        this.examData.outpBillItemsList = res.data.outpBillItemsList;
      })
    },
    InSelectCharge(val){
      this.inHospitalSaveStatus = val && val.length > 0;
      this.inHospitalPitchOnData = val;
    },
    returnPremiumOutp(){
      //门诊退费
      let examData = this.examData;
      let examItemData = this.examData.examItem;
      this.$confirm('确定要对患者：' + examData.patient[0].name + '的项目《' + examItemData.examItem+'》进行退费？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          exam_no: examItemData.examNo,
          exam_item_no: examItemData.examItemNo,
          refund_reason: this.textarea2,
          operator_emp_no: this.$store.getters.empNo,
          rcpt_no: examData.outpNo,
        }
        OutpHospitalReturnPremium(data).then(res => {
          this.$message.success(res.message);
          // 直接打印
          this.cancelCallQueue(examItemData.examNo,examData.patient[0].patientId);
          let pintData = [];
          pintData.push({
            name: examData.patient[0].name,
            performed_by: this.$store.getters.rowTable.performedByName,
            rcpt_no: examData.outpNo,
            create_time: this.formatDate(),
            costs: examItemData.costs,
            item_name: examItemData.examItem,
            refund_reason: this.textarea2,
          })
          this.$emit("refund_print",pintData)
          //如有需要退费成功后  刷新列表信息

        })
      })

    },
    returnPremiumIn(){
      let data = this.$store.getters.rowTable;
      if (data){
        if (data.examNo && this.inHospitalSaveStatus){
          let pitchOnData = this.inHospitalPitchOnData;
          let returnData = {
            exam_no: data.examNo,
            operator_emp_no: this.$store.getters.empNo,
            bill_items: [],
          }
          let billItems = [];
          pitchOnData.forEach(t => {
            billItems.push({
              exam_item_no: t.exaM_ITEM_NO,
              charge_item_no: t.chargE_ITEM_NO,
            })
          })
          returnData.bill_items = billItems;
          //调用退费接口
          InHospitalReturnPremium(returnData).then(res => {
            if (res.code === 200){
              this.$message.success(res.message);
              this.cancelCallQueue(data.examNo,data.patientId);
              //如有需要，退费成功后刷新列表
            }
          })
        }
      }
    },
    returnPremiumInitVerify(status,data){
      if (status){
        if (data){
          if (data.patientSource === '3'){
            this.$msgbox.alert(
              '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
              '当前检查项目来源为体检部开单,请前往体检部进行退费!!!' + '</div>',
              '系统提示',
              {
                confirmButtonText: '确定',
                type: 'warning',
                dangerouslyUseHTMLString: true
              }).then(() => { })
          }else{
            this.returnPremiumDataGain(data);
          }
        }
      }
    },
    returnPremiumDataGain(data){
      const loading = this.$loading({
        lock: true,
        text: '休息一下,数据正在努力加载中(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let patientSource = data.patientSource;
      let formData = {
        PatientSource: patientSource,
        ExamNo: data.examNo,
        PatientId: data.patientId,
        // DeptCode: this.$store.getters.deptCode,
        DeptCode: '0203',
      }
      ReturnPremiumParameterInit(formData).then(res => {
        this.examData = res.data;
        if (patientSource === '2'){
          this.inHospitalPatient = true;
          this.outpPatient = false;
        }else{
          this.inHospitalPatient = false;
          this.outpPatient = true;
        }
        this.returnPremiumStatus = true;
      }).finally(() => {
        loading.close();
      })
    },
    formatDate() {
      // 如果没有传入date，则使用当前时间
      let date = new Date();
      // 获取年月日时分秒
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const seconds = date.getSeconds().toString().padStart(2, '0');
      // 拼接成所需格式
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    cancelCallQueue(examNo,patientId){
      if (examNo && patientId){
        CancelCallQueueMessage(examNo,patientId).then(res => {
          this.$message.success(res.message);
        })
      }
    },
  }
}
</script>

<style scoped lang="scss">
.returnPremium-home{

  .return-outp-table{
    height: 650px;

    .outp-title{
      height: 40px;
      border: 1px solid #00a19b;
      background-color: #F0F0F0;
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 650;

      .basics{
        margin-left: 10px;
        width: 10%;
      }
      .title-item{
        display: flex;
        width: 20%;
        justify-content: space-evenly;
        .item-text{
          display: flex;
          align-items: flex-end;
        }
      }
    }
    .outp-master-table{
      margin-top: 5px;
      height: 560px;
      border: 1px solid #00a19b;
      display: flex;
      flex-wrap: wrap;
      .top-left-table{
        height: 320px;
        width: 40%;
        border: 1px solid #00a19b;
      }
      .top-right-table{
        height: 320px;
        width: 60%;
        border: 1px solid #00a19b;
      }
      .button-left-table{
        height: 240px;
        border: 1px solid #00a19b;
        width: 40%;
      }
      .button-right-table{
        height: 240px;
        border: 1px solid #00a19b;
        width: 60%;
        display: flex;
        .right-table{
          width: 60%;
        }
        .right-input{
          height: 240px;
          width: 40%;
          .input-title{
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            color: #FFFFFF;
            background-color: #185F7D;
            height: 35px;
            border-bottom: 2px solid #00a19b;
          }
        }
      }
    }
    .outp-button{
      display: flex;
      justify-content: flex-end;
      padding-top: 5px;
    }
  }

  .return-in-table{

    .return-button{
      display: flex;
      justify-content: flex-end;
      padding-top: 10px;
    }
  }
  ::v-deep.el-dialog__body {
    padding: 5px;
  }
  ::v-deep.el-table--medium .el-table__cell {
    padding: 5px 0;
  }
  ::v-deep.el-table th.el-table__cell > .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
  ::v-deep.el-table .el-table__header-wrapper th, .el-table .el-table__fixed-header-wrapper th {
    height: 30px;
  }
  ::v-deep.el-table__body tr.current-row > td.el-table__cell, .el-table__body tr.selection-row > td.el-table__cell {
    background-color: #1890FF;
    color: #FFFFFF;
  }
}
</style>
