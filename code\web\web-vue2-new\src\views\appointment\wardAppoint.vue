<template>
  <div class="app-container">
    <el-breadcrumb
      separator-class="el-icon-arrow-right"
      style="margin-bottom: 15px"
    >
      <el-breadcrumb-item :to="{ path: '#' }"
        >一站式预约检查</el-breadcrumb-item
      >
      <el-breadcrumb-item :to="{ path: '/ward-appoint' }"
        >病区预约患者列表</el-breadcrumb-item
      >
    </el-breadcrumb>
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="查询日期:">
        <el-date-picker
          v-model="queryParams.appointDate"
          type="date"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          placeholder="当前日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="患者姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入患者姓名"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="患者Id" prop="patientId">
        <el-input
          v-model="queryParams.patientId"
          placeholder="请输入患者Id"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="dataList"
      ref="table"
      @selection-change="handleSelectionChange"
      :row-class-name="tableRowClassName"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="检查申请号" align="center" prop="examNo" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="性别" align="center" prop="sex" />
      <el-table-column
        label="出生日期"
        align="center"
        prop="dateOfBirth"
        width="160"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.dateOfBirth, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="患者Id" align="center" prop="patientId" />
      <el-table-column label="检查类别" align="center" prop="examSubClass" />
      <el-table-column label="检查名称" align="center" prop="examItem" />
      <el-table-column label="科室名称" align="center" prop="deptName" />
      <el-table-column label="床号" align="center" prop="bedNo" />
      <el-table-column label="主要诊断" align="center" prop="diagnosis" />
      <el-table-column label="病情" align="center" prop="patientCondition">
        <template slot-scope="scope">
          <span style="color: red">{{ scope.row.patientCondition }}</span>
        </template>
      </el-table-column>
      <el-table-column label="主管医师" align="center" prop="doctorInCharge" />
      <el-table-column label="执行科室" align="center" prop="executeDeptName" />
      <el-table-column label="预约日期" align="center" prop="appointmentDate">
        <template slot-scope="scope">
          <span style="color: blue">{{ scope.row.appointmentDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预约时间" align="center" prop="appointmentTime">
        <template slot-scope="scope">
          <span style="color: blue">{{ scope.row.appointmentTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="报告状态" align="center" prop="resultStatus">
        <template slot-scope="scope">
          <span style="color: red">{{ scope.row.resultStatus }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { GetWardAppointInfo } from "@/api/appointment/appointInpExam";
export default {
  name: "ward-appoint",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        wardCode: undefined,
        patientId: undefined,
        name: undefined,
        appointDate: undefined,
      },
      //列表
      dataList: [],
    };
  },
  created() {
    this.queryParams.wardCode = this.$route.query && this.$route.query.wardCode;
    this.getList();
  },
  methods: {
    tableRowClassName({ row, rowIndex }) {
      if (row.patientCondition === "普通") {
        return "success-row";
      } else if (row.patientCondition === "病重") {
        return "warning-row";
      } else if (row.patientCondition === "病危") {
        return "danger-row";
      }
      return "";
    },
    /**
     * 获取肺功能Pdf列表
     */
    getList() {
      this.loading = true;
      GetWardAppointInfo(this.queryParams).then((response) => {
        this.loading = false;
        if (response.data) {
          this.dataList = response.data.rows;
          this.total = response.data.total;
        }
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.examNo);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },

    /**
     * 查询按钮
     */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
<style>
.el-table .warning-row {
  background: oldlace;
}

.el-table .success-row {
  background: #f0f9eb;
}
.el-table .danger-row {
  background: pink;
}
</style>
