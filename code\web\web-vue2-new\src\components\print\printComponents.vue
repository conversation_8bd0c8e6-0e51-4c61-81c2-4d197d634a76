<template>
  <div>
    <div>
      <el-form :inline="true" :model="totalModel" class="demo-form-inline">
        <el-form-item label="打印每页行数">
          <el-input-number v-model="myTotalSize" :min="1" :max="50"></el-input-number>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="totalSizeClick">确定</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-scrollbar style="width: 100%; overflow-x: hidden" :style="tableHeight">
      <div id="print-box" ref="printArea">
        <table v-for="(i, j) in printDataList" :key="j" class="my-table"
               :style="'font-size:' + 1.3 + 'vw'+(j==printDataList.length-1 ? 'page-break-after: always;':'')"
        >
          <thead>
          <tr>
            <td :colspan="tableTitle.length + 1" style="border: none;">
              <div v-html="titleHtml"></div>
            </td>
          </tr>
          <tr>
            <td :colspan="tableTitle.length + 1" style="border: none;">
              <div style="display: flex;align-items: center;">
                <div style="text-align: center; font-size: 1.3vw;width: 89%;">
                  <div v-html="pageHtml"></div>
                </div>
                <div style="font-size: 1.3vw;">第{{ j + 1 }}页(共{{ totalPage }}页)</div>
              </div>
              <div
                style="text-align: start;display: flex; flex-wrap: wrap;justify-content: space-between;  "
              >
              </div>
            </td>
          </tr>
          <tr>
            <th style="width: 2vw;"></th>
            <th v-for="(item,index) in tableTitle" :key="item.key">{{ item.title }}</th>
          </tr>
          </thead>
          <tbody :style="'font-size: 0.8vw;'">
          <tr v-for="(item, index) in i || []" :key="index">
            <td>{{ index + 1 }}</td>
            <td v-for="child in tableTitle" :key="child.key"
                :style="'width:' + (child.width ? child.width : '10vw;')"
            >
              <span :style="'font-weight:' + (child.fontWeight ? child.fontWeight : '')">{{ item[child.key] }}</span>
            </td>
          </tr>

          </tbody>

          <tfoot>
          <tr v-if="isMerge">
            <td :colspan="mergeSize">合计</td>
            <td style="font-weight: 600;font-size: 0.9vw;" v-for="(item,index) in mergeData" :key="index">
              <span v-if="item[j]">{{ item[j] }}</span>
            </td>
          </tr>
<!--          <div v-if="isSpecialMerge" v-for="(x,index) in specialMergeData.mergeColumn" :key="index">-->
<!--            <tr v-for="(item,indexTwo) in specialMergeData.mergeLine" :key="indexTwo">-->
<!--              <td :colspan="specialMergeData.mergeSize" :style="specialMergeData.mergeStyle">-->
<!--                <span v-if="item[x][item]">{{ item[x][item] }}</span>-->
<!--              </td>-->
<!--            </tr>-->
<!--          </div>-->
          <tr v-if="bottomHtml">
            <td :colspan="tableTitle.length + 1" v-html="bottomHtml" style="border: none;font-size: 1vw;"></td>
          </tr>
          </tfoot>
        </table>

      </div>
    </el-scrollbar>
    <div class="event-button">
      <el-button type="primary" circle v-print="printModel">打印</el-button>
    </div>
  </div>

</template>

<script>
export default {
  name: 'printComponents',
  props: [
    'printDataList',
    'tableTitle',
    'pageHtml',
    'totalPage',
    'totalSize',
    'mergeData',
    'isMerge',
    'titleHtml',
    'bottomHtml',
    'mergeSize',],
  components: {},
  data() {
    return {
      // printData: {
      //   printDataList: [],//打印数据
      //   tableTitle: [],//标题数据
      //   pageHtml: '',//（需要带样式,默认居中）
      //   totalPage: 0,//总行数
      //   totalSize: 20,//打印页每页显示条数
      //   mergeData: [],//合并数据
      //   isMerge: false,//是否合并
      //   titleHtml: '',//每页title文字（需要带样式）
      //   bottomHtml: ''//每页底部文字（需要带样式）
      //   mergeSize: 2 //合并行数，序号列1
      // },
      dialogVisible: false,
      printModel: {
        id: 'print-box',
        popTitle: '打印', // 打印配置页上方标题
        extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
        preview: false, // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
        previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
        previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
        zIndex: 20002, // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
        previewBeforeOpenCallback() {
        }, //预览窗口打开之前的callback（开启预览模式调用）
        previewOpenCallback() {
        }, // 预览窗口打开之后的callback（开启预览模式调用）
        beforeEntryIframe() {
        },
        openCallback() {
        }, // 调用打印之后的回调事件
        closeCallback() {
        }, //关闭打印的回调事件（无法确定点击的是确认还是取消）
        url: '',
        standard: '',
        extraCss: ''
      },
      totalModel: {},
      myTotalSize: 0,
      tableHeight: undefined
    }
  },
  created() {
    this.myTotalSize = this.totalSize
    this.handleResize()
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    handleResize() {
      this.tableHeight = 'height:' + (window.innerHeight - 250) + 'px' // 更新高度数据
    },
    totalSizeClick() {
      this.$emit('page-size', this.myTotalSize)
    },
    print() {
      this.dialogVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
@page {
  size: auto; /* auto is the initial value */
  margin: 3mm; /* this affects the margin in the printer settings */
}

#print-box {
  margin: 10px 10px 10px 10px;
  font-size: 1.6vw !important;
}

.my-table {
  margin-top: 20px;
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
}

.my-table th,
.my-table td {
  padding: 6px;
  text-align: center;
}

.my-table th {
  border: 1px solid #000;
  font-weight: bold;
}

.my-table td {
  border: 1px solid #000;
}

.ml-2 {
  margin-left: 8px;
}

.mt-2 {
  margin-top: 8px;
}

.event-button {
  position: fixed;
  /* 使用固定定位 */
  bottom: 600px;
  /* 距离底部20像素 */
  right: 200px;
  width: 80px;
  height: 80px;
  font-size: 40px !important;
  border-radius: 50% !important;

  ::v-deep.el-button--medium.is-circle {
    padding: 40px;
  }

  ::v-deep.el-button--medium {
    font-size: 30px;
  }
}
</style>
