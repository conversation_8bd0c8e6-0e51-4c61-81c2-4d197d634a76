import request from "@/utils/request";

/**
 * 获取检查列表数据信息
 * @param data
 * @returns {*}
 * @constructor
 */
export function GetCheckConfirmTable(data) {
  return request({
    url: "/CheckConfirm/GetCheckConfirmTable",
    method: "get",
    params: data,
  });
}

/**
 * 查询配置可选择设备信息
 * @returns {*}
 * @constructor
 */
export function GetEquipmentDict() {
  return request({
    url: "/CheckConfirm/GetEquipmentDict",
    method: "get",
  });
}

/**
 * 获取用户金额信息
 * @param patientId
 * @returns {*}
 * @constructor
 */
export function GetPatientMoneyCorrelationMessage(patientId) {
  return request({
    url: "/CheckConfirm/GetPatientMoneyCorrelationMessage?patientId=" + patientId,
    method: "get",
  });
}

/**
 * 获取打印信息
 * @param data 参数
 * @returns {*}
 * @constructor
 */
export function GetPrintMeg(data) {
  return request({
    url: "/CheckConfirm/GetPrintMeg" ,
    method: "post",
    data: data
  });
}

/**
 * 点击项目时获取检查信息
 * @param examNo 检查号
 * @returns {*}
 * @constructor
 */
export function GetItemInformation(examNo) {
  return request({
    url: "/CheckConfirm/GetItemInformation?examNo=" + examNo,
    method: "get",
  });
}

/**
 * 获取费用明细
 * @param examNo 检查号
 * @returns {*}
 * @constructor
 */
export function GetCostInquiry(examNo) {
  return request({
    url: "/CheckConfirm/GetCostInquiry?examNo=" + examNo,
    method: "get",
  });
}

/**
 * 删除项目-- 检查项目
 * @constructor
 */
export function DeleteExamProject(data) {
  return request({
    url: "/CheckConfirm/DeleteExamProject",
    method: "post",
    data: data,
  });
}

export function DeleteItemProject(data) {
  return request({
    url: "/CheckConfirm/DeleteItemProject",
    method: "post",
    data:data,
  });
}

/**
 * 列表项目保存确认
 * @param data
 * @returns {*}
 * @constructor
 */
export function SaveExamProject(data) {
  return request({
    url: "/CheckConfirm/SaveExamProject",
    method: "post",
    data: data,
  });
}
