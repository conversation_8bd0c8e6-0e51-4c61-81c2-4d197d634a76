<template>
  <div class="app-container">
    <div class="oneBed_container">
      <!--左侧病区-->
      <div class="left_search">
        <search-index :queryParams="queryParams" @node-clicked="onNodeClicked" />
      </div>
      <!--核心区域-->
      <div class="bed_content">
        <!--吸顶区域-->
        <div class="sticky_top">
          <!--顶部搜索区域-->
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
            <el-form-item label="患者姓名：" label-width="100px">
              <el-input v-model="queryParams.PatientName" placeholder="请输入患者姓名" clearable style="width: 150px"></el-input>
            </el-form-item>
            <el-form-item label="床号：">
              <el-input v-model="queryParams.BedNo" placeholder="请输入床号" clearable style="width: 150px"></el-input>
            </el-form-item>
            <el-form-item label="床位类型：" label-width="100px">
              <el-select v-model="queryParams.SearchKeywordArray" placeholder="请选择" @change="getList" clearable multiple
                style="width: 150px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="排序类型：" label-width="100px">
              <el-select v-model="queryParams.Type" placeholder="请选择" clearable style="width: 150px">
                <el-option v-for="item in optionsSort" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="warning" icon="el-icon-s-data" size="mini" @click="wardSorting">排序配置
              </el-button>
            </el-form-item>
            <el-form-item>
              <el-button type="success" icon="el-icon-office-building" size="mini" @click="floorTypeConfiguration">楼别配置
              </el-button>
            </el-form-item>
          </el-form>
          <div class="top">
            <div class="section_title">{{ sectionName }} ({{ occupiedBeds }}人)</div>
            <div>本区域全部床位（{{ totalBeds }}）</div>
            <div>已占床位（{{ occupiedBeds }}）</div>
            <div>空余床位（<span class="available-beds">{{ availableBeds }}</span>）</div>
          </div>
          <div class="underline"></div>
          <div class="levelOfCare">
            <div class="circleItem" v-for="(item, index) in careLevels" :key="index">
              <div :class="['circle', item.class]" :style="{ backgroundColor: item.color }"></div>
              <div style="margin-left: 10px;cursor: pointer">{{ item.name }}</div>
            </div>
          </div>
        </div>

        <!--中心内容区域-->
        <div class="bed_container">
          <div class="bed_detail_item" v-for="(item, index) in bedData" :key="index">
            <div class="bed_top_info">
              <div class="bed_top_bedNo"
                :style="{ backgroundColor: item.levelOfCare, border: item.border, color: item.fontColor }">
                {{ item.bedNo }}
              </div>
              <div class="bed_top_baseInfo" v-if="!item.isEmpty">
                <div style="text-align: center">{{ item.patientName }}</div>
                <div class="center_vertical">
                  <span>{{ item.gender }}</span>
                  <span v-show="item.gender">/</span>
                  <span v-show="item.age">{{ item.age }}</span>
                  <span v-show="item.age">/</span>
                  <span v-show="item.visitDays">{{ item.visitDays }}天</span>
                </div>
              </div>
              <img :src="imageSrcForBedData(item.gender)" alt="" v-if="!item.isEmpty" class="SexImg" />
              <img :src="getVipSrc(item.isVip)" alt="" v-if="item.isVip" class="VipImg" />
            </div>
            <div class="bed_middle_info">
              <div class="bed_middle_info_item" v-if="!item.isEmpty">
                <div class="bed_middle_info_item_title" v-for="(detail, idx) in item.details" :key="idx">
                  <span>{{ detail }}</span>
                  <!--                  <span :class="{ redText: shouldHighlight(detail) }">{{ detail }}</span>-->
                </div>
              </div>
              <div class="bed_middle_info_item" v-if="item.isEmpty">
                <div class="bed_middle_info_item_title">
                  <div>{{ item.details.ward }}</div>
                  <div>{{ item.details.bedType }}</div>
                  <div>{{ item.details.position }}</div>
                  <div>{{ item.details.roomNo }}</div>
                </div>
              </div>
              <img :src="emptyFloorSrc" alt="" v-if="item.isEmpty" class="emptyFloorSrc" />
              <el-button type="primary" plain v-if="item.isEmpty" icon="el-icon-plus" class="emptyFloorSrc">床位分配
              </el-button>
            </div>
            <div class="bed_bottom_info" v-if="!item.isEmpty">
              <div class="bed_bottom_info_left">
              </div>
              <div class="bed_bottom_info_right">
                <div class="sign"></div>
                <img :src="moreSrc" alt="" class="moreSrc" />
                <div class="hover_operation_wrapper">
                  <div class="hover_operation_content">
                    <div>床位置空</div>
                    <div>转房</div>
                    <div>转床</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <img :src="rocketSrc" alt="" class="img_rocket" @click="scrollToTop" v-show="showRocket" />
        </div>
      </div>
    </div>
    <!--排序配置-->
    <CustomDialog :isVisible="isVisible" @dialog-closed="onDialogClosed" />
    <!--楼别配置-->
    <el-drawer title="" size="40%" :visible.sync="drawer" :with-header="false">
      <floorTypeConfiguration :queryParams="queryParams"></floorTypeConfiguration>
    </el-drawer>
  </div>
</template>

<script>
import man from "@/assets/images/man.png";
import woMan from "@/assets/images/woman.png";
import emptyFloor from "@/assets/images/emptyFloor.png";
import more from "@/assets/images/more.png";
import rocket from "@/assets/images/rocket.png";
import VIP from "@/assets/images/VIP2.png";
import { utils } from "@/mixin.js";
import SearchIndex from "./searchIndex.vue";
import { GetDetailBedInfo } from "../../api/oneBed/OneBedInfo";
import { careLevels, options } from "./DataSources";
import CustomDialog from "./CustomDialog.vue";
import FloorTypeConfiguration from "./floorTypeConfiguration.vue";

export default {
  name: "oneBedIndex",
  mixins: [utils],
  components: { FloorTypeConfiguration, CustomDialog, SearchIndex },
  props: {},
  data() {
    return {
      total: 0,
      drawer: false,
      currentTime: '',
      bedData: [],
      showRocket: false,
      isVisible: false,
      scrollThreshold: 0,// 动态计算的滚动阈值
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dateRange: [],
        wardName: '',
        WardCode: undefined,
        PatientName: undefined,
        BedNo: undefined,
        Type: "1",
        SearchKeywordArray: undefined,
      },
      manSrc: man,
      woManSrc: woMan,
      VIPSrc: VIP,
      emptyFloorSrc: emptyFloor,
      moreSrc: more,
      rocketSrc: rocket,
      sectionName: '1-8病区',//默认一个病区
      totalBeds: 100,
      occupiedBeds: 66,
      careLevels,
      //检索类型
      RetrievalOptions: [
        {
          value: '1',
          label: '病区'
        },
        {
          value: '2',
          label: '全院'
        }
      ],
      optionsSort: [
        {
          value: '1',
          label: '按楼号'
        },
        {
          value: '2',
          label: '按病区楼层'
        }
      ],
      options,
    };
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth' // 平滑滚动
      });
    },
    onNodeClicked(data) {
      this.queryParams.WardCode = data.id;
      this.sectionName = data.label;
      this.getList();
    },
    getList() {
      this.loadingMethed();
      if (this.queryParams.WardCode === undefined) {
        this.queryParams.WardCode = "0108H";
      }
      GetDetailBedInfo(this.queryParams).then(res => {
        this.bedData = res.data.bedDetailList;
        this.totalBeds = res.data.allBedCount;
        this.occupiedBeds = res.data.occupiedBedCount;
        this.loadingMethed().close();
      });
    },
    updateScrollThreshold() {
      // 设置滚动阈值为视口高度的20%
      this.scrollThreshold = window.innerHeight * 0.2;
    },
    wardSorting() {
      this.isVisible = true;
    },
    floorTypeConfiguration() {
      this.drawer = true;
    },
    onDialogClosed(result, success) {
      this.isVisible = result;
      if (result) {
        // 如果结果为 true，则执行确定操作
      } else {
      }
    },
    handleScroll() {
      // 获取页面滚动的高度
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      // 判断是否达到了设定的滚动阈值
      if (scrollTop >= this.scrollThreshold) {
        this.showRocket = true;
      } else {
        this.showRocket = false;
      }
    },
  },
  computed: {
    availableBeds() {
      return this.totalBeds - this.occupiedBeds;
    },
    imageSrcForBedData() {
      return (gender) => {
        return gender === '女' ? this.woManSrc : this.manSrc;
      };
    },
    getVipSrc() {
      return (isVip) => {
        return isVip ? this.VIPSrc : '';
      };
    },
  },
  watch: {
    queryParams: {
      handler(newVal, oldVal) {
      },
      deep: true
    }
  },

  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.getList();
  },
  // 生命周期 - 挂载完成（可以访问 DOM 元素）
  mounted() {
    this.updateScrollThreshold();
    window.addEventListener('scroll', this.handleScroll);
    window.addEventListener('resize', this.updateScrollThreshold);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
    window.removeEventListener('resize', this.updateScrollThreshold);
    clearInterval(this.timer);
  },
};
</script>

<style lang="scss" scoped>
/* 屏幕宽度大于1400px时的样式 */
@media (min-width: 1401px) {
  .bed_detail_item {
    flex: 0 0 calc(20% - 20px);
    /* 屏幕宽度大于1400px时，每个元素占20%，即5个一排 */
  }

  .oneBed_container .left_search {
    width: 15% !important;
    /* 使用!important确保优先级 */
  }

  .oneBed_container .bed_content {
    width: 85% !important;
    /* 使用!important确保优先级 */
    margin-left: 15% !important;
  }
}

.oneBed_container {
  display: flex;
  position: relative;
  z-index: 1;

  .left_search {
    width: 20%;
    position: fixed;
    z-index: 2;
  }

  .bed_content {
    width: 80%;
    margin-left: 20%;

    .top {
      display: flex;
      justify-content: flex-start;
      gap: 40px;

      /* 添加间距 */
      .section_title {
        font-size: 16px;
        color: black;
        font-weight: bold;
      }

      .available-beds {
        color: #1ab394;
      }
    }

    .underline {
      /* 下划线样式 */
      width: 100%;
      margin-top: 1%;
      border-bottom: 2px solid #ebeef5;
    }

    .levelOfCare {
      /* 护理等级样式 */
      margin-top: 2%;
      display: flex;
      gap: 20px;

      /* 添加间距 */
      .circleItem {
        display: flex;
        align-items: center;

        .circle {
          width: 25px;
          height: 25px;
          border-radius: 50%;
        }
      }
    }

    .bed_container {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      /* 允许换行 */
      justify-content: flex-start;
      position: relative;

      .bed_detail_item {
        //添加边框和阴影
        border: 1px solid #ccc;
        /* 添加灰色实线边框 */
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        /* 添加阴影效果 */
        padding: 10px;
        //width: 24%;
        width: 32%;
        border-radius: 5px;
        margin-top: 2%;
        transition: all 0.3s ease;

        /* 添加过渡效果 */
        .bed_top_info {
          display: flex;
          justify-content: space-between;
          align-items: center;
          /* 垂直居中 */
          position: relative;

          .bed_top_bedNo {
            min-width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #1ab394;
            display: flex;
            /* 启用Flexbox布局 */
            justify-content: center;
            /* 水平居中 */
            align-items: center;
            /* 垂直居中 */
            color: white;
            /* 文字颜色 */
          }

          .bed_top_baseInfo {
            .center_vertical {
              display: flex;
              align-items: center;
              /* 垂直居中 */
              justify-content: space-around;
            }
          }

          .SexImg {
            //width: 18%;
            //height: auto;
            width: 20%;
            height: auto;
          }

          .VipImg {
            width: 20%;
            height: auto;
            //float: right;
            //position: absolute; /* 定位为绝对 */
            //right: 0; /* 距离右侧边缘的距离 */
            //top: 0; /* 距离顶部边缘的距离 */
          }
        }

        .bed_middle_info {}

        .bed_bottom_info {
          .bed_bottom_info_left {}

          .bed_bottom_info_right {
            display: flex;
            justify-content: space-between;
            position: relative;

            .sign {
              width: 2px;
            }

            .moreSrc {
              width: 9%;
              height: auto;
            }

            .hover_operation_wrapper {
              position: absolute;
              top: 100%;
              right: 0;
              visibility: hidden;
              z-index: 10;
              /* 确保内容位于前面 */
              opacity: 0;
              transform: translateY(-10px);
              transition: opacity 0.5s ease, transform 0.5s ease;
            }

            .hover_operation_content {
              background-color: white;
              border: 1px solid #ccc;
              padding: 10px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
            }

            .hover_operation_content>div {
              margin: 5px 0;
              cursor: pointer;
            }

            .hover_operation_content>div:hover {
              background-color: #f0f0f0;
              /* 浅灰色 */
            }

            /* 当鼠标悬停在 moreSrc 图片上时显示 hover_operation_content */
            .moreSrc:hover+.hover_operation_wrapper,
            .hover_operation_wrapper:hover {
              visibility: visible;
              opacity: 1;
              transform: translateY(0);
            }
          }
        }
      }

      .img_rocket {
        position: fixed;
        bottom: 5%;
        right: 5%;
        width: 5%;
        height: auto;
        cursor: pointer;
      }
    }

    /* 鼠标悬停时的效果 */
    .bed_detail_item:hover {
      border-color: #007bff;
      /* 改变边框颜色 */
      //border: solid #007bff;
      box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);
      /* 加大阴影 */
      transform: translateY(-2px);
      /* 向上移动一点 */
      cursor: pointer;
      z-index: 999;
    }

    .circle_super {
      /* 特级护理的额外样式 */
    }

    .circle_first {
      /* 一级护理的额外样式 */
    }

    .circle_second {
      /* 二级护理的额外样式 */
    }

    .circle_third {
      /* 三级护理的额外样式 */
      border: 1px solid black;
      /* 添加黑色边框 */
    }

    .circle_empty {
      /* 空床的额外样式 */
    }

    .common_margin_top {
      margin-top: 2%;
    }

    .emptyFloorSrc {
      width: 50%;
      height: auto;
      margin-left: 20%;
      margin-top: 5%;
    }

    .sticky_top {
      position: sticky;
      top: 0;
      /* 距离视口顶部的距离 */
      z-index: 1;
      /* 确保元素在其他内容之上 */
      background-color: white;
      /* 防止滚动时背景颜色不同产生闪烁 */
    }
  }

  .redText {
    color: red;
  }
}
</style>

