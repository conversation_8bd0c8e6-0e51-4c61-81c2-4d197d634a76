<template>
  <div class="confirm-table-home">
    <div :class="{one: tableButtonOne,two: tableButtonTwo,three: tableButtonThree,}">
      <el-table :data="tableData" border style="width: 100%" ref="tables" :height="tableHeight - 300"
                @select="handleSelectionChange" @select-all="handleSelectionChange"
                @row-click="tableClick" highlight-current-row :row-class-name="rowClassName"
                @cell-dblclick="doubleSelectionChange"
      >
        <el-table-column align="center" prop="examSubClass" label="检查子类" :show-overflow-tooltip="true"
                         :width="tableStyle.width.width1"
        />
        <el-table-column align="center" prop="patientId" label="病人ID" sortable :width="tableStyle.width.width2"/>
        <el-table-column align="center" prop="name" label="姓名" :width="tableStyle.width.width3">
          <template slot-scope="scope">
            <div style="font-weight: 600; font-size: 16px">
              {{ scope.row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="sex" label="性别" :width="tableStyle.width.width4"/>
        <el-table-column align="center" prop="age" label="年龄" :width="tableStyle.width.width4"/>
        <el-table-column align="center" prop="device" label="检查项目" sortable :width="tableStyle.width.width5">
          <template slot-scope="scope">
            <div style="font-weight: 600; font-size: 16px">
              {{ scope.row.device }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="reqDateTime" label="申请时间" :show-overflow-tooltip="true"
                         :width="tableStyle.width.width6"
        />
        <el-table-column align="center" type="selection" width="55" label="已做" :selectable="selectable"
                         :width="tableStyle.width.width7"
        />
        <el-table-column align="center" prop="patientLocalId" label="检查号" :width="tableStyle.width.width8"/>
        <el-table-column align="center" label="检查设备" :width="tableStyle.width.width9">
          <template slot-scope="scope">
            <!--            @focus="dictTreeDispose(scope.row)"-->
            <el-select v-model="scope.row.examDevice" placeholder="请选择">
              <el-option v-for="(item, index) in scope.row.tree"
                         :key="index" :label="item.label" :value="item.value"
              >
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="patientSource" label="来源" :width="tableStyle.width.width10">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.patientSource === '1'">门诊</el-tag>
            <el-tag v-if="scope.row.patientSource === '2'">住院</el-tag>
            <el-tag v-if="scope.row.patientSource === '3'">外来</el-tag>
            <el-tag v-if="scope.row.patientSource === '4'">门诊</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="bedsideFlag" label="床旁" :width="tableStyle.width.width10">
          <template slot-scope="scope">
            <div v-if="scope.row.patientSource">
              <el-tag type="danger" v-if="scope.row.bedsideFlag === 'Y'">是</el-tag>
              <el-tag v-else-if="scope.row.bedsideFlag === 'N'">否</el-tag>
              <el-tag v-else>否</el-tag>
            </div>
            <div v-else>其他</div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="costs" label="检查费用" :width="tableStyle.width.width11"/>
        <el-table-column align="center" prop="charges" label="应收费用" :width="tableStyle.width.width12"/>
        <el-table-column align="center" prop="notice" label="备注" :width="tableStyle.width.width13"
                         :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <div
              v-if="scope.row.notice === '已退费'"
              style="color: red; font-size: 16px"
            >
              {{ scope.row.notice }}
            </div>
            <div v-else style="font-size: 13px">{{ scope.row.notice }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="phoneNumber" label="联系电话" :width="tableStyle.width.width14"/>
        <el-table-column align="center" prop="examNo" label="申请序号" :width="tableStyle.width.width15"/>
        <el-table-column align="center" label="检查方式" :width="tableStyle.width.width16">
          <template slot-scope="scope">
            <div v-if="scope.row.examMode">
              <el-tag v-if="scope.row.examMode === '1'">病房</el-tag>
              <el-tag v-if="scope.row.examMode === '2'">科室</el-tag>
              <el-tag v-if="scope.row.examMode === '3'">其他</el-tag>
            </div>
            <el-tag v-else>其他</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="reqDeptName" label="申请科室" :width="tableStyle.width.width17"
                         :show-overflow-tooltip="true"
        />
        <el-table-column align="center" prop="reqPhysician" label="申请医生" :width="tableStyle.width.width18"/>
        <el-table-column align="center" prop="performedByName" label="执行科室" :width="tableStyle.width.width19"
                         :show-overflow-tooltip="true"
        />
        <el-table-column align="center" prop="impression" label="检查诊断(印象)" :width="tableStyle.width.width20"
                         :show-overflow-tooltip="true"
        />
        <el-table-column align="center" prop="operationDept" label="手术科室" :width="tableStyle.width.width21"
                         :show-overflow-tooltip="true"
        />
      </el-table>
    </div>
    <div style="display: flex; justify-content: flex-end; margin-right: 20%">
      <pagination v-show="total > 0" :limit.sync="pageSize" :page.sync="pageNum" :total="total"
                  @pagination="getPage" :page-sizes="[20, 50, 100, 200, 500]" :key="pageKey"
      />
    </div>

    <reservation-exam ref="reservationExamRef" @table-save-send="tableSave"/>
    <ExamPrint ref="examPrintRefTwo"/>
    <already-appoint-exam ref="alreadyAppointExamFef"/>
    <case-history-message ref="caseHistoryMsgRef"/>
    <patient-reminder ref="patientReminderRef"/>
  </div>
</template>

<script>
/**
 * 中间table功能
 */
import {
  GetCheckConfirmTable,
  GetEquipmentDict,
  SaveExamProject
} from '@/api/checkAndConfirm/checkConfirm'
import {
  GetLocalId,
  VerifyPatientIdIntegrality
} from '@/api/checkAndConfirm/checkCommon'
import ReservationExam from './reservationExam.vue'
import ExamPrint from './rightFunctionRealization/ExamPrint.vue'
import AlreadyAppointExam from './homeTable/alreadyAppointExam.vue'
import CaseHistoryMessage from './homeTable/caseHistoryMessage.vue'
import PatientReminder from './buttomFunctionRealization/patientReminder.vue'

export default {
  name: 'middleTableFunction',
  props: [],
  components: {
    PatientReminder,
    CaseHistoryMessage,
    AlreadyAppointExam,
    ExamPrint,
    ReservationExam
  },
  data() {
    return {
      isLoading: false,
      noMore: false,
      tableData: [],
      tableDataAll: [],
      tableStyle: {
        height: '200px',
        width: {
          width1: '120',
          width2: '70',
          width3: '55',
          width4: '40',
          width5: '',
          width6: '80',
          width7: '35',
          width8: '80',
          width9: '100',
          width10: '60',
          width11: '65',
          width12: '65',
          width13: '80',
          width14: '80',
          width15: '80',
          width16: '70',
          width17: '80',
          width18: '60',
          width19: '80',
          width20: '160',
          width21: '80'
        }
      },
      tableButtonOne: true,
      tableButtonTwo: false,
      tableButtonThree: false,
      dictTree: [],
      dictTreeOperation: [],
      newSelectData: [],
      total: 0,
      pageNum: 1,
      pageSize: 20,
      caseKey: 0,
      pageKey: 0,
      tableHeight: 0
    }
  },
  created() {
    this.getDictTree()
    this.handleResize()
  },
  methods: {
    tableClick(row, column, event) {
      this.$store.commit('SET_ROWS', row)
      this.$emit('button-status', this.examButtonDataDispose(row, this.newSelectData))
    },
    handleSelectionChange(val) {
      if (val) {
        let newData = []
        val.forEach((t) => {
          if (!t.disabled) {
            newData.push(t)
          }
        })
        if (newData.length > 0) {
          newData.forEach((t) => {
            t.patientLocalId = t.examNo
            t.examDevice = this.dictTreeDispose(t)
          })
          // newData.forEach(x => {
          //   if (!x.patientLocalId) {
          //     GetLocalId({
          //       dept_code: x.performedBy,
          //       local_id_name: 'EXAM_GE_SEQ',
          //       seq_type: 'patient_local_id',
          //       key: ++this.caseKey
          //     }).then(res => {
          //       this.tableData.filter(t => {
          //         if (t.examNo === x.examNo) {
          //           t.patientLocalId = res.data
          //           t.examDevice = this.dictTreeDispose(t)
          //         }
          //       })
          //     })
          //   }
          // })
        }
        this.newSelectData = newData
        this.$store.commit('SET_TABLE', newData)
        this.$emit('button-status', this.examButtonDataDispose(this.$store.getters.rowTable, this.newSelectData))
      }
    },
    getPage() {
      ++this.pageKey
      let data = this.$store.getters.patient
      data.pageNum = this.pageNum
      data.pageSize = this.pageSize
      this.getTable(data)
    },
    getTable(data) {
      const loading = this.$loading({
        lock: true,
        text: '数据正在努力提取中,请耐心等待!!!(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.$emit('button-status', this.examButtonDataCreate())
      this.$store.commit('SET_ROWS', null)
      this.tableData = []
      this.total = 0
      //先校验当前患者费用是否正常
      GetCheckConfirmTable(data)
        .then((res) => {
          let tableData = res.data.list
          this.total = res.data.count
          this.tableData = tableData
          this.localNameDispose()
          if (data.patientId) {
            //患者相关校验
            this.verifyPatientNoExamAppointmentMsg(tableData)
            this.verifyPatientIdIntegrality(data)
            this.verifyPatientReminder(data.patientId)
          }
          this.$refs.reservationExamRef.closeAppointmentMonitor(false)
          setTimeout(() => {
            tableData.forEach((t) => {
              if (t.disabled) {
                this.$refs.tables.toggleRowSelection(t, true)
                t.disabled = true
              }
            })
          }, 100)
        })
        .finally(() => {
          loading.close()
        })
    },
    //localName已保存的项目进行赋值
    localNameDispose() {
      this.tableData.forEach((t) => {
        if (t.resultStatus) {
          //进行赋值
          this.dictTreeDisposeTwo(t)
        }
      })
    },
    //校验患者是否有为保存的预约项目
    verifyPatientNoExamAppointmentMsg(tableData) {
      this.$refs.alreadyAppointExamFef.alreadyAppointMonitor(true, tableData)
    },
    //校验患者诊查费、未扣费等项目信息
    verifyPatientIdIntegrality(formData) {
      let patientData = {
        patient_id: formData.patientId,
        performed_by: formData.deptCode
      }
      VerifyPatientIdIntegrality(patientData).then((res) => {
        if (res.code === 200) {
        }
      })
    },
    //校验患者提醒
    verifyPatientReminder(patientId) {
      this.$refs.patientReminderRef.verify(patientId)
    },
    rowClassName({ row, rowIndex }) {
      if (row.disabled) {
        return 'disabled-row'
      }
    },
    selectable(row, index) {
      if (row.disabled) {
        return false
      } else {
        return true
      }
    },
    getDictTree() {
      GetEquipmentDict().then((res) => {
        this.dictTree = res.data
      })
    },
    dictTreeDisposeTwo(row) {
      let item = row.examClass
      let dict = this.dictTree
      let newDict = []
      dict.forEach(function(x, index) {
        if (x.son === item) {
          newDict.push(x)
        }
      })
      row.tree = newDict
    },
    dictTreeDispose(row) {
      let item = row.examClass
      let dict = this.dictTree
      let newDict = []
      dict.forEach(function(x, index) {
        if (item.includes(x.son)) {
          newDict.push(x)
        }
      })
      let examDevice = ''
      if (newDict.length === 1) {
        examDevice = newDict[0].value
      }
      row.tree = newDict
      return examDevice
    },
    tableSave(data) {
      if (data) {
        let selectData = this.newSelectData
        let insertData = []
        let insertRoWData = {}
        let empNo = this.$store.getters.empNo
        let status = true
        let appointData = this.$store.getters.reservationTable.table
        if (appointData) {
          selectData.filter((row) => {
            insertRoWData = {}
            let da = appointData.find((t) => row.examNo === t.examNo)
            if (da) {
              if (!da.appointmentDate && !da.appointmentTime) {
                status = false
                this.$msgbox.alert(
                    '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
                    '数据错乱' +
                    '</div>' +
                    '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
                    '请重新提取数据 / 按F5刷新' +
                    '</div>',
                    '系统提示',
                    {
                      confirmButtonText: '确定',
                      type: 'warning',
                      dangerouslyUseHTMLString: true
                    }).then(() => {
                  })
                return
              }
            }
            insertRoWData = {
              data_status: 'appoints',
              result_status: '2',
              exam_no: row.examNo,
              patient_local_id: row.patientLocalId,
              exam_device: row.examDevice,
              exam_class: row.examClass,
              user_id: empNo,
              user_name: empNo,
              costs: row.costs,
              charges: row.charges
            }
            insertData.push(insertRoWData)
          })
        } else {
          selectData.filter((row) => {
            insertRoWData = {}
            if (!row.examDevice) {
              if (row.tree && row.tree.length > 0) {
                status = false
                this.$msgbox
                  .alert(
                    '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
                    '当前选中行中部分数据请选择检查设备信息' +
                    '</div>',
                    '系统提示',
                    {
                      confirmButtonText: '确定',
                      type: 'warning',
                      dangerouslyUseHTMLString: true
                    }).then(() => {
                })
                return
              }
            }
            let appointmentDataDict = this.$store.getters.appointmentDeptDict
            if (!row.appTime && !row.appDate) {
              if (appointmentDataDict.includes(row.performedBy)) {
                status = false
                this.$msgbox.alert(
                  '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
                  '当前检查未预约时间,请前点击确定按钮 -> 前往预约中心预约!!!' +
                  '</div>',
                  '系统提示',
                  {
                    confirmButtonText: '确定',
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                  }).then(() => {
                  let examNos = []
                  selectData.forEach((x) => {
                    if (appointmentDataDict.includes(x.performedBy)) {
                      examNos.push(x.examNo)
                    }
                  })
                  this.$store.commit('SET_APPOINTMENTS_EXAM_NO', examNos)
                  this.$refs.reservationExamRef.reservationCenterMonitor(true, 1)
                })
                return
              }
            }
            insertRoWData = {
              data_status: 'appoints',
              result_status: '2',
              exam_no: row.examNo,
              patient_local_id: row.patientLocalId,
              exam_device: row.examDevice,
              exam_class: row.examClass,
              user_id: empNo,
              user_name: empNo,
              costs: row.costs,
              charges: row.charges
            }
            insertData.push(insertRoWData)
          })
        }
        if (status && insertData.length > 0) {
          //判断项目是否预约
          SaveExamProject(insertData).then((res) => {
            if (res.code === 200) {
              this.$message.success('保存成功!!!')
              this.getPage()
              this.$store.commit('SET_APPOINTMENTS_EXAM_NO', [])
              this.$alert(
                '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">是否要一键打印?</div>',
                '系统提示',
                {
                  dangerouslyUseHTMLString: true,
                  showCancelButton: true, // 添加取消按钮
                  confirmButtonText: '确定',
                  cancelButtonText: '取消'
                }
              ).then(() => {
                this.$refs.examPrintRefTwo.printOptFor(insertData)
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消一键打印'
                })
              })
            }
          })
        }
      }
    },
    examButtonDataDispose(row, optFor) {
      let rowData = row
      return {
        saveStatus: optFor.length > 0,
        projectStatus: rowData !== null && rowData.patientSource === '2' && (!rowData.resultStatus || rowData.resultStatus === '2'),
        deleteStatus: rowData !== null && !rowData.resultStatus,
        costBreakdownStatus: rowData !== null,
        printStatus: rowData !== null,
        queueStatus: this.$store.getters.examAuthDeptCodes.length > 0,
        appointmentStatus: rowData !== null,
        noExecuteStatus: this.$store.getters.patient.patientId !== null && this.$store.getters.patient.patientId !== undefined && this.$store.getters.patient.patientId !== '',
        caseHistoryStatusOne: rowData !== null && rowData.patientSource !== '2',
        caseHistoryStatusTwo: rowData !== null && rowData.patientSource === '2',
        returnPremiumStatus: rowData !== null && (!rowData.resultStatus || rowData.resultStatus === '2'),
        logisticsStatus: rowData !== null,
        examReportUploadStatus: rowData !== null && this.$store.getters.exportDictExam.includes(row.examClass),
        examReportSelectStatus: rowData !== null && this.$store.getters.exportDictExam.includes(row.examClass),
        csExamReportStatus:this.$store.getters.examAuthDeptCodes.includes('020901') &&
          rowData !== null && rowData.performedBy === '020901' && (rowData.patientSource === '1' || rowData.patientSource === '4') &&
          (rowData.resultStatus === '3' || rowData.resultStatus === '4')
      }

    },
    examButtonDataCreate() {
      return {
        saveStatus: false,
        projectStatus: false,
        deleteStatus: false,
        costBreakdownStatus: false,
        printStatus: false,
        queueStatus: this.$store.getters.examAuthDeptCodes.length > 0,
        appointmentStatus: false,
        noExecuteStatus: this.$store.getters.patient.patientId !== null && this.$store.getters.patient.patientId !== undefined && this.$store.getters.patient.patientId !== '',
        caseHistoryStatusOne: false,
        caseHistoryStatusTwo: false,
        returnPremiumStatus: false,
        logisticsStatus: false,
        examReportUploadStatus: false,
        examReportSelectStatus: false,
        csExamReportStatus: false,
      }
    },
    doubleSelectionChange(row) {
      this.$refs.caseHistoryMsgRef.caseMsgInitVerify(row)
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
      console.log(this.tableHeight)
    }
  },
  mounted() {
    this.$nextTick(() => {
      const bodyStyle = document.body.style, // 获取body节点样式
        htmlStyle = document.getElementsByTagName('html')[0].style, // 获取html节点样式
        docEl = document.documentElement,
        docBody = document.body,
        winWidth = docEl.clientWidth || docBody.clientWidth, // 获取浏览器的宽度
        WinHeight = docEl.clientHeight || docBody.clientHeight // 获取浏览器的高
      window.addEventListener('resize', this.handleResize) // 添加监听器
      if (winWidth <= 1200) {
        this.tableButtonOne = false
        this.tableButtonTwo = false
        this.tableButtonThree = true
        this.tableStyle = {
          height: '460px',
          width: {
            width1: '75',
            width2: '80',
            width3: '45',
            width4: '35',
            width5: '120',
            width6: '100',
            width7: '35',
            width8: '65',
            width9: '90',
            width10: '45',
            width11: '55',
            width12: '55',
            width13: '65',
            width14: '70',
            width15: '60',
            width16: '55',
            width17: '80',
            width18: '60',
            width19: '80',
            width20: '160',
            width21: '80'
          }
        }
      } else if (winWidth <= 1400) {
        this.tableButtonOne = false
        this.tableButtonTwo = true
        this.tableButtonThree = false
        this.tableStyle = {
          height: '580px',
          width: {
            width1: '75',
            width2: '90',
            width3: '50',
            width4: '35',
            width5: '120',
            width6: '120',
            width7: '35',
            width8: '70',
            width9: '90',
            width10: '45',
            width11: '55',
            width12: '55',
            width13: '80',
            width14: '80',
            width15: '70',
            width16: '55',
            width17: '80',
            width18: '60',
            width19: '80',
            width20: '160',
            width21: '80'
          }
        }
      } else {
        this.tableButtonOne = true
        this.tableButtonTwo = false
        this.tableButtonThree = false
        this.tableStyle = {
          height: '650px',
          width: {
            width1: '70',
            width2: '80',
            width3: '55',
            width4: '40',
            width5: '180',
            width6: '120',
            width7: '35',
            width8: '80',
            width9: '100',
            width10: '60',
            width11: '65',
            width12: '65',
            width13: '80',
            width14: '80',
            width15: '80',
            width16: '70',
            width17: '80',
            width18: '80',
            width19: '80',
            width20: '160',
            width21: '100'
          }
        }
      }
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  }
}
</script>

<style scoped lang="scss">
.confirm-table-home {
  .one {
    ::v-deep.el-table--medium .el-table__cell {
      padding: 1px 0;
    }

    ::v-deep.el-table .cell {
      padding-left: 3px;
      padding-right: 3px;
    }

    ::v-deep.el-table .el-table__header-wrapper th,
    .el-table .el-table__fixed-header-wrapper th {
      font-size: 12px;
      height: 30px;
    }

    ::v-deep.el-table {
      font-size: 11px;
    }

    ::v-deep.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      background-color: #1890ff;
      border-color: #1890ff;
    }

    ::v-deep.el-checkbox__input.is-disabled.is-checked
    .el-checkbox__inner::after {
      border-color: #ffffff;
    }

    ::v-deep.el-input--suffix .el-input__inner {
      padding-right: 10px;
    }

    ::v-deep.el-input--medium .el-input__inner {
      height: 28px;
      line-height: 36px;
    }

    ::v-deep.el-select-dropdown__item {
      font-size: 12px !important;
      padding: 0 4px !important;
      height: 28px !important;
    }
  }

  .two {
    ::v-deep.el-table--medium .el-table__cell {
      padding: 1px 0;
    }

    ::v-deep.el-table .cell {
      padding-left: 3px;
      padding-right: 3px;
    }

    ::v-deep.el-table .el-table__header-wrapper th,
    .el-table .el-table__fixed-header-wrapper th {
      font-size: 12px;
      height: 30px;
    }

    ::v-deep.el-table {
      font-size: 11px;
    }

    ::v-deep.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      background-color: #1890ff;
      border-color: #1890ff;
    }

    ::v-deep.el-checkbox__input.is-disabled.is-checked
    .el-checkbox__inner::after {
      border-color: #ffffff;
    }

    ::v-deep.el-input--suffix .el-input__inner {
      padding-right: 10px;
    }

    ::v-deep.el-input--medium .el-input__inner {
      height: 28px;
      line-height: 36px;
    }

    ::v-deep.el-select-dropdown__item {
      font-size: 12px !important;
      padding: 0 4px !important;
      height: 28px !important;
    }

    ::v-deep.el-input__inner {
      font-size: 13px;
      padding: 0 4px;
    }
  }

  .three {
    ::v-deep.el-table--medium .el-table__cell {
      padding: 1px 0;
    }

    ::v-deep.el-table .cell {
      padding-left: 2px;
      padding-right: 2px;
    }

    ::v-deep.el-table .el-table__header-wrapper th,
    .el-table .el-table__fixed-header-wrapper th {
      font-size: 11px;
      height: 30px;
    }

    ::v-deep.el-table {
      font-size: 10px;
    }

    ::v-deep.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      background-color: #1890ff;
      border-color: #1890ff;
    }

    ::v-deep.el-checkbox__input.is-disabled.is-checked
    .el-checkbox__inner::after {
      border-color: #ffffff;
    }

    ::v-deep.el-input--suffix .el-input__inner {
      padding-right: 10px;
    }

    ::v-deep.el-input--medium .el-input__inner {
      height: 28px;
      line-height: 36px;
    }

    ::v-deep.el-select-dropdown__item {
      font-size: 10px !important;
      padding: 0 !important;
      height: 28px !important;
    }

    ::v-deep.el-input__inner {
      font-size: 12px;
      padding: 0 2px;
    }
  }

  ::v-deep.pagination-container {
    position: relative;
    height: 25px;
    margin-bottom: 10px;
    margin-top: 15px;
    padding: 0px 20px !important;
  }

  ::v-deep.el-table__body tr.current-row > td.el-table__cell,
  .el-table__body tr.selection-row > td.el-table__cell {
    background-color: #1890ff;
    color: #ffffff;
  }

  ::v-deep.current-row {
    background: black;
  }

  ::-webkit-scrollbar {
    width: 11px;
    height: 11px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: #6bcaaf;
    border-radius: 10px;
  }

  ::v-deep.el-table .disabled-row .cell .el-checkbox__inner {
    background-color: red !important;
    border-color: red !important;
  }
}
</style>
<style scoped></style>
