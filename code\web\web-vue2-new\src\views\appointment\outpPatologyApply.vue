<template>
  <div class="home">
    <!-- 左侧布局 -->
    <div class="left">
      <div class="left-top">病理检查申请记录</div>
      <div class="left-center">
        <el-form ref="queryform" :model="queryParams" size="mini">
          <el-form-item label="检查分类">
            <el-select
              v-model="queryParams.examClass"
              placeholder="请选择检查分类"
              :clearable="true"
              filterable
              @change="getExamSelectTreeList"
            >
              <el-option
                v-for="item in examClassNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="检查子类">
            <el-select
              v-model="queryParams.examSubClass"
              placeholder="请选择检查子类"
              :clearable="true"
              filterable
              @change="getExamSelectTreeList"
            >
              <el-option
                v-for="item in examSubClassNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="检查项目">
            <el-select
              v-model="queryParams.examItem"
              placeholder="请选择检查项目"
              :clearable="true"
              filterable
              @change="getExamSelectTreeList"
            >
              <el-option
                v-for="item in patternLists"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="left-bottom">
        <div class="head-text">
          <span>检查记录</span>
        </div>
        <div class="item-list">
          <div
            class="item-list-item"
            v-for="(item, index) in applyExamList"
            :key="index"
          >
            <div class="item-top">
              <span>{{ item.patientName }}</span>
              <span>{{ item.applyDateTime }}</span>
            </div>
            <div class="item-center">
              <span>{{ item.examName }}</span>
            </div>
            <div class="item-bottom">
              <div class="item-right">
                <el-tooltip effect="dark" content="详情列表" placement="bottom">
                  <el-button icon="el-icon-tickets" type="text"> </el-button>
                </el-tooltip>
                <el-tooltip effect="dark" content="批量打印" placement="bottom">
                  <el-button
                    icon="el-icon-printer"
                    type="text"
                    style="margin-left: 10%"
                  ></el-button>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
        <div class="item-pagination">
          <pagination
            v-show="total > 0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            :pager-count="5"
            layout="prev, pager, next"
            @pagination="getPatologyInitInfo"
            small
          />
        </div>
      </div>
    </div>
    <!-- 中间布局 -->
    <div class="right">
      <!-- 患者基本信息 -->
      <div class="right-top">
        <div class="right-top-left">
          <el-avatar
            :src="manSrc"
            v-if="users.sex === '男'"
            size="small"
          ></el-avatar>
          <el-avatar
            :src="womenSrc"
            v-else-if="users.sex === '女'"
            size="small"
          ></el-avatar>
          <el-avatar :src="manSrc" v-else size="small"></el-avatar>

          <span style="font-weight: 1000">{{ users.userName }}</span>
          <span>/</span>
          <span style="font-weight: 1000">{{ users.sex }}</span>
          <span>/</span>
          <span style="font-weight: 1000">{{ users.age }}</span>
          <span>/</span>
          <span style="color: #1cbbb4">ID号:</span>
          <span style="color: red">{{ users.patientId }}</span>
          <span>/</span>
          <span style="color: #1cbbb4">本次就诊号:</span>
          <span style="color: #1c84c6">{{ queryParams.clinicNo }}</span>
          <span>/</span>
          <span style="color: #1cbbb4">本次就诊日期:</span>
          <span style="color: #2c8d54">{{ users.treatmentDate }}</span>
        </div>
        <div class="right-top-center"></div>
        <div class="right-top-right"></div>
      </div>
      <!-- 相关操作按钮 -->
      <div class="right-center"></div>
      <!-- 检查申请项目明细 -->
      <div class="right-bottom">
        <div class="right-bottom-left">
          <div class="head-text">
            <span>检查分类</span>
          </div>
          <div class="head-handle">
            <el-input
              style="width: 100%"
              placeholder="输入关键字进行过滤"
              v-model="treeSearch"
              prefix-icon="el-icon-search"
            >
            </el-input>
          </div>
          <div class="exam-tree">
            <el-tree
              :highlight-current="true"
              class="fixed-height-tree"
              :data="examClassDicts"
              :props="defaultProps"
              node-key="label"
              :filter-node-method="filterNode"
              @node-click="getCheckedNodes"
              :default-expand-all="false"
              ref="tree"
            >
            </el-tree>
          </div>
        </div>

        <div class="right-bottm-right">
          <!-- 检查项目列表 -->
          <div class="right-bottom-center">
            <div class="head-text">
              <span>检查项目</span>
            </div>

            <!-- 检查项目列表 -->
            <div class="exam-class-center">
              <el-checkbox-group v-model="patternSetList">
                <el-descriptions
                  :column="2"
                  size="small"
                  direction="horizontal"
                  border
                >
                  <el-descriptions-item
                    v-for="(item, index) in patternList"
                    :key="index"
                    align="center"
                  >
                    <el-checkbox
                      style="float: left; color: #00afff"
                      :label="item.label"
                      @change="getCheckBox(item.label, item)"
                    >
                    </el-checkbox>
                  </el-descriptions-item>
                </el-descriptions>
              </el-checkbox-group>
            </div>

            <!-- 已选检查项目 -->
            <div class="exam-selected-center">
              <div class="head-text">
                <span>已选择项目列表</span>
              </div>
              <el-table
                ref="multipleTable"
                :data="patternDetailsList"
                size="mini"
                border
                style="width: 100%"
              >
                <el-table-column align="center" label="操作">
                  <template slot-scope="scope">
                    <el-button icon="" size="mini" type="text">删除 </el-button>
                  </template>
                </el-table-column>
                <el-table-column
                  align="center"
                  show-overflow-tooltip
                  prop="pattern"
                  label="项目名称"
                ></el-table-column>
              </el-table>
            </div>
          </div>
          <!-- 门诊病历信息 -->
          <div class="right-bottom-right">
            <div class="head-text">
              <span>门诊病历</span>
            </div>
            <el-alert
              type="success"
              description="上方申请单内容旁的按钮,可快速修改患者门诊病历信息"
              show-icon
            >
            </el-alert>
            <div class="oupt-mr-input">
              <span class="my-sketch-span">1·检查目的</span>
              <div class="my-sketch-input">
                <el-input
                  type="textarea"
                  v-model="users.inspectionPurpose"
                  placeholder="请输入检查目的"
                ></el-input>
              </div>
              <i style="color: red">*</i
              ><span class="my-sketch-span">2·症状</span>
              <div class="my-sketch-input">
                <el-input
                  type="textarea"
                  v-model="users.medHistory"
                  placeholder="请输入症状"
                >
                </el-input>
              </div>

              <i style="color: red">*</i
              ><span class="my-sketch-span">3·体征</span>
              <div class="my-sketch-input">
                <el-input
                  type="textarea"
                  placeholder="请输入体征"
                  v-model="users.bodyExam"
                >
                </el-input>
              </div>
              <i style="color: red">*</i
              ><span class="my-sketch-span">6·临床诊断</span>
              <div class="my-sketch-input">
                <el-input
                  type="textarea"
                  placeholder="请输入临床诊断"
                  v-model="users.diagDesc"
                >
                </el-input>
              </div>
              <span class="my-sketch-span">7·其他诊断</span>
              <div class="my-sketch-input">
                <el-input
                  type="textarea"
                  placeholder="请输入其他诊断"
                  v-model="users.relevantDiag"
                >
                </el-input>
              </div>
              <span class="my-sketch-span">8·相关化验结果</span>
              <div class="my-sketch-input">
                <el-input
                  type="textarea"
                  placeholder="请输入相关化验结果"
                  v-model="users.relevantLabTest"
                >
                </el-input>
              </div>
            </div>
          </div>
          <!-- 检查申请发送 -->
          <div class="send-apply-bottom"></div>
        </div>
      </div>
    </div>

    <!-- 病理检查申请单页面 从右往左打开 -->
    <div class="exam-bl">
      <el-drawer
        title="门诊患者病理检查申请"
        :visible.sync="pathologyDrawerVisible"
        :direction="pathologyDirection"
        size="60%"
      >
        <pathology
          :patientInfo="queryParams"
          :users="users"
          :examItemName="examItemName"
          :checkExamInfo="checkExamInfo"
          :index="Math.random()"
          @close-drawer="pathologyDrawerVisible = false"
          @get-list="getExamAppointmentRegisterList"
        />
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  GetExamClassSelectInfo,
  GetPatologyInitInfo,
  GetPatologyExamTree,
  GetExamItemInfo
} from "@/api/appointment/pathology";
import { GetExamProjectParticularsList } from "@/api/appointment/register";

import pathology from "./component/outpAppointPathology.vue";
export default {
  name: "outp-patology-apply",
  components: {
    pathology,
  },
  data() {
    return {
      //病理检查申请抽屉
      pathologyDrawerVisible: false,
      pathologyDirection: "rtl",
      //点击选择的检查项目名称
      examItemName: undefined,
      //获取申请路由参数
      queryParams: {
        pagNum: 1,
        pageSize: 6,
        patientId: undefined,
        clinicNo: undefined,
        empNo: undefined,
        examClass: undefined,
        examSubClass: undefined,
        examItem: undefined,
      },
      checkExamInfo:{
      },
      //检查项目分类字典
      examClassNameList: [],
      //检查项目子分类字典
      examSubClassNameList: [],
      //检查项目名称字典
      patternLists: [],
      //用户基本信息
      users: {
        userName: "",
        sex: "",
        age: "",
        patientId: "",
        bed: "",
        treatmentDate: "",
        visitId: "",
        costType: "",
        diagnose: "",
        clinicNo:""
      },
      //申请列表
      applyExamList: [],
      total: 0,
      treeSearch: undefined,
      //检查项目列表
      examClassDicts: [],
      patternSetList: [],
      //检查方法
      patternList: [],
      patternDetailsList: [],
      //默认属性
      defaultProps: {
        children: "children",
        label: "label",
      },
      manSrc: require("@/assets/icons/svg/man.png"), //男性头像
      womenSrc: require("@/assets/icons/svg/woman.png"), //女性头像
    };
  },
  created() {
    this.queryParams = {
      patientId: this.$route.query && this.$route.query.patientId,
      clinicNo: this.$route.query && this.$route.query.clinicNo,
      empNo: this.$route.query && this.$route.query.empNo,
    };
    this.users.clinicNo = this.$route.query && this.$route.query.clinicNo;
    this.getExamClassSelectInfo();
    this.getPatologyInitInfo();
    this.getExamTree();
  },
  watch: {
    treeSearch(val) {
      this.$refs.tree.filter(val);
    },
  },
  methods: {
    /**
     * 父组件事件
     */
    getExamAppointmentRegisterList() {
      //查询检查记录列表
      this.getPatologyInitInfo();
    },
    /**
     * 选择检查项目事件
     * @param label
     * @param item
     */
    getCheckBox(label, item) {
      if (item.examClass === "病理" && item.examSubClass !== "显微摄影") {
        GetExamItemInfo(item).then(response=>{
          this.checkExamInfo = response.data;
          this.examItemName = item.label;
          this.pathologyDrawerVisible = true;
        });
      } else {
        if (item.label !== "" || item.label !== undefined || true) {
          console.log("============================");
        }
      }
    },



    /**
     * 检查记录列表初始化值
     */
    getPatologyInitInfo() {
      GetPatologyInitInfo(this.queryParams).then((response) => {
        this.applyExamList = response.data.patExamList;
        this.total = response.data.total;
      });
    },

    //获取检查申请树形结构
    getExamTree() {
      GetPatologyExamTree(this.queryParams).then((response) => {
        this.users = response.data.user;
        this.examClassDicts = response.data.treeList;
      });
    },

    /**
     *
     * 不同分类选择
     */
    getExamSelectTreeList() {
      this.getExamClassSelectInfo();
      //查询检查记录列表
      this.getPatologyInitInfo();
    },

    /**
     * 初始化项目检查信息
     */
    getExamClassSelectInfo() {
      const queryParam = {
        examClassName: this.queryParams.examClass,
        examSubClassName: this.queryParams.examSubClass,
      };
      GetExamClassSelectInfo(queryParam).then((response) => {
        this.examClassNameList = response.data.classNames;
        this.examSubClassNameList = response.data.subClassNames;
        this.patternLists = response.data.projects;
      });
    },
    //树结构筛选
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    /**
     * 选择类别获取相关列表
     */
    getCheckedNodes(data) {
      if (data.value) {
        GetExamProjectParticularsList(data.value, data.label).then((res) => {
          this.patternList = res.data;
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@media (max-width: 1280px) {
  * {
    font-size: 14px !important;
  }
}

@media (max-width: 1024px) {
  * {
    font-size: 12px !important;
  }
}

.home {
  height: 100%;
  width: 100%;
  padding: 0;
  border: 1px solid #b0b1b0;
  margin: 0;
  .left {
    width: 18%;
    border: 1px solid saddlebrown;
    float: left;
    height: 98%;
    display: flex;
    flex-direction: column;
    .left-top {
      background-color: #1c84c6;
      color: #f2f2f2;
      height: 3%;
      width: 100%;
      font-size: 16px;
      margin: 0 auto;
      text-align: center;
    }
    .left-center {
      margin-top: 2%;
      margin-left: 5%;
      margin-bottom: 5px;
    }
    .left-bottom {
      flex: 1;
      overflow: hidden;
      .item-list {
        height: 89%;
        width: 99%;
        overflow: auto;
        .item-list-item {
          cursor: pointer;
          transition: background-color 0.3s, transform 0.3s ease-in-out; /* 平滑的过渡效果 */
          margin: 5px;
          padding-top: 3px;
          border: 1px solid green;
          .item-top {
            display: flex;
            justify-content: space-between; /* 左右两侧对齐，中间有空隙 */
            color: blue;
          }
          .item-center {
            color: #1c84c6;
          }
          .item-bottom {
            .item-right {
              margin-right: 10px;
              text-align: right;
            }
          }
        }

        .item-list-item:hover {
          transform: translateY(-3px);
          background-color: #6bcaaf;
        }
      }
      .item-pagination {
        display: flex;
        justify-content: center;
        text-align: center;
        margin-top: -15px;
        margin-left: 30px;
      }
    }
  }

  .right {
    float: left;
    width: 82%;
    height: 98%;
    display: flex;
    flex-direction: column;
    .right-top {
      display: flex;
      border: 1px solid slateblue;
      width: 99%;
      height: 46px;
      margin: 3px 3px;

      .right-top-left {
        display: flex;
        flex: 0 0 65%;
        align-items: center;
        margin-left: 5px;
        justify-content: space-between;
      }
      .right-top-center {
        flex: 1;
      }
      .right-top-right {
        flex: 0 0 20%;
      }
    }

    .right-center {
      width: 99%;
      margin: 3px 3px;
      height: 38px;
      float: left;
      border: 1px solid #bfcbd9;
    }

    .right-bottom {
      flex: 1;
      overflow: auto;
      .right-bottom-left {
        margin: 3px 3px;
        border: 1px solid darkslateblue;
        width: 20%;
        height: calc(100% - 6px);
        overflow: hidden;
        float: left;

        .head-handle {
          margin: 3px 3px;
        }
        .exam-tree {
          height: 90%;
          .fixed-height-tree {
            height: 100%; /* 你想要的固定高度 */
            overflow-y: auto; /* 添加滚动条 */
          }
        }
      }

      .right-bottm-right {
        float: left;
        width: 79%;
        height: 100%;
        .right-bottom-center {
          margin: 3px 3px;
          border: 1px solid darkslateblue;
          width: 70%;
          height: calc(100% - 108px);
          float: left;
          .exam-class-center {
            height: 50%;
            overflow: auto;
          }

          .exam-selected-center {
            margin-top: 5px;
            height: 50%;
            overflow: auto;
          }
        }

        .right-bottom-right {
          margin: 3px 3px;
          border: 1px solid darkslateblue;
          float: left;
          height: calc(100% - 108px);
          width: 28%;
          display: flex;
          flex-direction: column;
          .oupt-mr-input {
            overflow: auto;
            flex: 1;
            .my-sketch-span {
              font-size: 12px;
              font-weight: bolder;
              margin-left: 2%;
            }
          }
        }
        .send-apply-bottom {
          margin: 5px 5px;
          padding: 10px;
          border: 1px solid green;
          box-sizing: border-box;
          float: left;
          height: 95px;
          width: 98%;
        }
      }
    }
  }

  .head-text {
    border: 1px solid #f9f9fa;
    background-color: #f0f0f8;
    text-align: center;
    font-size: 16px;
    letter-spacing: 10px;
    color: #1b2947;
    font-weight: bolder;
  }
}

.home::after {
  content: "";
  clear: both;
  display: block;
}
::v-deep.el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #00afff;
}

::v-deep .el-drawer__header {
  margin-bottom: 0px;
  text-align: center;
  background-color: #9feae3;
  color: black;
  font-weight: 800;
  font-size: 18px;
  padding: 5px;
}
</style>
