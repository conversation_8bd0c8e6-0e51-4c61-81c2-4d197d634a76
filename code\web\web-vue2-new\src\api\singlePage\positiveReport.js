import request from '@/utils/request2'

export function getPositiveReportList(data) {
  return request({
    url: '/singlePage/positiveReport/inquire',
    method: 'post',
    data: data
  })
}

export function exportPositiveReport(data) {
  return request({
    url: '/singlePage/positiveReport/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
export function getPositiveReportByResultPatientId(patientId) {
  return request({
    url: '/singlePage/positiveReport/result/' + patientId,
    method: 'post',
  })
}
