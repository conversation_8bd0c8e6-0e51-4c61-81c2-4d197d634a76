// 制度管理模块
import request from '@/utils/request'

export function GetCommitteeNameDict(data) {
    return request({
        url: '/SystemManagement/GetCommitteeNameDict',
        method: 'post',
        data: data
    })
}

export function GetCommitteeNameFile(data) {
    return request({
        url: '/SystemManagement/GetCommitteeNameFile',
        method: 'post',
        data: data
    })
}

export function SystemManagementFile(data) {
    return request({
        url: '/SystemManagement/SystemManagementFile',
        method: 'post',
        data: data
    })
}

export function SystemManagementDel(fileid) {
    return request({
        url: '/SystemManagement/SystemManagementDel?fileid=' + fileid,
        method: 'get'
    })
}

export function GetCommitteeNameFileHistorical(data) {
    return request({
        url: '/SystemManagement/GetCommitteeNameFileHistorical',
        method: 'post',
        data: data
    })
}

export function SystemManagementDelHis(fileid) {
    return request({
        url: '/SystemManagement/SystemManagementDelHis?fileid=' + fileid,
        method: 'get'
    })
}