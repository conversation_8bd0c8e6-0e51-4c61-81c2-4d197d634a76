<template>
  <div class="app-container">
    <div class="big-container">
      <div class="one">
        <el-tag style="margin-bottom: 5px">内科楼：</el-tag>
        <el-input v-model.trim="wardName" placeholder="请输入病区名称" clearable size="small" prefix-icon="el-icon-search"
          style="margin-bottom: 20px" />
        <div class="down-tree">
          <el-tree :data="wardOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" ref="tree" node-key="id" :default-expand-all="false" highlight-current
            show-checkbox :check-strictly="true" :check-on-click-node="true" :checked-keys="checkedNodes" />
        </div>

      </div>
      <div class="two">
        <el-tag type="warning" style="margin-bottom: 5px">外科楼：</el-tag>
        <el-input v-model.trim="wardNameTwo" placeholder="请输入病区名称" clearable size="small" prefix-icon="el-icon-search"
          style="margin-bottom: 20px" />
        <div class="down-tree">
          <el-tree :data="wardOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNodeTwo" ref="treeTwo" node-key="id" :default-expand-all="false" highlight-current
            show-checkbox :check-strictly="true" :check-on-click-node="true" :checked-keys="checkedNodesTwo" />
        </div>
      </div>
    </div>
    <el-button type="primary" @click="getSelectedKeys" style="float: right;margin-right: 0">确定</el-button>
  </div>
</template>

<script>
import { utils } from "@/mixin.js";
import { GetLocationInfo, GetWardListInfo, SaveFloorType, GetFloorType } from "../../api/oneBed/OneBedInfo";

export default {
  name: "floorTypeConfiguration",
  mixins: [utils],
  components: {},
  props: {
    queryParams: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      defaultProps: {
        value: "id",  // 确保这里是 `id`
        label: "label",
      },
      total: 0,
      //名称
      wardName: undefined,
      wardNameTwo: undefined,
      wardOptions: [],
      checkedNodes: [],
      checkedNodesTwo: [],
    };
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    filterNodeTwo(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // getSelectedNodes() {
    //   const selectedNodes = this.$refs.tree.getCheckedNodes();
    //   console.log('Selected Nodes:', selectedNodes);
    // },
    getSelectedKeys() {
      // DOM已更新，现在可以安全地使用 this.$refs
      const selectedKeys = this.$refs.tree.getCheckedKeys();
      const selectedKeysTwo = this.$refs.treeTwo.getCheckedKeys();
      console.log('Selected Keys:', selectedKeys);
      console.log('Selected KeysTwo:', selectedKeysTwo);
      let form = {
        selectedKeys: selectedKeys,
        selectedKeysTwo: selectedKeysTwo,
      }
      SaveFloorType(form).then(res => {
        this.$message({
          message: '保存成功',
          type: 'success',
        });

      });
    },
    getList() {
      GetLocationInfo(this.queryParams).then(res => {
        if (this.queryParams.Type === "1") {
          this.wardOptions = res.data.buildingNoList;
        } else {
          this.wardOptions = res.data.wardFloor;
        }
        if (this.wardOptions.length === 0) {
          GetWardListInfo().then(res => {
            this.wardOptions = res.data;
          });
        }
      });
    },
    GetFloorTypeMethod() {
      GetFloorType().then(res => {
        this.checkedNodes = res.data.selectedKeys;
        this.checkedNodesTwo = res.data.selectedKeysTwo;
        this.$refs.tree.setCheckedKeys(this.checkedNodes)
        this.$refs.treeTwo.setCheckedKeys(this.checkedNodesTwo)
      })
    },
  },
  computed: {},
  watch: {
    // 根据名称部门树
    wardName(val) {
      this.$refs.tree.filter(val);
    },
    wardNameTwo(val) {
      this.$refs.treeTwo.filter(val);
    },
    // 监听 queryParams.Type 的变化
    'queryParams.Type': function (newVal, oldVal) {
      this.getList();
    }
  },
  // 生命周期 - 创建完成（可以访问当前this 实例）
  created() {
    this.getList()
  },
  mounted() {
    this.GetFloorTypeMethod();
  }
};
</script>

<style lang="scss" scoped>
.big-container {
  display: flex;
  justify-content: space-around;

  .one {}

  .two {}
}
</style>

