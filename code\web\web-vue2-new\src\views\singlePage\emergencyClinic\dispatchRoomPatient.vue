<template>
  <div class="single-master">
    <div class="single-title">急诊科调度室患者查询</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="开始时间:">
              <el-date-picker v-model="queueForm.beginDate" type="date" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker v-model="queueForm.endDate" type="date" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
                placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="ID号:">
              <el-input v-model="queueForm.patientId" placeholder="请输入患者id" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <div class="element-button">
                <el-button type="primary" icon="el-icon-search" @click="getClinicDispatch">查询</el-button>
                <el-button type="primary" icon="el-icon-download" @click="exportData">导出</el-button>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="element-table">
          <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 150" highlight-current-row
            @cell-dblclick="doubleSelectionChange">
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column prop="NAME" align="center" label="姓名" width="75"></el-table-column>
            <el-table-column prop="SEX" align="center" label="性别" width="55"></el-table-column>
            <el-table-column prop="AGE" align="center" label="年龄" width="55"></el-table-column>
            <el-table-column prop="MAILING_ADDRESS" align="center" label="家庭地址" width="250"></el-table-column>
            <el-table-column prop="DIAG_DESC" align="center" label="诊断" width="300"></el-table-column>
            <el-table-column prop="VISIT_DATE" align="center" label="挂号日期" width="100"></el-table-column>
            <el-table-column prop="CLINIC_LABEL" align="center" label="号别" width="150"></el-table-column>
          </el-table>
        </div>
        <el-dialog :visible.sync="mrStatus" :title="title" width="85%">
          <dispatch-mr :row-data="rowData" :max-height="'height:' + (tableHeight - 250) + 'px'"
            :key="mrKey"></dispatch-mr>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import { getClinicDispatchApi, exportClinicDispatch } from "@/api/singlePage/emergencyClinicDispatch"
import DispatchMr from './module/dispatchMr.vue'
import { excelDownloadXLSX } from "@/utils/BlobUtils"
export default {
  name: 'dispatchRoomPatient',
  props: [],
  components: { DispatchMr },
  data() {
    return {
      tableDate: [],
      queueForm: {
        beginDate: this.formatDate(new Date()),
        endDate: this.formatDate(new Date()),
        // beginDate: '2024-05-01',
        // endDate: '2024-05-01',
        patientId: '',
      },
      mrStatus: false,
      title: '患者病历信息',
      mrKey: 0,
      rowData: {},
      tableHeight: undefined,
    }
  },
  created() {
    this.handleResize();
    this.getClinicDispatch();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize); // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize); // 移除监听器
  },
  methods: {
    exportData() {
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力导出中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      exportClinicDispatch(this.queueForm).then(res => {
        let fileName = "急诊科调度室患者";
        excelDownloadXLSX(res, fileName)
      }).finally(() => {
        loading.close();
      });
    },
    doubleSelectionChange(row) {
      this.rowData = row;
      ++this.mrKey;
      this.mrStatus = true;
    },
    handleResize() {
      this.tableHeight = window.innerHeight; // 更新高度数据
    },
    getClinicDispatch() {
      getClinicDispatchApi(this.queueForm).then(res => {
        if (res.code === 200) {
          this.tableDate = res.data
        }
      })
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
</style>
