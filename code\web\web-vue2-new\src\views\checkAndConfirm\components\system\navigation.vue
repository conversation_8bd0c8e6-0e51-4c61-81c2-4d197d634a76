<template>
  <div class="menu-home">
    <div class="menu-item">
      <el-menu default-active="2" class="el-menu-vertical-demo" @select="handleOpen">
        <el-menu-item index="1">
          <i class="el-icon-user-solid"></i>
          <span slot="title">人员配置</span>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script>
export default {
  name: 'navigation',
  props: [],
  components: {},
  data() {
    return {}
  },
  created() {
  },
  mounted() {
  },
  methods: {
    handleOpen(key, keyPath) {
      this.$emit('menu-item', key)
    }
  }
}
</script>

<style scoped lang="scss">
.menu-home {
  .menu-item{
    height: 100%;
    border-right: 1px solid #00a19b;
  }
}
</style>
