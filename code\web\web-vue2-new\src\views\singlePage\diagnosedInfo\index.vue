<template>
  <div class="single-master">
    <div class="single-title">已诊患者信息</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline" @keyup.enter.native="getList"
            @submit.native.prevent>
  
            <el-form-item label="患者ID号:">
              <el-input v-model="queueForm.patientId" placeholder="请输入患者id" clearable></el-input>
            </el-form-item>
            <el-form-item>
              <div class="element-button">
                <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
              </div>
            </el-form-item>
  
          </el-form>
  
        </div>
  
        <div v-for="(item, index) in baseInfo" :key="index" :value="item">
          <el-tag> 患者id: {{ item.patienT_ID }}</el-tag>
          <el-tag> 患者姓名: {{ item.name }}</el-tag>
          <el-tag>患者性别: {{ item.sex }} </el-tag>
          <el-tag> 患者身份证号码: {{ item.iD_NO }}</el-tag>
          <el-tag> 患者手机号: {{ item. phonE_NUMBER_HOME}}</el-tag>
          <el-tag> 患者家庭地址: {{ item.mailinG_ADDRESS }}</el-tag>
          <el-tag> 身份: {{ item.identity }}</el-tag>
        </div>
  
        <div class="my-table">
          <el-table :data="tableDate" style="width: 100%" border :height="200" highlight-current-row
            @row-click="handleTableClick">
            <el-table-column prop="visiT_DATE" align="center" label="就诊日期" />
            <el-table-column prop="chargE_TYPE" align="center" label="缴费类型" />
            <el-table-column prop="deptname" align="center" label="就诊科室" />
  
            <el-table-column prop="cliniC_TYPE" align="center" label="挂号类型" />
            <el-table-column align="center" label="操作">
              <template slot-scope="scope">
                <el-button size="mini" plain type="text" @click="toSelectOutMr(scope.row)">
                  查看门诊病历
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
  
        <div>
          <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="以往处方" name="first">
              <!--template标签解决点击闪烁问题-->
              <template name='first'>
                <First v-if="activeName==='first'" :firstList="firstList" />
              </template>
            </el-tab-pane>
            <el-tab-pane label="检查" name="second">
              <template name='second'>
                <Second v-if="activeName==='second'" :secondList="secondList" />
              </template>
            </el-tab-pane>
            <el-tab-pane label="化验" name="third">
              <template name='third'>
                <Third v-if="activeName==='third'" :thirdList="thirdList" />
              </template>
            </el-tab-pane>
            <el-tab-pane label="以往处置" name="fourth">
              <template name='fourth'>
                <Fourth v-if="activeName==='fourth'" :fourthList="fourthList" />
              </template>
            </el-tab-pane>
            <el-tab-pane label="血糖/血酮检测记录" name="fifth">
              <template name='fifth'>
                <Fifth v-if="activeName==='fifth'" :fifthList="fifthList" />
              </template>
            </el-tab-pane>
          </el-tabs>
  
        </div>
  
        <el-dialog title="门诊病历" :visible.sync="mrVisiable" width="60%" :before-close="setVisiableClose">
  
          <div v-for="(item,index) in outMrList" :key="index">
            <el-card>
              <el-form :model="item" size="small" :inline="true" label-width="68px">
  
                <table>
                  <tr>
                    <td colspan="4">
                      主诉<el-input v-model="item.illnessdesc" clearable type="textarea" style="width: 600px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="2">
  
                      既往史<el-input v-model="item.anamnesis" clearable type="textarea"
                        :autosize="{ minRows: 1, maxRows: 20}" style="width: 300px;"></el-input>
  
                    </td>
                    <td colspan="2">
                      月经史<el-input v-model="item.menses" clearable type="textarea" style="width: 200px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      过敏史<el-input v-model="item.individual" clearable type="textarea" style="width: 100px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                    <td>
                      家族史<el-input v-model="item.familill" clearable type="textarea" style="width: 100px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                    <td>
                      婚姻<el-input v-model="item.marrital" clearable type="textarea" style="width: 100px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                    <td>
                      医生 <el-input v-model="item.doctor" clearable type="textarea" style="width: 100px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
  
                  </tr>
                  <tr>
                    <td colspan="2">
                      现病史<el-input v-model="item.medhistory" clearable type="textarea" style="width: 250px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                    <td colspan="2">
                      体检<el-input v-model="item.boayexam" clearable type="textarea" style="width: 250px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="2">
  
                      治疗意见<el-input v-model="item.operationrecord" clearable type="textarea" style="width: 250px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                    <td colspan="2">
  
                      病情变化情况<el-input v-model="item.medicalrecord" clearable type="textarea" style="width: 250px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
  
                    </td>
                  </tr>
                  <tr>
                    <td colspan="2">
                      西医诊断 <el-input v-model="item.diagdesc" clearable type="textarea" style="width: 250px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                    <td colspan="2">
                      辅助检查结果<el-input v-model="item.advice" clearable type="textarea" style="width: 250px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="2">
                      西医诊断<el-input v-model="item.icddiagname" clearable type="textarea" style="width: 250px;"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                    <td colspan="2">
                      ICD-10 <el-input v-model="item.icddiagcode" clearable type="textarea" style="width: 250px;"
                        :autosize="{ minRows:1, maxRows: 20}"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="4">
                      中医诊断<el-input v-model="item.cdiag" clearable type="textarea" style="width: 600px;"
                        :autosize="{ minRows:1, maxRows: 20}"></el-input>
                    </td>
  
                  </tr>
                  <tr>
  
                    <td colspan="2">
                      中医诊断<el-input v-model="item.icdcddiagname" clearable type="textarea" style="width: 200px;"
                        :autosize="{ minRows:1, maxRows: 20}"></el-input>
                    </td>
                    <td colspan="2">
                      ICD-10<el-input v-model="item.icdcddiagcode" clearable type="textarea" style="width: 200px;"
                        :autosize="{ minRows:1, maxRows: 20}"></el-input>
                    </td>
  
                  </tr>
                  <tr>
  
                    <td colspan="2">
                      复诊时间<el-input clearable type="textarea" :autosize="{ minRows: 1, maxRows: 20}"
                        style="width: 200px;"></el-input>
                    </td>
                    <td colspan="2">
                      下次就诊时间<el-input clearable type="textarea" :autosize="{ minRows: 1, maxRows: 20}"
                        style="width: 200px;"></el-input>
                    </td>
                  </tr>
                  <tr>
  
                    <td colspan="4">
                      备注<el-input v-model="item.memo" clearable type="textarea"
                        :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                    </td>
                  </tr>
                </table>
  
  
  
  
              </el-form>
            </el-card>
          </div>
          <el-form :model="item" size="small" :inline="true" label-width="68px" v-if="outMrList.length==0 ">
  
            <table>
              <tr>
                <td colspan="4">
                  主诉<el-input v-model="item.illnessdesc" clearable type="textarea" style="width: 600px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
              </tr>
              <tr>
                <td colspan="2">
  
                  既往史<el-input v-model="item.anamnesis" clearable type="textarea" :autosize="{ minRows: 1, maxRows: 20}"
                    style="width: 300px;"></el-input>
  
                </td>
                <td colspan="2">
                  月经史<el-input v-model="item.menses" clearable type="textarea" style="width: 200px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
              </tr>
              <tr>
                <td>
                  过敏史<el-input v-model="item.individual" clearable type="textarea" style="width: 100px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
                <td>
                  家族史<el-input v-model="item.familill" clearable type="textarea" style="width: 100px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
                <td>
                  婚姻<el-input v-model="item.marrital" clearable type="textarea" style="width: 100px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
                <td>
                  医生 <el-input v-model="item.doctor" clearable type="textarea" style="width: 100px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
  
              </tr>
              <tr>
                <td colspan="2">
                  现病史<el-input v-model="item.medhistory" clearable type="textarea" style="width: 250px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
                <td colspan="2">
                  体检<el-input v-model="item.boayexam" clearable type="textarea" style="width: 250px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
              </tr>
              <tr>
                <td colspan="2">
  
                  治疗意见<el-input v-model="item.operationrecord" clearable type="textarea" style="width: 250px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
                <td colspan="2">
  
                  病情变化情况<el-input v-model="item.medicalrecord" clearable type="textarea" style="width: 250px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
  
                </td>
              </tr>
              <tr>
                <td colspan="2">
                  西医诊断 <el-input v-model="item.diagdesc" clearable type="textarea" style="width: 250px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
                <td colspan="2">
                  辅助检查结果<el-input v-model="item.advice" clearable type="textarea" style="width: 250px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
              </tr>
              <tr>
                <td colspan="2">
                  西医诊断<el-input v-model="item.icddiagname" clearable type="textarea" style="width: 250px;"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
                <td colspan="2">
                  ICD-10 <el-input v-model="item.icddiagcode" clearable type="textarea" style="width: 250px;"
                    :autosize="{ minRows:1, maxRows: 20}"></el-input>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  中医诊断<el-input v-model="item.cdiag" clearable type="textarea" style="width: 600px;"
                    :autosize="{ minRows:1, maxRows: 20}"></el-input>
                </td>
  
              </tr>
              <tr>
  
                <td colspan="2">
                  中医诊断<el-input v-model="item.icdcddiagname" clearable type="textarea" style="width: 200px;"
                    :autosize="{ minRows:1, maxRows: 20}"></el-input>
                </td>
                <td colspan="2">
                  ICD-10<el-input v-model="item.icdcddiagcode" clearable type="textarea" style="width: 200px;"
                    :autosize="{ minRows:1, maxRows: 20}"></el-input>
                </td>
  
              </tr>
              <tr>
  
                <td colspan="2">
                  复诊时间<el-input clearable type="textarea" :autosize="{ minRows: 1, maxRows: 20}"
                    style="width: 200px;"></el-input>
                </td>
                <td colspan="2">
                  下次就诊时间<el-input clearable type="textarea" :autosize="{ minRows: 1, maxRows: 20}"
                    style="width: 200px;"></el-input>
                </td>
              </tr>
              <tr>
  
                <td colspan="4">
                  备注<el-input v-model="item.memo" clearable type="textarea"
                    :autosize="{ minRows: 1, maxRows: 20}"></el-input>
                </td>
              </tr>
            </table>
  
  
  
  
          </el-form>
  
        </el-dialog>
  
      </div>
    </div>
  </div>
</template>

<script>
import First from './first.vue'
import Second from './second.vue'
import Third from './third.vue'
import Fourth from './fourth.vue'
import Fifth from './fifth.vue'

import {
  GetParasiteList,
  GetParasiteByTestNo,
  GetParasiteByDocName
} from "@/api/singlePage/parasite";
import {
  GetDiagnosedAndBase,
  GetPrescriptionList,
  GetInspectList,
  GetDisposeList,
  GetBloodSugarList,
  GetAssayList,
  GetOutMrList
} from "@/api/singlePage/diagnosedInfo";
export default {
  name: 'parasiteIndex',
  props: [],
  components: { First, Second, Third, Fourth, Fifth },
  data() {
    return {
      item: {},
      outMrList: [],
      mrVisiable: false,
      currentTabIndex: '0',
      activeName: 'third',
      queueForm: {
        pageNum: 1,
        pageSize: 10,


      },
      baseInfo: [],
      tableDate: [],
      total: 0,
      tableDateTestNo: [],
      tableDateDocName: [],

      testNoDialog: false,
      docNameDialog: false,
      visitModel:
      {
        visitDate: undefined,
        visitNo: undefined,

      },
      firstList: [],
      secondList: [],
      thirdList: [],
      fourthList: [],
      fifthList: [],
      sixthList: [],
      tableHeight: undefined,
    }

  },

  created() {
    //this.getList();
    this.handleResize();
  },

  mounted() {
    window.addEventListener('resize', this.handleResize); // 添加监听器
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize); // 移除监听器
  },

  methods: {
    //切换页签
    handleClick(tab, event) {
      this.currentTabIndex = tab.index
    },
    //点击就诊记录
    handleTableClick(row) {
      this.visitModel.visitDate = row.visiT_DATE
      this.visitModel.visitNo = row.visiT_NO
      this.selectData()
    },
    //查询数据
    selectData() {
      console.log(this.visitModel);

      if (this.visitModel.visitDate != undefined && this.visitModel.visitNo != undefined) {
        GetPrescriptionList(this.visitModel).then(res => {
          this.firstList = res.data.prescription;

        }).finally(() => {

        });
      }

      GetDisposeList(this.visitModel).then(res => {
        this.fourthList = res.data.dispose;

      }).finally(() => {

      });


    },
    // 初始化数据
    getList() {
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力导出中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      GetDiagnosedAndBase(this.queueForm).then(res => {
        this.baseInfo = res.data.baseInfo;
        this.tableDate = res.data.diagnosed;
      }).finally(() => {
        loading.close();
      });
      //检查
      GetInspectList(this.queueForm).then(res => {
        this.secondList = res.data.inspect;
      }).finally(() => {

      });
      //血糖
      GetBloodSugarList(this.queueForm).then(res => {
        this.fifthList = res.data.bloodSugar;

      }).finally(() => {

      });
      //化验查询
      GetAssayList(this.queueForm).then(res => {
        this.thirdList = res.data.baseTop;

      }).finally(() => {

      });
    },
    //查看门诊病历
    toSelectOutMr(row) {
      this.visitModel.visitDate = row.visiT_DATE
      this.visitModel.visitNo = row.visiT_NO
      GetOutMrList(this.visitModel).then(res => {

        this.outMrList = res.data.outMr;

      }).finally(() => {

      });
      this.mrVisiable = true

    },
    setVisiableClose(done) {
      done()
    },
    // 自定义高度变化更新高度
    handleResize() {
      this.tableHeight = window.innerHeight; // 更新高度数据
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.text-click {
  color: #00afff;
}

:hover.text-click {
  cursor: pointer;
  border-bottom: 1px solid #00afff;
}

.my-table {
  ::v-deep.el-table--medium .el-table__cell {
    padding: 0;
  }

  ::v-deep.el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    word-break: break-word;
    background-color: #f8f8f9;
    color: #515a6e;
    height: 30px;
    font-size: 14px;
  }

  ::v-deep.el-table th.el-table__cell>.cell {
    padding: 0;
  }

  ::v-deep.el-table--border .el-table__cell:first-child .cell {
    padding: 0;
  }

  ::v-deep.el-button+.el-button {
    margin-left: 2px;
  }

  ::v-deep.el-table .cell {
    padding: 1px;
  }

  /* ---el-table滚动条公共样式--- */
  // 滚动条的宽度
  ::v-deep.el-table__body-wrapper::-webkit-scrollbar {
    width: 10px; // 横向滚动条
    height: 10px; // 纵向滚动条 必写
  }

  // 滚动条的滑块
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }
}
</style>