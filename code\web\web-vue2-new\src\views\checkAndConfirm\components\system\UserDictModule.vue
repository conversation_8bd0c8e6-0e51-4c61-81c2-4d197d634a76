<template>
  <div class="user-dict-home">
    <div class="user-tree-master">
      <div class="tree-text">组织架构</div>
      <el-scrollbar style="height: 750px; overflow-x: hidden">
        <el-tree :data="treeData" :props="defaultProps" :show-checkbox="false" :highlight-current="true"
                 :expand-on-click-node="false" node-key="value" :default-expanded-keys="idArr" :indent="22"
        >
          <template #default="{ node, data }">
              <span v-if="!node.isLeaf" style="display: flex; align-items: center">
                <el-icon class="el-icon-folder-opened" v-if="node.expanded" style="margin: 0 6px 0 2px" size="20"
                ></el-icon>
                <el-icon class="el-icon-folder" v-else style="margin: 0 6px 0 2px" size="20"></el-icon>
                <small @click="handleNodeClick(node, data)">{{ node.label }}</small>
              </span>

            <span v-else style="display: flex; align-items: center">
                <el-icon class="el-icon-tickets" style="margin: 0 6px 0 2px" size="20"></el-icon>
                <small @click="handleNodeClick(node, data)">{{ node.label }}</small>
              </span>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
    <div class="user-table-master">
      <div class="role-form">
        <el-form :inline="true" :model="userForm" class="demo-form-inline">
          <el-form-item label="角色名称">
            <el-input v-model="userForm.userName" placeholder="支持模糊查询"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary">查询</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="role-table">
        <el-table :data="tableData" border style="width: 100%" height="700">
          <el-table-column align="center" type="index" label="序号" width="80"></el-table-column>
          <el-table-column align="center" prop="ygxm" label="姓名"></el-table-column>
          <el-table-column align="center" prop="xb" label="性别"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="userDictVerify(scope.row)">科室配置</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="user-dialog-master">
      <el-dialog
        title="系统科室配置"
        :visible.sync="dictStatus"
        width="50%"
      >
        <div>
          <div class="dialog-transfer">
            <el-transfer v-model="configData.configArray" :data="configData.deptAll"
                          :titles="titles"></el-transfer>
          </div>
          <div class="dialog-button">
            <el-button type="primary" @click="configSave">保存</el-button>
          </div>
        </div>

      </el-dialog>
    </div>
  </div>
</template>

<script>
import { GetHisDeptTree, GetOaUserList, UserHisMessageVerify,SaveUserConfig } from '@/api/checkAndConfirm/system'

export default {
  name: 'UserDictModule',
  props: [],
  components: {},
  data() {
    return {
      userForm: {
        userName: '',
        deptCode: '',
        pageNum: 1,
        pageSize: 10
      },
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      idArr: [],
      tableData: [],
      dictStatus: false,
      treeData: [],
      configData:{},
      titles: ['未选择','已选择'],
    }
  },
  created() {
    this.getHisDeptTree()
  },
  mounted() {
  },
  methods: {
    configSave(){
      let data = this.configData;
      let saveForm = {
        empNo: data.empNo,
        configArray: data.configArray,
        deptAll: data.deptAll,
        createBy: this.$store.getters.empNo,
      }
      SaveUserConfig(saveForm).then(res => {
        this.$message.success(res.message)
        // 强制更新视图
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      })
    },
    userDictVerify(row) {
      UserHisMessageVerify(row.userId).then(res => {
        this.dictStatus = true;
        this.configData = res.data;
      })
    },
    handleNodeClick(node, data) {
      this.userForm.deptCode = data.value
      this.getTableList()
    },
    getHisDeptTree() {
      GetHisDeptTree().then(res => {
        this.treeData = res.data
      })
    },
    getTableList() {
      GetOaUserList(this.userForm).then(res => {
        this.tableData = res.data
      })
    }
  }
}
</script>

<style scoped lang="scss">
.user-dict-home {
  display: flex;
  padding: 10px;
  width: 100%;

  .user-tree-master {
    width: 20%;
    border-right: 1px solid #1e47a1;

    .tree-text {
      border: 1px solid #2951ad;
      background-color: #2951ad;
      color: #ffffff;
      text-align: center;
      font-size: 20px;
      border-radius: 10px 0px 0px 0px;
    }

    ::v-deep.el-tree {
      /* ---- ---- ---- ---- ^（节点对齐）---- ---- ---- ---- */
      .el-tree-node {
        /* / 已展开的父节点 */
        /* ^ 叶子节点 */
        i.el-tree-node__expand-icon.is-leaf {
          &::before {
            display: none;
          }
        }

        /* / 叶子节点 */
        /* ^ 复选框 */
        .el-checkbox {
          margin: 0 7px 0 2px;

          .el-checkbox__inner {
            width: 14px;
            height: 14px;
            border-radius: 2px;
            border: 1px solid #bbb;
          }

          .el-checkbox__input.is-checked .el-checkbox__inner,
          .el-checkbox__input.is-indeterminate .el-checkbox__inner {
            border: 1px solid #5e7ce0;
          }
        }

        /* / 复选框 */
        .el-tree-node__content {
          small {
            font-size: 13px;
          }
        }
      }

      /* ---- ---- ---- ---- /（节点对齐）---- ---- ---- ---- */
      /* ---- ---- ---- ---- ^（文字高亮）---- ---- ---- ---- */
      .el-tree-node.is-current {
        .el-tree-node__content {
          small {
            color: #5e7ce0;
          }
        }

        .el-tree-node__children {
          small {
            color: unset;
          }
        }
      }

      /* ---- ---- ---- ---- /（文字高亮）---- ---- ---- ---- */
      /* ---- ---- ---- ---- ^（新增辅助线）---- ---- ---- ---- */
      /* ^ 树节点 */
      .el-tree-node {
        position: relative;
        width: auto;
        // width: max-content; // 显示文字宽度
        padding-left: 10px;

        &::before {
          width: 1px;
          height: 100%;
          content: "";
          position: absolute;
          top: -38px;
          bottom: 0;
          left: 0;
          right: auto;
          border-width: 1px;
          border-left: 1px solid #b8b9bb;
        }

        &::after {
          width: 16px;
          height: 13px;
          content: "";
          position: absolute;
          z-index: 0;
          left: 0;
          right: auto;
          top: 12px;
          bottom: auto;
          border-width: 1px;
          border-top: 1px solid #b8b9bb;
        }

        .el-tree-node__content {
          position: relative;
          z-index: 1;
          color: #000;
          padding-left: 0 !important;
          /* ^ 复选框 */
          .el-checkbox {
            margin: 0 10px 0 5.5px;
          }

          /* / 复选框 */
        }

        .el-tree-node__children {
          padding-left: 12px;
        }

        &:last-child::before {
          height: 50px;
        }
      }

      /* / 树节点 */
      /* ^ 第一层节点 */
      > .el-tree-node {
        padding-left: 0;

        &::before {
          border-left: none;
        }

        &::after {
          border-top: none;
        }
      }

      /* / 第一层节点 */
      /* ^ 叶子节点 */
      i.el-tree-node__expand-icon.is-leaf {
        display: none;
      }

      /* / 叶子节点 */
      /* ^ 设置子节点左外边距 */
      .el-tree-node__content:has(.is-leaf) {
        // color: #00ffff;
        margin-left: 0px !important;
      }

      /* / 设置子节点左外边距 */
      /* ---- ---- ---- ---- /（新增辅助线）---- ---- ---- ---- */
    }
  }

  .user-table-master {
    padding-left: 10px;
    width: 70%;
  }

  .user-dialog-master{
    ::v-deep.el-transfer-panel .el-checkbox__label {
      padding-left: 40px;
    }
    .dialog-transfer{
      display: flex;
      justify-content: space-around;
    }
    .dialog-button{
      display: flex;
      justify-content: space-around;
      margin-top: 20px;
    }
  }
}
</style>
