import request from "@/utils/request";

/**
 * 预约中心首页参数第一次获取
 * @param data 查询参数
 * @returns {*}
 * @constructor
 */
export function GetReservationCenterInfo(data) {
  return request({
    url: "/ReservationExam/GetReservationCenterInfo",
    method: "get",
    params: data,
  });
}

/**
 * 预约中心首页确认成功后再次调用
 * @param data 查询参数
 * @returns {*}
 * @constructor
 */
export function GetReservationCenterInfoTwo(data) {
  return request({
    url: "/ReservationExam/GetReservationCenterInfoTwo",
    method: "get",
    params: data,
  });
}

/**
 * 获取windowId信息
 * @param data
 * @returns {*}
 * @constructor
 */
export function GetUnitOrWindowTree(data) {
  return request({
    url: "/ReservationExam/GetUnitOrWindowTree",
    method: "post",
    data: data,
  });
}

/**
 * 获取当前项目 unit 、 window 信息
 * @param data
 * @returns {*}
 * @constructor
 */
export function GetUnitAndWindowTree(data) {
  return request({
    url: "/ReservationExam/GetUnitAndWindowTree",
    method: "post",
    data: data,
  });
}

/**
 * 获取时间集合
 * @param data
 * @returns {*}
 * @constructor
 */
export function GetAppointmentList(data) {
  return request({
    url: "/ReservationExam/GetAppointmentList",
    method: "post",
    data: data,
  });
}

export function GetTimeDispose(data) {
  return request({
    url: "/ReservationExam/GetTimeDispose?date=" + data,
    method: "get",
  });
}

/**
 * 时间预约
 * @param data
 * @returns {*}
 * @constructor
 */
export function TimeAppointment(data) {
  return request({
    url: "/ReservationExam/TimeAppointment",
    method: "post",
    data: data,
  });
}
