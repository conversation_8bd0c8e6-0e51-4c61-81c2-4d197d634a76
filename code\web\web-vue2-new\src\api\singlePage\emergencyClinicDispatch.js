import request from '@/utils/request2'

export function getClinicDispatchApi(data) {
  return request({
    url: '/singlePage/emergencyClinicDispatch/inquire',
    method: 'post',
    data: data
  })
}
export function exportClinicDispatch(data) {
  return request({
    url: '/singlePage/emergencyClinicDispatch/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

export function getClinicDispatchByPatientIdApi(patientId) {
  return request({
    url: '/singlePage/emergencyClinicDispatch/inquire/' + patientId,
    method: 'get',
  })
}
