<template>
  <div>
    <div class="tabs-home">
      <el-tabs v-model="activeName" type="card" @tab-click="tabsClick" v-for="(item, index) in activeItem" :key="index"
        :class="{ tabsButtonOne: activeName === item.value }">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </el-tabs>
    </div>
    <div :style="maxHeight" style="border: 1px solid slategray;border-radius: 3px;">
      <div v-if="activeName === 'mr'">
        <module-mr :row-data="rowData" :max-height="maxHeight" :key="activeKey"></module-mr>
      </div>
      <div v-if="activeName === 'exam'">
        <module-exam :row-data="rowData" :max-height="maxHeight" :key="activeKey"></module-exam>
      </div>
      <div v-if="activeName === 'test'">
        <module-text :row-data="rowData" :max-height="maxHeight" :key="activeKey"></module-text>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleMr from './moduleMr.vue'
import ModuleExam from './moduleExam.vue'
import ModuleText from './moduleText.vue'

export default {
  name: 'extraFunction',
  props: ['rowData', 'maxHeight'],
  components: { ModuleText, ModuleExam, ModuleMr },
  data() {
    return {
      activeName: 'mr',
      activeItem: [{
        label: '病历',
        value: 'mr'
      },
      {
        label: '检查',
        value: 'exam'
      }, {
        label: '检验',
        value: 'test'
      }],
      activeKey: 0,
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    tabsClick() {
      ++this.activeKey;
    },
  }
}
</script>

<style scoped lang="scss">
.tabs-home {
  display: flex;
  margin-left: 5px;

  ::v-deep.el-tabs__header {
    padding: 0;
    margin: 0;
  }

  ::v-deep.el-tabs--card>.el-tabs__header .el-tabs__nav {
    border-radius: 0;
  }

  ::v-deep.el-tabs__item {
    padding: 0 20px;
    font-size: 16px;
    height: 35px;
    color: #303133;
    line-height: 35px;
  }

  ::v-deep.el-dialog__body {
    padding: 10px 20px;
  }

  .tabsButtonOne {
    ::v-deep.el-tabs__nav {
      background-color: #FFFFFF !important;
      color: black !important;
    }

    ::v-deep.el-tabs__item {
      color: black !important;
    }
  }

  ::v-deep.el-tabs__nav {
    :hover {
      background-color: #FFFFFF;
      color: black;
      transform: scale(1.1);
    }
  }
}
</style>
