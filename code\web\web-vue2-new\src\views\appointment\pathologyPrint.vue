<template>
    <!-- 病例打印界面 -->
    <div class="app-container">
        <div id="printArea">
            <table width="100%" border="0" cellpadding="0" cellspacing="1">
                <tr class="tr">
                    <td class="centered">
                        河南宏力医院病理科
                    </td>
                </tr>
                <tr class="tr1">
                    <td class="centered">
                        <span>病</span><span class="span">理</span><span class="span">检</span><span class="span">查</span>
                        <span class="span">申</span><span class="span">请</span><span class="span">单</span>
                    </td>
                </tr>
                <tr class="tr2">
                    <td class="centered1 flex-row">
                        <div class="left">
                            患者ID号:<input v-model="fromCard.patienT_ID" type="text" class="unit_input" />
                        </div>
                        <div class="right">
                            病理号:<input type="text" class="unit_input" />
                        </div>
                    </td>
                </tr>
            </table>
            <table border="1px" width="100%" cellspacing="0px" cellpadding="6px" align="center">
                <tr class="tr2">
                    <td colspan="2">
                        患者姓名:<input v-model="fromCard.name" type="text" class="unit_input" :show-overflow-tooltip="true" />
                    </td>
                    <td colspan="2">
                        性别:<input v-model="fromCard.sex" type="text" class="unit_input" :show-overflow-tooltip="true" />
                    </td>
                    <td colspan="2">
                        年龄:<input v-model="fromCard.age" type="text" class="unit_input" :show-overflow-tooltip="true" />
                    </td>
                    <td colspan="2">
                        申请号:<input v-model="fromCard.exaM_NO" type="text" class="unit_input1"
                            :show-overflow-tooltip="true" />
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="2">
                        送检医院:<input v-model="fromCard.deliveR_HOSPITAL" type="text" class="unit_input"
                            :show-overflow-tooltip="true" />
                    </td>
                    <td colspan="2">
                        科别:<input v-model="fromCard.reQ_DEPT_NAME" type="text" class="unit_input2"
                            :show-overflow-tooltip="true" />
                    </td>
                    <td colspan="2">
                        住院号:<input v-model="fromCard.inP_NO" type="text" class="unit_input" :show-overflow-tooltip="true" />
                    </td>
                    <td colspan="2">
                        床号:<input v-model="fromCard.beD_NO" type="text" class="unit_input" :show-overflow-tooltip="true" />
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="4">
                        电话:<input v-model="fromCard.relatioN_PHONE_NUM" type="text" style="width: 300px" class="unit_input"
                            :show-overflow-tooltip="true" />
                    </td>
                    <td colspan="4">
                        地址:<input v-model="fromCard.address" type="text" style="width: 300px" class="unit_input"
                            :show-overflow-tooltip="true" />
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div style="height: 100px;">
                            送检组织名称:
                            <div style="margin-top: 10px;">
                                <el-input v-model="fromCard.inspectioN_SAMPLE_NAME" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 3 }" placeholder="请输入" class="input"
                                    :show-overflow-tooltip="true" />
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div style="height: 100px;">
                            取材部位:
                            <div style="margin-top: 10px;">
                                <el-input v-model="fromCard.samplE_LOCATION" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 3 }" placeholder="请输入" class="input"
                                    :show-overflow-tooltip="true" />
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div style="margin-top: 20px;">
                            件数:
                            <input v-model="fromCard.samplE_NUM" type="text" style="width: 211px" class="unit_input"
                                :show-overflow-tooltip="true" />
                        </div>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div>
                            传染性疾病检测:
                        </div>
                        <div style="margin-top: 10px;">
                            <span>
                                HBV:<input v-model="fromCard.hbv" type="text" class="unit_input"
                                    :show-overflow-tooltip="true" />
                            </span>
                            <span class="span">
                                HCV:<input v-model="fromCard.hcv" type="text" class="unit_input"
                                    :show-overflow-tooltip="true" />
                            </span>
                            <span class="span">
                                HIV:<input v-model="fromCard.hiv" type="text" class="unit_input"
                                    :show-overflow-tooltip="true" />
                            </span>
                            <span class="span">
                                TB:<input v-model="fromCard.tb" type="text" class="unit_input"
                                    :show-overflow-tooltip="true" />
                            </span>
                            <span class="span">
                                TP:<input v-model="fromCard.tp" type="text" class="unit_input"
                                    :show-overflow-tooltip="true" />
                            </span>
                            <span class="span">
                                其它:<input v-model="fromCard.infecT_OTHER" type="text" class="unit_input"
                                    :show-overflow-tooltip="true" />
                            </span>
                        </div>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div style="height: 200px;">
                            <div>
                                病历摘要及临床检查所见:(包含病史,体征,实验室检查,肿瘤发现时间,部位,大小,
                            </div>
                            <div style="margin-top: 10px;">
                                硬度,生长速度,有无转移,是否有既往史,手术史;印象学资料)
                            </div>
                            <div style="margin-top: 10px;">
                                <el-input v-model="fromCard.cliN_SYMP" type="textarea"
                                    :autosize="{ minRows: 4, maxRows: 4 }" placeholder="请输入" :show-overflow-tooltip="true"
                                    class="custom-textarea" />
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div>
                            妇科信息:
                        </div>
                        <div style="margin-top: 10px;">
                            <span>
                                末次月经: <el-date-picker v-model="fromCard.menstruaL_PERIOD_DATE" type="date"
                                    placeholder="选择日期" size="mini" style="width: 150px;" />
                            </span>
                            <span class="span5">
                                是否绝经:
                                <el-radio v-model="fromCard.menopausE_STATUS" label="Y">是</el-radio>
                                <el-radio v-model="fromCard.menopausE_STATUS" label="N">否</el-radio>
                            </span>
                            <span class="span5">
                                妊娠史:
                                <el-radio v-model="fromCard.pregnancY_HISTORY" label="Y">是</el-radio>
                                <el-radio v-model="fromCard.pregnancY_HISTORY" label="N">否</el-radio>
                            </span>
                        </div>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div style="height: 100px;">
                            手术名称及手术所见:
                            <div style="margin-top: 10px;">
                                <el-input v-model="fromCard.surgicaL_PROCESS" type="textarea"
                                    :autosize="{ minRows: 2, maxRows: 3 }" placeholder="请输入" class="input"
                                    :show-overflow-tooltip="true" />
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div>
                            临床诊断:<input v-model="fromCard.cliniC_DIAGNOSIS" type="text" style="width: 600px"
                                class="unit_input" :show-overflow-tooltip="true" />
                        </div>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div>
                            检查项目:<input v-model="fromCard.exaM_ITEM" type="text" style="width: 600px" class="unit_input"
                                :show-overflow-tooltip="true" />
                        </div>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div>
                            既往病理号及诊断:<input v-model="fromCard.pathlogY_DIAGNOSIS" type="text" style="width: 500px"
                                class="unit_input" :show-overflow-tooltip="true" />
                        </div>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="4">
                        <span>
                            标本离体时间: <el-date-picker v-model="fromCard.specimeN_LEVAVE_TIME" type="datetime"
                                placeholder="选择日期时间" size="mini" style="width: 200px;" />
                        </span>
                    </td>
                    <td colspan="4">
                        <span>
                            标本固定时间: <el-date-picker v-model="fromCard.specimeN_FIXED_TIME" type="datetime"
                                placeholder="选择日期时间" size="mini" style="width: 200px;" />
                        </span>
                    </td>
                </tr>
                <tr class="tr2">
                    <td colspan="8">
                        <div>
                            <span>
                                送检日期:<el-date-picker v-model="fromCard.reQ_DATE_TIME" type="datetime" placeholder="选择日期时间"
                                    size="mini" style="width: 175px;" />
                            </span>
                            <span class="span">
                                送检医师:<input type="text" v-model="fromCard.reQ_PHYSICIAN" class="unit_input"
                                    :show-overflow-tooltip="true" />
                            </span>
                            <span class="span">
                                接收日期:<el-date-picker v-model="fromCard.receivE_DATE_TIME" type="datetime"
                                    placeholder="选择日期时间" size="mini" style="width: 175px;" />
                            </span>
                            <span class="span">
                                接收人:<input type="text" v-model="fromCard.receivE_DOCTOR" class="unit_input"
                                    :show-overflow-tooltip="true" />
                            </span>
                        </div>
                    </td>
                </tr>
            </table>
            <div>
                注意:1.病理检查申请单与盛放标本容器上信息内容必须一致,否则病理科安“不合格样本”处理,拒收。
            </div>
            <div class="div">
                2.新鲜标本须在离体30min内固定与10%中性福尔马林内(冰冻切片及特殊检查项目除外)。
            </div>
            <div class="div">
                3.手术取出之标本,务必全部送检,请勿擅自切开或分送他处,从而影响诊断。
            </div>
            <div class="div1">
                <span>
                    *检查科室地址:门诊楼3楼
                </span>
                <span class="span2">
                    联系电话:0373-8882184
                </span>
            </div>
        </div>
        <div class="div2">
            <el-button v-print="print" type="primary">打印</el-button>
            <el-button type="primary" @click="updatePrint">修改</el-button>
        </div>
    </div>
</template>

<script>
import { GetPathologyPrintDetail, UpdatePathologyPrint } from "@/api/appointment/pathologyPrint";
export default {
    // 接收父组件传递的参数
    props: {
        examNo: {
            type: String,
            required: true,
        }
    },
    data() {
        return {
            fromCard: {},
            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {
                }, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {
                }, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeEntryIframe() {
                    const cells = document.querySelectorAll('.cell');
                    [].slice.call(cells).forEach((item) => {
                        // 为了让表格中的内容自动换行，不需要的话可以删掉
                        item.style.whiteSpace = 'pre-wrap'
                    })
                },
                openCallback() {
                }, // 调用打印之后的回调事件
                closeCallback() {
                }, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: ''
            }
        }
    },

    created() {
        this.getPrint();
    },

    methods: {
        getPrint() {
            // 详情
            this.fromCard.examNo = this.examNo;
            GetPathologyPrintDetail(this.fromCard).then((response) => {
                if (response.data.printDetailRetModels.length > 0) {
                    this.fromCard = response.data.printDetailRetModels[0]
                }
            });
        },

        // 修改按钮操作
        updatePrint() {
            UpdatePathologyPrint(this.fromCard).then((response) => {
                this.$modal.msgSuccess("修改数据成功");
                this.$emit('get-list');
                this.$emit('close-drawer');
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.app-container {
    width: 960px;
    margin-left: auto;
    margin-right: auto;
}

.tr {
    height: 30px;
    font-size: 25px;
}

.tr1 {
    height: 30px;
    font-size: 35px;
}

.tr2 {
    font-size: 20px;
    height: 31px;
}

.span {
    margin-left: 10px;
}

.span1 {
    margin-left: 100px;
}

.span2 {
    margin-left: 300px;
}

.span3 {
    margin-left: 200px;
}

.span5 {
    margin-left: 60px;
}

.div {
    margin-left: 36px;
    margin-top: 10px;
}

.div1 {
    margin-top: 10px;
}

.div2 {
    text-align: right;
    margin-top: 10px;
}

.centered {
    // 水平居中
    text-align: center;
    // 垂直居中
    vertical-align: middle;
}

.centered1 {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.input {
    font-size: 18px;
}

::v-deep.custom-textarea .el-textarea__inner {
    font-size: 18px;
    color: #000000;
}

.unit_input {
    border: none;
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 88px;
    font-size: 18px;
    font-weight: 100px;
    margin-left: 3px;
}

.unit_input1 {
    border: none;
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 100px;
    font-size: 18px;
    font-weight: 100px;
    margin-left: 3px;
}

.unit_input2 {
    border: none;
    border-bottom: 1px solid rgb(0, 0, 0);
    outline: none;
    width: 130px;
    font-size: 18px;
    font-weight: 100px;
    margin-left: 3px;
}

::v-deep .el-radio {
    .el-radio__input {
        .el-radio__inner {
            border-radius: 2px;
        }
    }

    .el-radio__input.is-checked .el-radio__inner::after {
        content: '';
        width: 8px;
        height: 6px;
        border: 2px solid white;
        border-top: transparent;
        border-right: transparent;
        text-align: center;
        display: block;
        position: absolute;
        top: 2px;
        left: 1px;
        vertical-align: middle;
        transform: rotate(-45deg);
        border-radius: 0px;
        background: none;
    }
}

.left,
.right {
    display: inline-block;
}
</style>