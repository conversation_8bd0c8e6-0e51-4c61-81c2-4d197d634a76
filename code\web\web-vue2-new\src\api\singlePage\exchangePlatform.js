import request from '@/utils/request3'

export function getSendList(data) {
  return request({
    url: 'DevOpsCloud-exchangePlatform/exchangePlatform/send/data',
    method: 'post',
    data: data
  })
}

export function getReceptionList(data) {
  return request({
    url: 'DevOpsCloud-exchangePlatform/exchangePlatform/reception/data',
    method: 'post',
    data: data
  })
}

export function sendApi(data) {
  return request({
    url: 'DevOpsCloud-exchangePlatform/exchangePlatform/send',
    method: 'post',
    data: data
  })
}

export function getUserInfo(id) {
  return request({
    url: 'DevOpsCloud-auth/token/userInfo',
    method: 'get',
  })
}

export function uploadFile2(file,md5) {
  return request({
    url: '/DevOpsCloud-system/ua/file/upload2/'+ md5 + '/exchange-platform',
    method: 'post',
    data: file,
  })
}

export function userTreeAll() {
  return request({
    url: '/DevOpsCloud-user-his/system/staff/ua/listUserTreeAll',
    method: 'get',
  })
}

export function getFileInfo(id) {
  return request({
    url: '/DevOpsCloud-exchangePlatform/exchangePlatform/fileInfo/' + id,
    method: 'get',
  })
}

export function deleteSendById(id) {
  return request({
    url: '/DevOpsCloud-exchangePlatform/exchangePlatform/delete/send/' + id,
    method: 'delete',
  })
}

export function userLogin(data) {
  return request({
    url: '/DevOpsCloud-auth/ua/login',
    method: 'post',
    params: data
  })
}


