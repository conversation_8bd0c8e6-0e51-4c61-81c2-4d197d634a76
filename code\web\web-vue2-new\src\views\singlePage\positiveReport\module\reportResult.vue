<template>
  <div>
    <div class="element-table">
      <el-table :data="patientData" style="width: 100%" border>
        <el-table-column prop="PATIENT_ID" align="center" label="ID"></el-table-column>
        <el-table-column prop="NAME" align="center" label="病人姓名"></el-table-column>
        <el-table-column prop="SEX" align="center" label="性别"></el-table-column>
        <el-table-column prop="DATE_OF_BIRTH" align="center" label="出生日期"></el-table-column>
        <el-table-column prop="NATION" align="center" label="民族"></el-table-column>
        <el-table-column prop="CHARGE_TYPE" align="center" label="费别"></el-table-column>
        <el-table-column prop="MAILING_ADDRESS" align="center" label="家庭住址"></el-table-column>
      </el-table>
    </div>
    <div class="element-table">
      <el-table :data="resultData" :span-method="objectSpanMethod" style="width: 100%" :height="maxHeight-300" border>
        <el-table-column prop="receivedate" align="center" label="报告日期" width="90"></el-table-column>
        <el-table-column prop="patid" align="center" label="病人ID" width="85"></el-table-column>
        <el-table-column prop="patname" align="center" label="病人姓名" width="80"></el-table-column>
        <el-table-column prop="age" align="center" label="年龄" width="70"></el-table-column>
        <el-table-column prop="cname" align="center" label="项目组合" width="250"></el-table-column>
        <el-table-column prop="pname" align="center" label="项目名称" width="250"></el-table-column>
        <el-table-column prop="reportdesc" align="center" label="结果" width="120"></el-table-column>
        <el-table-column prop="technician" align="center" label="开单医生" width="80"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getPositiveReportByResultPatientId } from '@/api/singlePage/positiveReport'

export default {
  name: 'reportResult',
  props: ['rowData', 'maxHeight'],
  components: {},
  data() {
    return {
      patientData: [],
      resultData: [],
      mergeArr: ["receivedate","patid","patname","age","cname","technician"],
      mergeObj: {},
    }
  },
  created() {
    this.getPositiveReportByResult()
  },
  mounted() {
  },
  methods: {
    getPositiveReportByResult() {
      const loading = this.$loading({
        lock: true,
        text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      getPositiveReportByResultPatientId(this.rowData.PATID).then(res => {
        this.patientData = res.data.patient
        this.resultData = res.data.result
        this.getSpanArr(res.data.result);
      }).finally(() => {
        loading.close();
      })
    },
    getSpanArr(data) {
      // 循环需要合并的单元格数据
      this.mergeArr.forEach((key, index1) => {
        let count = 0; // 用来记录需要合并行的起始位置
        this.mergeObj[key] = []; // 记录每一列的合并信息
        data.forEach((item, index) => {
          // index == 0表示数据为第一行，直接 push 一个 1
          if (index === 0) {
            this.mergeObj[key].push(1);
          } else {
            // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并 并push 一个 0 作为占位
            if (item[key] === data[index - 1][key]) {
              this.mergeObj[key][count] += 1;
              this.mergeObj[key].push(0);
            } else {
              // 如果当前行和上一行其值不相等
              count = index; // 记录当前位置
              this.mergeObj[key].push(1); // 重新push 一个 1
            }
          }
        });
      });
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      // 判断列的属性
      if (this.mergeArr.indexOf(column.property) !== -1) {
        // 判断其值是不是为0
        if (this.mergeObj[column.property][rowIndex]) {
          return [this.mergeObj[column.property][rowIndex], 1];
        } else {
          // 如果为0则为需要合并的行
          return [0, 0];
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../../assets/styles/singlePage";
</style>
