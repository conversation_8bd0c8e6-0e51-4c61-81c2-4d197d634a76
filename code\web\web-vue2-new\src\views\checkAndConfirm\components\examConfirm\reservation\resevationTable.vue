<template>
  <div class="reservation-table-home">
    <div class="left-master">
      <div class="left-user" >
        <div class="user-title" :style="styleData.left.user.title">
          患者基础信息
        </div>
        <div class="user-info" :style="styleData.left.user.height">
          <div class="user-master" :style="styleData.left.user.info">
            <div class="user-img">
              <el-avatar
                :src="manImg"
                v-if="thisTable.sex === '男'"
                :size="styleData.left.user.img"
              ></el-avatar>
              <el-avatar
                :src="woManImg"
                v-else-if="thisTable.sex === '女'"
                :size="styleData.left.user.img"
              ></el-avatar>
              <el-avatar
                :src="manImg"
                v-else
                :size="styleData.left.user.img"
              ></el-avatar>
            </div>
            <div class="user-msg">
              <div class="user-msg-1" :style="styleData.left.user.size">
                <span>姓名：{{ thisTable.name }}</span>
                <span>ID：{{ thisTable.patientId }}</span>
              </div>
              <div class="user-msg-1" :style="styleData.left.user.size">
                <span>性别：{{ thisTable.sex }}</span>
                <span>年龄：{{ thisTable.age }}</span>
              </div>
            </div>
          </div>
          <div class="user-exam" :style="styleData.left.user.exam">
            <span>申请号：{{ thisTable.examNo }}</span>
            <span>住院次数：{{ thisTable.visitId }}</span>
          </div>
        </div>
        <div class="left-table">
          <div class="my-header-text" :style="styleData.left.case.title">
            项目列表
          </div>
          <el-scrollbar :style="styleData.left.scrollbar">
            <div v-if="styleData.left.case.table.one">
              <div class="case-table" @click="tableItemDisplay(item)" v-for="item in tableData.table" :key="item.index"
                :class="{tableOne: item.color === '1',tableTwo: item.color === '2',tableThree: item.color === '3',
                tableZero: item.color === '0',}">
                <el-tooltip effect="light" placement="right">
                  <div slot="content">
                    <div class="time-table-img" @click="AiButton(item)">
                      <img :src="answerQuestionsImg" :style="styleData.right.img" alt=""/>
                    </div>
                  </div>
                  <div class="case-table-item" :style="styleData.left.case.table.height">
                    <div>检查项目：
                      <span :style="styleData.left.case.table.size1">
                        {{ item.examItem }}
                        <span v-if="item.resultStatus === '4'">(已做)</span>
                        <span v-else-if="item.resultStatus === '2'">(已确认)</span>
                        <span v-else-if=" item.resultStatus === null && item.appointmentTime !== null">(已预约)</span>
                        <span v-else>(未预约)</span>
                      </span>
                    </div>
                    <div :style="styleData.left.case.table.size2">检查位置：{{ item.examPositionName }}</div>
                    <div style="display: flex" :style="styleData.left.case.table.size2">
                      <div :style="styleData.left.case.table.width2">申请科室：{{ item.applyDeptName }}</div>
                      <div>申请医师：{{ item.applyDoctorName }}</div>
                    </div>
                    <div :style="styleData.left.case.table.size2">申请时间：{{ item.createDate }}</div>
                    <div :style="styleData.left.case.table.size2" style="display: flex">
                      <div :style="styleData.left.case.table.width2">预约日期：{{ item.appointmentDate }}</div>
                      <div>预约时间：{{ item.appointmentTime }}</div>
                    </div>
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div v-if="styleData.left.case.table.two">
              <div class="case-table" @click="tableItemDisplay(item)" v-for="item in tableData.table" :key="item.index"
                :class="{ tableOne: item.color === '1',  tableTwo: item.color === '2',  tableThree: item.color === '3',
                  tableZero: item.color === '0',}">
                <el-tooltip effect="light" placement="right">
                  <div slot="content">
                    <div class="time-table-img" @click="AiButton(item)">
                      <img :src="answerQuestionsImg" :style="styleData.right.img" alt=""/>
                    </div>
                  </div>
                  <div class="case-table-item" :style="styleData.left.case.table.height">
                    <div>检查项目：<span :style="styleData.left.case.table.size1">{{ item.examItem }}
                        <span v-if="item.resultStatus === '4'">(已做)</span>
                        <span v-else-if="item.resultStatus === '2'">(已确认)</span>
                        <span v-else-if=" item.resultStatus === null && item.appointmentTime !== null">(已预约)</span>
                        <span v-else>(未预约)</span>
                      </span>
                    </div>
                    <div :style="styleData.left.case.table.size2">检查位置：{{ item.examPositionName }}</div>
                    <div :style="styleData.left.case.table.size2">申请科室：{{ item.applyDeptName }}</div>
                    <div :style="styleData.left.case.table.size2">申请医师：{{ item.applyDoctorName }}</div>
                    <div :style="styleData.left.case.table.size2">申请时间：{{ item.createDate }}</div>
                    <div :style="styleData.left.case.table.size2">预约日期：{{ item.appointmentDate }}</div>
                    <div :style="styleData.left.case.table.size2">预约时间：{{ item.appointmentTime }}</div>
                  </div>
                </el-tooltip>
              </div>
            </div>
            <div v-if="styleData.left.case.table.three">
              <div class="case-table" @click="tableItemDisplay(item)" v-for="item in tableData.table" :key="item.index"
                :class="{tableOne: item.color === '1',tableTwo: item.color === '2',tableThree: item.color === '3',
                  tableZero: item.color === '0',}">
                <el-tooltip effect="light" placement="right">
                  <div slot="content">
                    <div class="time-table-img" @click="AiButton(item)">
                      <img :src="answerQuestionsImg" :style="styleData.right.img" alt=""/>
                    </div>
                  </div>
                  <div class="case-table-item" :style="styleData.left.case.table.height">
                    <div>
                      检查项目：<span :style="styleData.left.case.table.size1">{{ item.examItem }}
                        <span v-if="item.resultStatus === '4'">(已做)</span>
                        <span v-else-if="item.resultStatus === '2'">(已确认)</span>
                        <span v-else-if="item.resultStatus === null && item.appointmentTime !== null">(已预约)</span>
                        <span v-else>(未预约)</span>
                      </span>
                    </div>
                    <div :style="styleData.left.case.table.size2">检查位置：{{ item.examPositionName }}</div>
                    <div :style="styleData.left.case.table.size2">申请科室：{{ item.applyDeptName }}</div>
                    <div :style="styleData.left.case.table.size2">申请医师：{{ item.applyDoctorName }}</div>
                    <div :style="styleData.left.case.table.size2">申请时间：{{ item.createDate }}</div>
                    <div :style="styleData.left.case.table.size2">预约日期：{{ item.appointmentDate }}</div>
                    <div :style="styleData.left.case.table.size2">预约时间：{{ item.appointmentTime }}</div>
                  </div>
                </el-tooltip>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import man from "@/assets/icons/svg/man.png";
import woMan from "@/assets/icons/svg/woman.png";
import answerQuestions from "@/assets/icons/answerQuestions1.png";
import {GetReservationCenterInfo,GetReservationCenterInfoTwo} from "@/api/checkAndConfirm/reservationExam";

export default {
  name: 'resevationTable',
  props: ['styleData','appointmentType',],
  components: {},
  data() {
    return {
      tableForm:{
        examNos: [],
        empNo:'',
        unitId: '',
        windowId: '',
        thisTime: '',
        TimeList: [],
      },
      examNos: [],
      tableData: {},
      thisTable: {},
      manImg: man,
      woManImg: woMan,
      answerQuestionsImg: answerQuestions,
      autherEntity: {
        autherKey: 'DA12FB821147938CA09641D3B51365C5',
        userGuid: '',
        serialNumber: '',
        doctorGuid: '',
        doctorName: '',
        department: '',
        hospitalGuid: '1',
        hospitalName: '河南宏力医院',
        customEnv: '1',
        flag: 'm'
      },
    }
  },
  created() {
    this.ListFetchData();
  },
  mounted() {
  },
  methods: {
    //列表点击切换处理
    tableItemDisplay(data){
      let table = this.tableData.table;
      table.forEach(function (item, index) {
        if (item.color === "1") {
          if (
            item.appointmentTime === null ||
            item.appointmentTime === "" ||
            item.appointmentTime === undefined
          ) {
            item.color = "2";
          } else {
            item.color = "0";
          }
          if (
            item.status !== null &&
            item.status !== "" &&
            item.status !== undefined
          ) {
            if (item.status === "4") {
              item.color = "3";
            }
          }
        }
      });
      data.color = "1";
      this.thisTable = data;
      this.createInit(data.patientId,data.patientId + "0000",
      this.$store.getters.empNo,this.$store.getters.name,"");
      this.setRowTable(data);
      this.emitRow(data);
    },
    //列表参数初始化
    ListFetchData(){
      this.createInit("","0000",
        "","","");
      let data = this.$store.getters.appointmentExamNos;
      if (data){
        this.GetTableData(data);
      }
    },
    //获取列表数据
    GetTableData(data){
      this.examNos = data;
      let tableForm = {
        examNos: this.examNos,
      }
      console.log(tableForm)
      GetReservationCenterInfo(tableForm).then(res => {
        let data = res.data;
        console.log(res.data);
        this.tableData = data;
        this.thisTable = data.thisTable;
        this.setTableData(data);
        this.setRowTable(data.thisTable);
        this.emitCreate(data);
      })
    },
    GetTableDataTwo(data){
      let tableForm = {
        examNos: this.examNos,
        empNo:this.$store.getters.empNo,
        unitId: data.thisUnitId,
        windowId: data.thisWindowId,
        thisTime: data.thisTime,
        TimeList: data.TimeList,
      }
      GetReservationCenterInfoTwo(tableForm).then(res => {
        let data = res.data;
        this.tableData = data;
        this.thisTable = data.thisTable;
        this.setTableData(data);
        this.setRowTable(data.thisTable);
        this.emitCreate(data);
        //判断当前项目是否全部预约成功
        if (this.appointmentType === 1){
          this.judgeIsTheReservationComplete(data.table);
        }
      })
    },
    //ai查看按钮
    AiButton(item) {
      this.knowledgeBase(item);
    },
    //知识库查看
    knowledgeBase(item) {
      let data = {
        name: item.examItem,
        type: "12",
        id: item.examItemCode,
        customEnv: "1",
        isActicleDetail: "1",
      };
      mayson.openArticleDetail(data);
    },
    init(autherEntity) {
      HM.maysonLoader(autherEntity, function(mayson) {
        // mayson.closeMaysonPro();
        mayson.setDrMaysonConfig('m', 1)
        window.mayson = mayson
        mayson.ai()
      })
    },
    //惠美初始化方法加载
    createInit(userGuid, serialNumber, doctorGuid, doctorName, department) {
      this.autherEntity.userGuid = userGuid
      this.autherEntity.serialNumber = serialNumber
      this.autherEntity.doctorGuid = doctorGuid
      this.autherEntity.doctorName = doctorName
      this.autherEntity.department = department
      this.init(this.autherEntity)
    },

    setTableData(data){
      this.$store.commit('SET_RESERVATION_TABLE', data)
    },
    setRowTable(data){
      this.$store.commit('SET_RESERVATION_ROW_TABLE', data)
    },
    emitRow(data){
      this.$emit("row-table",data);
    },
    emitCreate(data){
      this.$emit("emit-create",data);
    },
    judgeIsTheReservationComplete(data){
      let status = true;
      data.forEach(item => {
        if (!item.appointmentDate && !item.appointmentTime){
          status = false;
        }
      })
      if (status){
        this.$msgbox.alert(
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
          '当前项目已全部预约完成' + '</div><div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">>是否要一键保存</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
          this.$emit("table-save",status);
        })
      }
    },
  }
}
</script>

<style scoped lang="scss">
.reservation-table-home{
  padding: 0;
  margin: 0;

  .left-master{

    .left-user{

      .user-title{
        background: #185f7d;
        text-align: center;
        color: #ffffff;
        font-size: 16px;
      }

      .user-info{
        background: rgb(#b4bccc, 0.1);
        height: 200px;

        .user-master{
          display: flex;
          border-bottom: 1px solid #b4bccc;
          .user-img{
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            align-items: center;
            width: 20%;
            height: 100%;
            border-right: 1px solid #b4bccc;
          }

          .user-msg{
            width: 80%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-evenly;

            .user-msg-1 {
              height: 33.3%;
              display: flex;
              align-items: center;
              justify-content: space-around;

              span {
                width: 45%;
              }
            }

            .user-msg-2 {
              margin-left: 3%;
            }
          }
        }
        .user-exam {
          display: flex;
          justify-content: space-around;

          span {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
          }
        }
      }

      .left-table{
        .case-table {
          :hover {
            background: #1890ff !important;
            color: #ffffff;
            cursor: pointer;
          }
        }

        .case-table-item {
          display: flex;
          height: 150px;
          border: 1px solid #3f536e;
          flex-direction: column;
          justify-content: space-around;
        }

        ::v-deep.el-scrollbar__wrap {
          overflow: auto;
          height: 100%;
        }

        .my-header-text {
          border-top: 1px solid #b4bccc;
          background-color: #f9f9fa;
          text-align: center;
          letter-spacing: 10px;
          color: #1b2947;
          font-weight: bolder;
        }

        .my-sketch-span {
          font-weight: bolder;
          margin-left: 2%;
        }

        ::v-deep.el-textarea {
          position: relative;
          width: 95%;
          vertical-align: bottom;
          font-weight: 1500;
          font-size: 12px;
          margin-left: 1.5%;
          border: 1px solid #00afff;
          border-radius: 4px;
        }

        ::v-deep.el-textarea.is-disabled .el-textarea__inner {
          background-color: #ffffff;
          border-color: #e4e7ed;
          color: black;
          cursor: not-allowed;
        }


        .tableZero {
          background: #ffffff !important;
          color: black;
        }

        .tableOne {
          background: #1890ff !important;
          color: #ffffff;
        }

        .tableTwo {
          color: #ffffff;
          background: #20b2aa !important;
        }

        .tableThree {
          color: #ffffff;
          background: red !important;
        }
      }
    }

  }
}
</style>
