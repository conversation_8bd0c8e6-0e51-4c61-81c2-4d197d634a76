// 框架管理模块
import request from '@/utils/request'

export function GetFrameworkFile(data) {
    return request({
        url: '/FrameworkManagement/GetFrameworkFile',
        method: 'post',
        data: data
    })
}

export function GetFrameworkFileHistorical(data) {
    return request({
        url: '/FrameworkManagement/GetFrameworkFileHistorical',
        method: 'post',
        data: data
    })
}

export function FrameworkManagementFile(data) {
    return request({
        url: '/FrameworkManagement/FrameworkManagementFile',
        method: 'post',
        data: data
    })
}

export function FrameworkManagementDel(fileid) {
    return request({
        url: '/FrameworkManagement/FrameworkManagementDel?fileid=' + fileid,
        method: 'get'
    })
}

export function FrameworkManagementDelHis(fileid) {
    return request({
        url: '/FrameworkManagement/FrameworkManagementDelHis?fileid=' + fileid,
        method: 'get'
    })
}