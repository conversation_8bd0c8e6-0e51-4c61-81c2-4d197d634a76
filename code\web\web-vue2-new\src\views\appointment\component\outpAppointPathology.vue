<template>
  <div class="drawer-container">
    <el-descriptions title="患者基本信息>>>请核对" style="margin-top: 5px" size="medium" border>
      <el-descriptions-item label="患者姓名：">{{
        this.formData.name
      }}</el-descriptions-item>
      <el-descriptions-item label="性别">{{
        this.formData.sex
      }}</el-descriptions-item>
      <el-descriptions-item label="年龄">{{
        this.formData.age
      }}</el-descriptions-item>
      <el-descriptions-item label="科别：">{{
        this.formData.deptName
      }}</el-descriptions-item>
      <el-descriptions-item label="检查项目：">{{
        this.formData.examItem
      }}</el-descriptions-item>
      <el-descriptions-item label="临床诊断：">{{
        this.formData.clinicDiagnosis
      }}</el-descriptions-item>
      <el-descriptions-item label="住院号：">{{
        this.formData.inpNo
      }}</el-descriptions-item>
      <el-descriptions-item label="床号：">{{
        this.formData.bedNo
      }}</el-descriptions-item>
      <el-descriptions-item label="电话：">{{
        this.formData.relationPhoneNum
      }}</el-descriptions-item>
      <el-descriptions-item label="住址：">{{
        this.formData.address
      }}</el-descriptions-item>
    </el-descriptions>
    <el-divider><i class="el-icon-thumb"></i></el-divider>

    <el-form :inline="true" :model="formData" :rules="rules" ref="dataForm">
      <el-form-item label="送检医院:" prop="deliverHospital">
        <el-input v-model="formData.deliverHospital" placeholder="请输入送检医院"></el-input>
      </el-form-item>
      <el-form-item label="送检组织名称:" prop="inspectionSampleName">
        <el-input v-model="formData.inspectionSampleName" placeholder="请输入送检组织名称"></el-input>
      </el-form-item>
      <el-form-item label="取材部位:" prop="sampleLocation">
        <el-input v-model="formData.sampleLocation" placeholder="请输入取材部位"></el-input>
      </el-form-item>
      <el-form-item label="件数:" prop="sampleNum">
        <el-input-number v-model="formData.sampleNum" :min="1" label="请输入件数"></el-input-number>
      </el-form-item>
      <el-form-item label="传染性疾病监测:">
        <el-form-item label="HBV:" prop="hbv">
          <el-input v-model="formData.hbv" placeholder="HBV结果"></el-input>
        </el-form-item>
        <el-form-item label="HCV:" prop="hcv">
          <el-input v-model="formData.hcv" placeholder="HCV结果"></el-input>
        </el-form-item>
        <el-form-item label="HIV:" prop="hiv">
          <el-input v-model="formData.hcv" placeholder="HIV结果"></el-input>
        </el-form-item>
        <el-form-item label="TB:" prop="tb">
          <el-input v-model="formData.tb" placeholder="TB结果"></el-input>
        </el-form-item>
        <el-form-item label="TP:" prop="tp">
          <el-input v-model="formData.tp" placeholder="Tp结果"></el-input>
        </el-form-item>
        <el-form-item label="其它:" prop="infectOther">
          <el-input v-model="formData.infectOther" placeholder="其它传染性疾病"></el-input>
        </el-form-item>
      </el-form-item>
      <el-form-item label="病历摘要及临床检查所见:(包含病史,体征,实验室检查,肿瘤发现时间,部位,大小,硬度,生长速度,有无转移,是否有既往史,手术史;印象学资料)" prop="clinSymp">
        <el-input type="textarea" v-model="formData.clinSymp" placeholder="请输入病历摘要及临床检查所见"
          style="width: 580px"></el-input>
      </el-form-item>

      <el-form-item label="妇科信息:">
        <el-form-item label="末次月经:" prop="menstrualPeriodDate">
          <el-date-picker v-model="formData.menstrualPeriodDate" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否绝经:" prop="menstrualStatus">
          <el-radio-group v-model="formData.menstrualStatus">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="妊娠史:" prop="pregnancyHistory">
          <el-radio-group v-model="formData.pregnancyHistory">
            <el-radio label="Y">是</el-radio>
            <el-radio label="N">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form-item>
      <el-form-item label="手术名称及手术所见：" prop="surgicalProcess">
        <el-input type="textarea" v-model="formData.surgicalProcess" placeholder="请输入手术名称及手术所见"
          style="width: 580px"></el-input>
      </el-form-item>
      <el-form-item label="既往病理号及诊断：" prop="pathlogyDiagnosis">
        <el-input type="textarea" v-model="formData.pathlogyDiagnosis" placeholder="请输入既往病理号及诊断"
          style="width: 580px"></el-input>
      </el-form-item>
      <el-form-item label="标本离体时间:" prop="specimenLeaveTime">
        <el-date-picker v-model="formData.specimenLeaveTime" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择时间" style="width: 150px">
        </el-date-picker>
      </el-form-item>
    </el-form>

    <div class="drawer-footer">
      <el-button type="primary" class="custom-width" @click="submitForm">确定</el-button>
      <el-button class="custom-width" @click="closeDrawer">取 消</el-button>
    </div>
  </div>
</template>
  
<script>
import { saveOutpExamPathologyRegister } from "@/api/appointment/pathology";

export default {
  name: "appoint-pathology",
  props: {
    patientInfo: {
      type: Object,
      required: true,
    },
    users: {
      type: Object,
      required: true,
    },
    examItemName: {
      type: String,
      required: true,
    },
    checkExamInfo: {
      type: Object,
      required: true,
    },
    index: {
      type: Number,
      default: Math.random(),
    },
  },
  data() {
    return {
      formLabelWidth: "80px",
      formData: {
        menstrualStatus: "",
        pregnancyHistory: "",
        examItem: undefined,
      },
      examItemInfo: {

      },
      rules: {
        deliverHospital: [
          { required: true, message: "请输入送检医院", trigger: "blur" },
        ],
        clinSymp: [
          {
            required: true,
            message: "请输入病历摘要及临床检查所见",
            trigger: "blur",
          },
        ],
        inspectionSampleName: [
          { required: true, message: "请输入送检组织名称", trigger: "blur" },
        ],
        sampleLocation: [
          { required: true, message: "请输入取材部位", trigger: "blur" },
        ],
        sampleNum: [{ required: true, message: "请输入件数", trigger: "blur" }],
        surgicalProcess: [
          {
            required: true,
            message: "请输入手术名称及手术所见",
            trigger: "blur",
          },
        ],
        specimenLeaveTime: [
          { required: true, message: "请输入标本离体时间", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getBasicInfo();
  },
  watch: {
    examItemName: {
      handler(newData) {
        this.formData.examItem = newData;
      },
      immediate: true,
      deep: true,
    },
    checkExamInfo: {
      handler(newData) {
        this.examItemInfo = newData;
      },
      immediate: true,
      deep: true,
    }
  },
  methods: {
    /**
     * 病理检查申请单患者基本信息
     */
    getBasicInfo() {
      this.formData.patientId = this.users.patientId;
      this.formData.visitId = this.users.visitId;
      this.formData.name = this.users.userName;
      this.formData.sex = this.users.sex;
      this.formData.age = this.users.age;
      this.formData.relationPhoneNum = this.users.phone;
      this.formData.address = this.users.mailingAddress;
      this.formData.clinSymp = this.users.bodyExam;
      this.formData.clinicDiagnosis = this.users.diagDesc;
      this.formData.examItem = this.examItemName;
      this.formData.deptName = this.users.deptName;
      this.formData.inpNo = this.users.inpNo;
      this.formData.bedNo = this.users.bedNo;
      this.formData.sampleNum = 1;
      this.formData.clincNo = this.patientInfo.clincNo;
    },

    /**
     * 关闭抽屉
     */
    closeDrawer() {
      this.$emit("close-drawer");
    },
    /**
     * 保存病理检查申请信息
     */
    submitForm: function () {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          const data = {
            users: this.users,
            examPathology: this.formData,
            examItem: this.examItemName,
            checkExamInfo: this.examItemInfo
          };
          saveOutpExamPathologyRegister(data).then((response) => {
            this.$modal.msgSuccess("门诊病理申请成功");
            this.$emit("get-list");
            this.$emit("close-drawer");
          });
        }
      });
    },

  },
};
</script>
<style lang="scss" scoped>
.drawer-container {
  margin-left: 15px;
  margin-right: 15px;

  .drawer-footer {
    position: absolute;
    bottom: 10px;
    right: 10px;
    padding: 10px;

    .custom-width {
      width: 130px;
      margin-right: 15px;
    }
  }
}
</style>
  