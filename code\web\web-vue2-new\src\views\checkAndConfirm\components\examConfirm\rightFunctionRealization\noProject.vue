<template>
    <div class="cost-inquiry-home">
        <el-dialog title="患者未执行的检查检验项目查询" :visible.sync="dialogVisible" width="70%">
            <div class="dialog-master">
                <div class="item-table-master">
                    <el-table :data="tableDate" style="width: 100%" border max-height="350px">
                        <el-table-column prop="id" label="行号" :index="indexMethod" type="index" align="center" />
                        <el-table-column prop="exam_sub_class" label="检查子类" align="center" />
                        <el-table-column prop="patient_id" label="病人ID" width="90px" align="center" />
                        <el-table-column prop="name" label="姓名" align="center" />
                        <el-table-column prop="sex" label="性别" width="50px" align="center" />
                        <el-table-column prop="exam_no" label="申请序号" width="100px" align="center" />
                        <el-table-column prop="device" label="检查项目" align="center" />
                        <el-table-column prop="eq_date_time" label="申请时间" width="150px" :formatter="formatterTime"
                            align="center" />
                        <el-table-column prop="result_status" label="已做" align="center" />
                        <el-table-column prop="req_physician" label="申请医生" align="center" />
                        <el-table-column prop="req_dept" label="申请科室" align="center" />
                        <el-table-column prop="performed_by" label="执行医生" align="center" />
                        <el-table-column prop="fee_status" label="缴费状态" align="center" />
                    </el-table>
                </div>
                <div class="bottom-button-master">
                    <div class="button-item">
                        <el-button type="info" @click="cancel">关闭</el-button>
                    </div>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { GetNoProjectByPatientId } from "@/api/checkAndConfirm/noProject"
export default {
    name: 'noProject',
    props: [],
    components: {},
    data() {
        return {
            tableDate: [],
            toBackend: {
                pageNum: 1,
                pageSize: 15,
            },
            dialogVisible: false,
        }
    },

    created() {
    },

    mounted() {
    },

    methods: {
        // 按钮点击校验(判断点击按钮调用vistry方法)
        noProjectMonitor(status) {
            console.log(status);
            if (status) {
                // 如果status为true则调用vistry方法
                this.vistry();
            }
        },

        // 校验传递的参数做出判断(11255569)
        vistry() {
            let data = this.$store.getters.patient;
            if (!data.patientId) {
                this.$msgbox.alert(
                    '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
                    '当前患者id为空,请手动输入患者id或读卡或扫码后再点击此功能!!!' + '</div>',
                    '系统提示',
                    {
                        confirmButtonText: '确定',
                        type: 'warning',
                        dangerouslyUseHTMLString: true
                    }).then(() => {
                        this.$emit('path-skip', 'reservationCenter')
                    })
                return;
            }
            // 如果传递的患者ID不为空则调用getNoProject方法
            this.getNoProject();
        },

        // 根据患者ID查询患者未执行项目数据
        getNoProject() {
            let data = this.$store.getters.patient;
            this.toBackend = data;
            this.dialogVisible = true;
            GetNoProjectByPatientId(this.toBackend).then((response) => {
                this.tableDate = response.data.getNoProjectRetVueModels;
            });
        },

        // 关闭按钮操作
        cancel() {
            this.dialogVisible = false;
        },

        // 序号翻页递增
        indexMethod(index) {
            let nowPage = this.toBackend.pageNum; //当前第几页，根据组件取值即可
            let nowLimit = this.toBackend.pageSize; //当前每页显示几条，根据组件取值即可
            return index + 1 + (nowPage - 1) * nowLimit; // 这里可以理解成一个公式
        },

        // 时间不显示时分秒
        formatterTime(row, column) {
            let data = row[column.property]
            return /\d{4}-\d{1,2}-\d{1,2}/g.exec(data)
        },
    },
}
</script>

<style scoped lang="scss">
.cost-inquiry-home {

    .dialog-master {

        ::v-deep.el-table--medium .el-table__cell {
            padding: 1px 0;
        }

        ::v-deep.el-table .el-table__header-wrapper th,
        .el-table .el-table__fixed-header-wrapper th {
            word-break: break-word;
            background-color: #f8f8f9;
            color: #515a6e;
            height: 30px;
            font-size: 13px;
        }

        ::v-deep.el-table__body tr.current-row>td.el-table__cell,
        .el-table__body tr.selection-row>td.el-table__cell {
            background-color: #1890FF;
            color: #FFFFFF;
        }

        .bottom-button-master {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
    }
}
</style>
