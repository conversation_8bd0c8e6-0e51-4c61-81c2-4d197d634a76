<template>
  <div :class="{topHomeOne: topOne,topHomeTwo: topTwo,topHomeThree : topThree}">
    <div class="top-left" :style="topStyle.width2">
      <top-button></top-button>
      <div class="left-top"></div>
      <route-item @tabs-click="receiveRoute" ref="routeItemRef"></route-item>
    </div>
    <div class="top-right" :style="topStyle.width1">
      <user-message></user-message>
    </div>


  </div>
</template>

<script>
import RouteItem from './navigation/routeItem.vue'
import TopButton from './navigation/topButton.vue'
import UserMessage from './navigation/userMessage.vue'

export default {
  name: 'confirmTop',
  props: [],
  components: { UserMessage, TopButton, RouteItem },
  data() {
    return {
      topStyle: {
        width1: '7%',
        width2: '90%'
      },
      topOne: true,
      topTwo: false,
      topThree: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      const docEl = document.documentElement,
        docBody = document.body,
        winWidth = docEl.clientWidth || docBody.clientWidth // 获取浏览器的宽度
      if (winWidth <= 1200) {
        this.topOne = false
        this.topTwo = false
        this.topThree = true
        this.topStyle = {
          width1: '13%',
          width2: '87%'
        }
      } else if (winWidth <= 1400) {
        this.topOne = false
        this.topTwo = true
        this.topThree = false
        this.topStyle = {
          width1: '10%',
          width2: '90%'
        }
      } else {
        this.topOne = true
        this.topTwo = false
        this.topThree = false
        this.topStyle = {
          width1: '7%',
          width2: '93%'
        }
      }
    })
  },
  methods: {
    routeItemAssignment(data){
      this.$refs.routeItemRef.tabsAssignment(data);
    },
    receiveRoute(data) {
      this.$emit('route-value', data)
    }
  }
}
</script>

<style scoped lang="scss">
.topHomeOne {
  display: flex;
  background-color: #E9EDF2;

  .top-left {
    width: 93%;
    display: flex;
    .left-top {
      height: 50px;
      width: 21%;
      background-size: 100% auto;
      background: url("../../../assets/logo/honlivhpOne.png") 35% no-repeat;
    }
  }

  .top-right {
    width: 7%;
  }
}

.topHomeTwo {
  display: flex;
  background-color: #E9EDF2;

  .top-left {
    width: 90%;
    display: flex;

    .left-top {
      height: 50px;
      width: 30%;
      background: url("../../../assets/logo/honlivhpOne.png") 51% center no-repeat;
      background-size: 100% auto;
    }
  }

  .top-right {
    width: 10%;
  }
}

.topHomeThree {
  display: flex;
  background-color: #E9EDF2;

  .top-left {
    width: 87%;
    display: flex;
    .left-top {
      height: 50px;
      width: 33%;
      background: url("../../../assets/logo/honlivhpOne.png") 51% center no-repeat;
      background-size: 100% auto;
    }
  }

  .top-right {
    width: 13%;
  }
}
@media screen and (max-height: 650px) {
  .left-top {
    height: 30px !important;
  }
}
</style>
