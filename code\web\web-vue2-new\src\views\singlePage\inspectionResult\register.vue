<template>
  <div class="register-home">
    <div class="register-master">
      <div class="register-title">
        河南宏力医院检查检验结果线下互认登记备查本
      </div>
      <div class="register-date">
        登记日期：
        <div class="date-item">{{ thisYear }}</div>
        年
        <div class="date-item">{{ thisMonth }}</div>
        月
        <div class="date-item">{{ thisDay }}</div>
        日
      </div>
      <div class="register-table">
        <el-table :data="tableData" @row-click="projectTableClick" highlight-current-row :height="440"
          style="width: 100%">
          <el-table-column align="center" type="index" label="序号" width="25"></el-table-column>
          <el-table-column align="center" :width="myStyle.table.T" label="操作">
            <template slot-scope="scope">
              <div v-if="scope.row.status">
                <el-button type="text" @click="keepAndRegister(scope.row)" icon="el-icon-download">登记</el-button>
              </div>
              <div v-else>
                <el-button type="text" @click="updateDispose(scope.row)">修改</el-button>
                <el-button type="text" @click="deleteDispose(scope.row)" icon="el-icon-delete">删除</el-button>
              </div>

            </template>
          </el-table-column>
          <el-table-column align="center" prop="name" label="患者姓名" :width="myStyle.table.one"></el-table-column>
          <el-table-column align="center" prop="idNo" label="身份证号" :show-overflow-tooltip="true"
            :width="myStyle.table.two"></el-table-column>
          <el-table-column align="center" prop="examName" :show-overflow-tooltip="true" label="患者携带的近期既有检查检验结果项目名称"
            :width="myStyle.table.event">
            <template slot-scope="scope">
              <el-input v-if="scope.row.examNameInputStatus" v-model="scope.row.examName" placeholder="请输入检验结果项目名称"
                type="textarea" :autosize="{ minRows: 2, maxRows: 6 }" ref="examNameRefs" @blur="InputBlur(scope.row)">
              </el-input>
              <span v-else>{{ scope.row.examName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="出具该检查检验结果的医院名称及级别">
            <el-table-column align="center" prop="hospitalName" label="名称" :show-overflow-tooltip="true"
              :width="myStyle.table.ten">
              <template slot-scope="scope">
                <el-input v-if="scope.row.hospitalNameStatus" v-model="scope.row.hospitalName"
                  placeholder="出具该检查检验结果的医院名称" type="textarea" :autosize="{ minRows: 2, maxRows: 6 }"
                  ref="hospitalNameRefs" @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.hospitalName }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="hospitalLevelOne" label="三级" :width="myStyle.table.three">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.hospitalLevelOneStatus" v-model="scope.row.hospitalLevelOne"
                  ref="hospitalLevelOneRefs" @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.hospitalLevelOne }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="hospitalLevelTwo" label="二级" :width="myStyle.table.three">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.hospitalLevelTwoStatus" v-model="scope.row.hospitalLevelTwo"
                  ref="hospitalLevelTwoRefs" @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.hospitalLevelTwo }}</span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" prop="examDate" label="检查检验时间（年/月/日）" :width="myStyle.table.four">
            <template slot-scope="scope">
              <el-date-picker v-if="scope.row.examDateStatus" v-model="scope.row.examDate" type="date" clearable
                value-format="yyyy-MM-dd" placeholder="选择日期" ref="examDateRefs" @blur="InputBlur(scope.row)">
              </el-date-picker>
              <span v-else>{{ scope.row.examDate }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="是否互认">
            <el-table-column align="center" prop="confirmStatusOne" label="是" :width="myStyle.table.three">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.confirmStatusOneStatus" v-model="scope.row.confirmStatusOne"
                  ref="confirmStatusOneRefs" @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.confirmStatusOne }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="confirmStatusTwo" label="否" :width="myStyle.table.three">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.confirmStatusTwoStatus" v-model="scope.row.confirmStatusTwo"
                  ref="confirmStatusTwoRefs" @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.confirmStatusTwo }}</span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" label="不予互认的原因（勾选）">
            <el-table-column align="center" prop="one" label="①" :width="myStyle.table.five">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.oneStatus" v-model="scope.row.one" ref="oneRefs"
                  @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.one }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="two" label="②" :width="myStyle.table.five">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.twoStatus" v-model="scope.row.two" ref="twoRefs"
                  @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.two }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="three" label="③" :width="myStyle.table.five">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.threeStatus" v-model="scope.row.three" ref="threeRefs"
                  key="input" @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.three }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="four" label="④" :width="myStyle.table.five">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.fourStatus" v-model="scope.row.four" ref="fourRefs"
                  @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.four }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="five" label="⑤" :width="myStyle.table.five">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.fiveStatus" v-model="scope.row.five" ref="fiveRefs"
                  @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.five }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="six" label="⑥" :width="myStyle.table.five">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.sixStatus" v-model="scope.row.six" ref="sixRefs"
                  @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.six }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="seven" label="⑦" :width="myStyle.table.five">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.sevenStatus" v-model="scope.row.seven" ref="sevenRefs"
                  @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.seven }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="eight" label="⑧" :width="myStyle.table.five">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.eightStatus" v-model="scope.row.eight" ref="eightRefs"
                  @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.eight }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="nine" label="⑨" :width="myStyle.table.five">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.nineStatus" v-model="scope.row.nine" ref="nineRefs"
                  @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.nine }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="ten" label="⑩" :width="myStyle.table.five">
              <template slot-scope="scope">
                <el-input class="inputOne" v-if="scope.row.tenStatus" v-model="scope.row.ten" ref="tenRefs"
                  @blur="InputBlur(scope.row)">
                </el-input>
                <span v-else>{{ scope.row.ten }}</span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column align="center" prop="doctorName" label="接诊医师签名" :width="myStyle.table.six"></el-table-column>
          <el-table-column align="center" prop="signature" label="电子签名" :width="myStyle.table.sixTwo">
            <template slot-scope="scope">
              <img :src="scope.row.signature" href="" style="width: 90px;height: auto" />
            </template>
          </el-table-column>
        </el-table>
        <div style="display: flex;justify-content: center;">
          <pagination v-show="total > 0" :limit.sync="formQuery.pageSize" :page.sync="formQuery.pageNum" :total="total"
            :pager-count="5" layout="prev, pager,next" @pagination="pageMessage" />
        </div>
      </div>

      <el-scrollbar :style="myStyle.scrollbarHeight">
        <div class="register-remind">
          <div style="color: red;font-size: 30px;">注：</div>
          <div>
            <div>1.
              医疗机构各门急诊室、各住院部病区均要置备本登记备查本，作为过渡性措施供接诊医师逐项登记检查检验项目互认情况时使用。
            </div>
            <div>2. 门急诊登记工作由门诊办负责组织落实、统计与核查，各住院部病区由各临床科室负责组织落实、统计与核查。</div>
            <div>3. 各有关医疗机构医务部门负责每周统计线下互认数量、不予互认数量等有关情况，并按要求逐级上报省卫生健康委。
            </div>
            <div class="register-text-two">
              <div>4. 可不予互认的10情形：</div>
              <div class="register-text-three">
                <div class="register-text-four">
                  <div class="register-text-item" :style="myStyle.textWidth">
                    ①.因病情变化，已有的检查检验结果难以反映病人当前实际病情或难以满足临床诊疗需求的；
                  </div>
                  <div class="register-text-item" :style="myStyle.textWidth">
                    ②.因时效问题，已有的检查检验结果难以提供参考价值的；
                  </div>
                </div>
                <div class="register-text-four">
                  <div class="register-text-item" :style="myStyle.textWidth">
                    ③.检查检验结果与疾病发展关联程度高、变化幅度大的项目；
                  </div>
                  <div class="register-text-item" :style="myStyle.textWidth">④.检查检验结果与病情明显不符的；</div>
                </div>
                <div class="register-text-four">
                  <div class="register-text-item" :style="myStyle.textWidth">⑤.患者或其亲属要求做进一步检查检验的；</div>
                  <div class="register-text-item" :style="myStyle.textWidth">
                    ⑥.重新复查检查检验项目对治疗措施选择意义重大的（如手术等重大医疗措施前）；
                  </div>
                </div>
                <div class="register-text-four">
                  <div class="register-text-item" :style="myStyle.textWidth">⑦.因疾病转归需连续对比观察的；</div>
                  <div class="register-text-item" :style="myStyle.textWidth">⑧.急诊、急救等抢救生命的紧急状态下的；</div>
                </div>
                <div class="register-text-four">
                  <div class="register-text-item" :style="myStyle.textWidth">⑨.司法、伤残及病退等鉴定所需的；</div>
                  <div class="register-text-item" :style="myStyle.textWidth">⑩.其他符合诊疗需要的不可预测情形。</div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </el-scrollbar>


    </div>

  </div>
</template>

<script>
import { InitializePatientAndDoctorMessage, KeepAndRegister, DeleteById } from '@/api/singlePage/inspectionResult'

export default {
  name: 'register',
  props: [],
  components: {},
  data() {
    return {
      tableData: [],
      thisYear: '',
      thisMonth: '',
      thisDay: '',
      total: 0,
      formQuery: {
        pageSize: 5,
        pageNum: 1,
        empNo: '',
        patientId: '',
        patientType: '',
        visitId: '',
        visitNo: '',
        visitDate: ''
      },
      myStyle: {
        table: {
          one: '80',
          two: '170',
          three: '50',
          four: '110',
          five: '40',
          six: '80',
          sixTwo: '120',
          event: '150',
          ten: '95',
          T: '120'
        },
        textWidth: 'width: 400px;',
        scrollbarHeight: 'height: 130px'
      },
      check: '✔',
      noCheck: '',
      dictItem: {
        one: '1',
        two: '2',
        three: '3',
        four: '4',
        five: '5',
        six: '6',
        seven: '7',
        eight: '8',
        nine: '9',
        ten: '10'
      }
    }
  },
  created() {
    let patientId = this.$route.query && this.$route.query.patientId
    let empNo = this.$route.query && this.$route.query.empNo
    let patientType = this.$route.query && this.$route.query.patientType
    let visitId = this.$route.query && this.$route.query.visitId
    let visitNo = this.$route.query && this.$route.query.visitNo
    let visitDate = this.$route.query && this.$route.query.visitDate
    this.dateInitialize()
    this.InitializeMessage(patientId, empNo, patientType, visitId, visitNo, visitDate)

  },
  mounted() {
    this.$nextTick(() => {
      const bodyStyle = document.body.style, // 获取body节点样式
        htmlStyle = document.getElementsByTagName('html')[0].style, // 获取html节点样式
        docEl = document.documentElement,
        docBody = document.body,
        winWidth = docEl.clientWidth || docBody.clientWidth, // 获取浏览器的宽度
        WinHeight = docEl.clientHeight || docBody.clientHeight // 获取浏览器的高
      this.bodyHeight = 'height:' + WinHeight + 'px'
      bodyStyle.minWidth = '1014px'
      bodyStyle.minHeight = '768px'
      htmlStyle.minHeight = '768px'
      htmlStyle.minWidth = '1014px'
      if (winWidth <= 1200) {
        this.myStyle = {
          table: {
            one: '65',
            two: '80',
            three: '40',
            four: '80',
            five: '30',
            six: '55',
            sixTwo: '100',
            event: '150',
            ten: '95',
            T: '120'
          },
          textWidth: 'width: 400px;',
          scrollbarHeight: 'height: 130px'
        }
      } else if (winWidth <= 1400) {
        this.myStyle = {
          table: {
            one: '65',
            two: '80',
            three: '40',
            four: '80',
            five: '30',
            six: '55',
            sixTwo: '100',
            event: '170',
            ten: '95',
            T: '120'
          },
          textWidth: 'width: 520px;'
        }
      } else {
        this.myStyle = {
          table: {
            one: '100',
            two: '170',
            three: '50',
            four: '150',
            five: '40',
            six: '100',
            sixTwo: '120',
            event: '320',
            ten: '140',
            T: '170'
          },
          textWidth: 'width: 600px;'
        }
      }
    })
  },
  methods: {
    deleteDispose(row) {
      this.$confirm('你确定要删除' + row.name + '的互认登记结果吗？本次删除数据将会永久删除!!!', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        DeleteById(row.id).then(res => {
          this.$message.success(res.message)
          this.pageMessage()
        })
      }).catch(() => {
      })
    },
    updateDispose(row) {
      row.status = true
    },
    keepAndRegister(row) {
      let data = {
        id: row.id,
        patientId: row.patientId,
        name: row.name,
        idNo: row.idNo,
        examName: row.examName,
        hospitalName: row.hospitalName,
        hospitalLevel: this.hospitalLevelDispose(row),
        examDate: row.examDate,
        confirmStatus: this.confirmStatusDispose(row),
        unConfirmReason: this.unConfirmReasonDispose(row),
        doctorNo: row.doctorNo,
        doctorName: row.doctorName,
        deptCode: row.deptCode,
        deptName: row.deptName,
        patientType: row.patientType,
        visitId: row.visitId,
        visitNo: row.visitNo,
        visitDate: row.visitDate
      }
      KeepAndRegister(data).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message)
          row.status = false
          this.pageMessage()
        }
      })
    },
    projectTableClick(row, column, event) {
      if (row.status) {
        if (column.property === 'examName') {
          row.examNameInputStatus = true
          setTimeout(() => {
            this.$refs.examNameRefs.focus()
          }, 200)
        } else if (column.property === 'hospitalName') {
          row.hospitalNameStatus = true
          setTimeout(() => {
            this.$refs.hospitalNameRefs.focus()
          }, 200)
        } else if (column.property === 'hospitalLevelOne') {
          row.hospitalLevelOneStatus = true
          row.hospitalLevelOne = this.check
          row.hospitalLevelTwo = this.noCheck
          setTimeout(() => {
            this.$refs.hospitalLevelOneRefs.focus()
          }, 200)
        } else if (column.property === 'hospitalLevelTwo') {
          row.hospitalLevelTwoStatus = true
          row.hospitalLevelTwo = this.check
          row.hospitalLevelOne = this.noCheck
          setTimeout(() => {
            this.$refs.hospitalLevelTwoRefs.focus()
          }, 200)
        } else if (column.property === 'examDate') {
          row.examDateStatus = true
          setTimeout(() => {
            this.$refs.examDateRefs.focus()
          }, 200)
        } else if (column.property === 'confirmStatusOne') {
          row.confirmStatusOneStatus = true
          row.confirmStatusOne = this.check
          row.confirmStatusTwo = this.noCheck
          row.one = ''
          row.two = ''
          row.three = ''
          row.four = ''
          row.five = ''
          row.six = ''
          row.ten = ''
          row.eight = ''
          row.seven = ''
          row.nine = ''
          setTimeout(() => {
            this.$refs.confirmStatusOneRefs.focus()
          }, 200)
        } else if (column.property === 'confirmStatusTwo') {
          row.confirmStatusTwoStatus = true
          row.confirmStatusTwo = this.check
          row.confirmStatusOne = this.noCheck
          setTimeout(() => {
            this.$refs.confirmStatusTwoRefs.focus()
          }, 200)
        } else if (row.confirmStatusTwo && column.property === 'one') {
          row.oneStatus = true
          row.one = this.check
          setTimeout(() => {
            this.$refs.oneRefs.focus()
          }, 200)
        } else if (row.confirmStatusTwo && column.property === 'two') {
          row.twoStatus = true
          row.two = this.check
          setTimeout(() => {
            this.$refs.twoRefs.focus()
          }, 200)
        } else if (row.confirmStatusTwo && column.property === 'three') {
          row.threeStatus = true
          row.three = this.check
          setTimeout(() => {
            this.$refs.threeRefs.focus()
          }, 200)
        } else if (row.confirmStatusTwo && column.property === 'four') {
          row.fourStatus = true
          row.four = this.check
          setTimeout(() => {
            this.$refs.fourRefs.focus()
          }, 200)
        } else if (row.confirmStatusTwo && column.property === 'five') {
          row.fiveStatus = true
          row.five = this.check
          setTimeout(() => {
            this.$refs.fiveRefs.focus()
          }, 200)
        } else if (row.confirmStatusTwo && column.property === 'six') {
          row.sixStatus = true
          row.six = this.check
          setTimeout(() => {
            this.$refs.sixRefs.focus()
          }, 200)
        } else if (row.confirmStatusTwo && column.property === 'seven') {
          row.sevenStatus = true
          row.seven = this.check
          setTimeout(() => {
            this.$refs.sevenRefs.focus()
          }, 200)
        } else if (row.confirmStatusTwo && column.property === 'nine') {
          row.nineStatus = true
          row.nine = this.check
          setTimeout(() => {
            this.$refs.nineRefs.focus()
          }, 200)
        } else if (row.confirmStatusTwo && column.property === 'eight') {
          row.eightStatus = true
          row.eight = this.check
          setTimeout(() => {
            this.$refs.eightRefs.focus()
          }, 200)
        } else if (row.confirmStatusTwo && column.property === 'ten') {
          row.tenStatus = true
          row.ten = this.check
          setTimeout(() => {
            this.$refs.tenRefs.focus()
          }, 200)
        }
      }
    },
    InputBlur(row) {
      this.$nextTick(() => {
        row.examNameInputStatus = false
        row.hospitalNameStatus = false
        row.hospitalLevelOneStatus = false
        row.hospitalLevelTwoStatus = false
        row.examDateStatus = false
        row.confirmStatusOneStatus = false
        row.confirmStatusTwoStatus = false
        row.oneStatus = false
        row.twoStatus = false
        row.threeStatus = false
        row.fourStatus = false
        row.fiveStatus = false
        row.sixStatus = false
        row.sevenStatus = false
        row.nineStatus = false
        row.eightStatus = false
        row.tenStatus = false
      })

    },
    InitializeMessage(patient, empNo, patientType, visitId, visitNo, visitDate) {
      this.formQuery.patientId = patient
      this.formQuery.empNo = empNo
      this.formQuery.patientType = patientType
      this.formQuery.visitId = visitId
      this.formQuery.visitNo = visitNo
      this.formQuery.visitDate = visitDate
      this.pageList()
    },
    pageMessage() {
      this.formQuery.patientId = ''
      this.pageList()
    },
    pageList() {
      InitializePatientAndDoctorMessage(this.formQuery).then(res => {
        if (res.code === 200) {
          this.tableData = res.data.list
          this.total = res.data.page
        }
      })
    },
    dateInitialize() {
      let today = new Date()
      this.thisYear = today.getFullYear()
      let month = today.getMonth() + 1 // 月份是从0开始的
      let day = today.getDate()
      // 格式化月份和日期，小于10的数字前面添加0
      this.thisMonth = month < 10 ? '0' + month : month
      this.thisDay = day < 10 ? '0' + day : day
    },
    hospitalLevelDispose(row) {
      let text = ''
      if (row.hospitalLevelOne) {
        text = '三级'
      } else if (row.hospitalLevelTwo) {
        text = '二级'
      }
      return text
    },
    confirmStatusDispose(row) {
      let text = ''
      if (row.confirmStatusOne) {
        text = '是'
      } else if (row.confirmStatusTwo) {
        text = '否'
      }
      return text
    },
    unConfirmReasonDispose(row) {
      let dict = this.dictItem
      let array = []
      if (row.one) {
        array.push(dict.one)
      }
      if (row.two) {
        array.push(dict.two)
      }
      if (row.three) {
        array.push(dict.three)
      }
      if (row.four) {
        array.push(dict.four)
      }
      if (row.five) {
        array.push(dict.five)
      }
      if (row.six) {
        array.push(dict.six)
      }
      if (row.seven) {
        array.push(dict.seven)
      }
      if (row.eight) {
        array.push(dict.eight)
      }
      if (row.nine) {
        array.push(dict.nine)
      }
      if (row.ten) {
        array.push(dict.ten)
      }
      return array.join(',')
    }
  }
}
</script>

<style scoped lang="scss">
.register-home {
  padding: 10px;
  margin-top: 10px;


  .register-master {
    //border: 1px solid blueviolet;

    .register-title {
      font-size: 28px;
      color: black;
      text-align: center;
    }

    .register-date {
      margin-top: 12px;
      font-size: 24px;
      border: black;
      margin-left: 10%;
      display: flex;

      .date-item {
        font-size: 22px;
        display: flex;
        align-items: center;
        padding-left: 10px;
        padding-right: 10px;
        border-bottom: 2px solid black;
      }
    }

    .register-table {
      margin-top: 20px;
      height: 500px;
      border: 1px solid black;
      border-radius: 10px;

      ::v-deep.el-table--medium .el-table__cell {
        padding: 1px 0;
      }

      ::v-deep.el-table th.el-table__cell>.cell {
        padding-left: 5px;
        padding-right: 5px;
      }

      ::v-deep.el-table .cell {
        padding-left: 1px;
        padding-right: 1px;
      }

      ::v-deep.el-table {
        border-radius: 10px 10px 0 0;
      }
    }

    .register-remind {
      margin-top: 10px;
      font-size: 14px;
      display: flex;
      color: #1c84c6;

      .register-text-two {
        display: flex;
      }

      .register-text-three {
        display: flex;
        flex-direction: column;
      }

      .register-text-four {
        display: flex;
      }

      .register-text-item {
        width: 600px;
      }
    }
  }

  .inputOne {
    ::v-deep.el-input__inner {
      padding: 0 5px;
    }
  }


}
</style>
