<template>
  <div class="single-master">
    <div class="single-title">新闻审核（外网）</div>
    <div class="single-element">
      <div class="element-master">
        <div class="element-form">
          <el-form :inline="true" :model="queueForm" class="demo-form-inline">
            <el-form-item label="开始时间:">
              <el-date-picker
                v-model="queueForm.beginDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                @change="getJournalismPage"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间:">
              <el-date-picker
                v-model="queueForm.endDate"
                type="date"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                @change="getJournalismPage"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div class="element-table">
          <el-table :data="tableData" style="width: 100%" border :height="(tableHeight-250)">
            <el-table-column type="index" width="40" align="center"></el-table-column>
            <el-table-column prop="header" align="center" label="新闻标题"></el-table-column>
            <el-table-column prop="SUB_STORAGE" align="center" label="新闻信息" width="90">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-s-order" @click="newsClick(scope.row)"></el-button>
              </template>
            </el-table-column>
            <el-table-column prop="SUB_STORAGE" align="center" label="文件检测" width="90">
              <template slot-scope="scope">
                <div v-if="scope.row.detection">√</div>
                <el-button v-else  type="text" icon="el-icon-unlock" @click="newsDetectionClick(scope.row)"></el-button>

              </template>
            </el-table-column>
            <el-table-column prop="provider" align="center" label="发布者" width="140"></el-table-column>
            <el-table-column prop="fbDate" align="center" label="发布时间" width="110">
              <template slot-scope="scope">
                <span>{{ scope.row.fbDate.split(" ")[0] }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="状态" width="90">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status">已同步</el-tag>
                <el-tag v-else type="danger">未同步</el-tag>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="150">
              <template slot-scope="scope">
                <el-button v-if="!scope.row.status"  type="text" icon="el-icon-s-promotion" @click="intranetSynchronization(scope.row)">同步内网</el-button>
                <el-button v-else  type="text" icon="el-icon-s-promotion" disabled>已同步</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="element-page">
          <pagination v-show="total > 0" :total="total" :page.sync="queueForm.pageNum" :limit.sync="queueForm.pageSize"
                      @pagination="getJournalismPage" />
        </div>
        <div class="element-dialog">
          <el-dialog
            :visible.sync="OpenStatus"
            :title="title"
            width="85%"
          >
            <el-scrollbar :style="'height:' + (tableHeight - 200) + 'px'">
              <div v-html="html"></div>
              <!--              <div style="text-align:center;"><img src="http://10.1.100.4/lkoa/gsjj/newNews/upload/20241223/202412231656355170.jpg" alt="" /></div>"-->

            </el-scrollbar>
          </el-dialog>
          <el-dialog
            :visible.sync="detectionStatus"
            title="文件检测"
            width="40%"
          >
            <div v-loading="detectionLoading" style="height: 300px;"
                 element-loading-text="文件正在检测中"
                 element-loading-spinner="el-icon-loading"
                 element-loading-background="rgba(0, 0, 0, 0.8)">
              <div v-if="detectionResult">
                <el-result icon="success" title="检测成功" subTitle="新闻安全">
                  <template slot="extra">
                    <el-button v-if="!rowData.status" type="primary" icon="el-icon-s-promotion" @click="startSynchronization">同步内网</el-button>
                    <el-button v-else type="primary" icon="el-icon-s-promotion" @click="detectionStatus = false">确定</el-button>
                  </template>
                </el-result>
              </div>
              <div v-if="!detectionResult">
                <el-result icon="error" title="检测失败" subTitle="新闻异常">
                  <template slot="extra">
                    <el-button type="primary" icon="el-icon-s-promotion" @click="detectionStatus = false">确定</el-button>
                  </template>
                </el-result>
              </div>
            </div>
          </el-dialog>
          <el-dialog
            :visible.sync="uploadStatus"
            title="文件同步"
            width="40%"
          >
            <div v-loading="uploadLoading" style="height: 300px;"
                 element-loading-text="文件正在同步中"
                 element-loading-spinner="el-icon-loading"
                 element-loading-background="rgba(0, 0, 0, 0.8)">
              <div v-if="uploadResult">
                <el-result icon="success" title="同步成功" subTitle="新闻同步内网成功">
                  <template slot="extra">
                    <el-button type="primary" icon="el-icon-s-promotion" @click="uploadStatus = false">确定</el-button>
                  </template>
                </el-result>
              </div>
              <div v-if="!uploadResult">
                <el-result icon="error" title="同步失败" subTitle="新闻同步内网失败,请手动排查异常">
                  <template slot="extra">
                    <el-button type="primary" icon="el-icon-s-promotion" @click="uploadStatus = false">确定</el-button>
                  </template>
                </el-result>
              </div>
            </div>
          </el-dialog>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import {getJournalismPageListOuterNet, newsFileDetecting, oaHpoa1005SyuchronizationById,getNewHtmlThenBase64} from "@/api/journalismAudit/auditApi";

export default {
  name: 'outerNet',
  props: [],
  components: {},
  data() {
    return {
      queueForm: {
        pageNum: 1,
        pageSize: 10,
        beginDate: this.formatDateMonth(new Date()),
        endDate: this.formatDate(new Date()),
      },
      tableData: [],
      total: 0,
      tableHeight: undefined,
      OpenStatus: false,
      title: '新闻信息',
      html: '',
      detectionStatus: false,
      detectionLoading: false,
      detectionResult: true,
      newsId:'',
      uploadStatus: false,
      uploadLoading: false,
      uploadResult: true,
      rowData: {},
    }
  },
  created() {
    this.getJournalismPage();
    this.handleResize();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    intranetSynchronization(row){
      this.rowData = row;
      if (!row.detection){
        this.$msgbox.alert(
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
          '请先进行文件检测' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
        return;
      }
      this.newsId = row.id;
      this.startSynchronization();
    },
    startSynchronization(){
      this.detectionStatus = false;
      if (!this.newsId){
        this.$msgbox.alert(
          '<div style="font-size: 28px !important;color: red; text-align: center;font-weight: 800;margin-bottom: 10px;">' +
          '数据错乱' + '</div>' + '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
          '请手动点击行右侧同步按钮进行同步' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }).then(() => {
        })
      }else{
        this.uploadStatus = true;
        this.uploadLoading = true;
        //数据同步
        oaHpoa1005SyuchronizationById(this.newsId).then(res => {
          if (res.code === 200){
            if (res.data === true){
              this.$message.success("数据同步内网成功")
            }else{
              this.uploadResult = false;
            }
          }else{
            this.uploadResult = false;
          }
          this.getJournalismPage();
          this.uploadLoading = false;
        })
      }
    },
    newsDetectionClick(row){
      this.rowData = row;
      this.newsId = row.id;
      this.detectionStatus = true;
      this.detectionLoading = true;
      newsFileDetecting(row.id,'2').then(res => {
        if (res.data.length > 0){
          res.data.some(t => {
            if (t.component1 !== 'OK') {
              this.detectionResult = false;
              return true; // 结束 some 方法，相当于 break
            }
            return false; // 继续循环
          });
        }else{
          this.detectionResult =true;
        }
        this.getJournalismPage();
        this.detectionLoading = false;
      })
    },
    newsClick(row) {
      const loading = this.$loading({
        lock: true,
        text: "休息一下,新闻正在加载中(●" + "◡" + "●)",
        spinner: "el-icon-coffee-cup",
        background: "rgba(0, 0, 0, 0.7)",
      });
      this.html = row.content;
      getNewHtmlThenBase64(row.id,"2").then(res => {
        if (res.code === 200){
          this.html = res.data;
          this.OpenStatus = true;}
      }).finally(() => {
        loading.close();
      })
    },
    getJournalismPage() {
      getJournalismPageListOuterNet(this.queueForm).then(res => {
        this.tableData = res.data.list;
        this.total = res.data.total;
      })
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    formatDateMonth(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      return `${year}-${month}-01`
    }
  },
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
</style>
