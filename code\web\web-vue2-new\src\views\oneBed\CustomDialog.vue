<template>
  <el-dialog ref="dialogRef" title="配置" :visible.sync="isVisible" :before-close="handleClose" :width="dialogWidth">
    <div class="sort_container">
      <div class="left_sort">
        <el-tag>按楼号：</el-tag>
        <draggable :list="BuildingNoList" :options="{ animation: 200, group: 'buildingNo', ghostClass: 'ghost' }"
          @end="onDragEnd" tag="ul" class="task-list">
          <li v-for="(item, index) in BuildingNoList" :key="index" class="task-item">
            {{ item.label }}
          </li>
        </draggable>
      </div>
      <div class="button">
        <el-button @click="hideDialog">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
      <div class="right_sort">
        <el-tag type="warning">按病区楼层：</el-tag>
        <draggable :list="WardFloor" :options="{ animation: 200, group: 'wardFloor', ghostClass: 'ghost' }"
          @end="onDragEndTwo" tag="ul" class="task-list">
          <li v-for="(item, index) in WardFloor" :key="index" class="task-item">
            {{ item.label }}
          </li>
        </draggable>
      </div>
    </div>

  </el-dialog>
</template>

<script>
import { utils } from "@/mixin.js";
import draggable from 'vuedraggable';
import { GetLocationInfo, GetWardListInfo, SaveLocationInfo } from "../../api/oneBed/OneBedInfo";

export default {
  name: 'CustomDialog',
  mixins: [utils],
  components: {
    draggable,
  },
  props: {
    isVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogWidth: "50%",
      BuildingNoList: [],
      WardFloor: [],
      form: {
        BuildingNoList: [],
        WardFloor: [],
      }
    };
  },
  methods: {
    getList() {
      let form = {
        Type: "3"// 两种类型的都查出来
      }
      GetLocationInfo(form).then(res => {
        this.BuildingNoList = res.data.buildingNoList;
        this.WardFloor = res.data.wardFloor;
        if (this.BuildingNoList.length === 0 && this.WardFloor.length === 0) {
          GetWardListInfo().then(res => {
            // 使用深拷贝的方法来创建两个独立的数据副本
            this.BuildingNoList = JSON.parse(JSON.stringify(res.data));
            this.WardFloor = JSON.parse(JSON.stringify(res.data));
          });
        }
      });
    },
    onResize() {
      const screenWidth = this.windowWidth(); // 获取屏幕宽度
      if (screenWidth <= 1024) {
        this.dialogWidth = '70%'; // 当屏幕宽度小于等于1024时，设置宽度为60%
      } else if (screenWidth <= 1280 && screenWidth > 1024) {
        this.dialogWidth = '60%';
      } else {
        this.dialogWidth = '45%';
      }
    },
    hideDialog() {
      this.$emit('dialog-closed', false);
    },
    addBuildingNoItem() {
      const newItem = {
        children: [],
        id: "admin",
        index: this.BuildingNoList.length,
        label: "按楼号",
      };
      this.BuildingNoList.push(newItem);
    },
    addWardFloorItem() {
      const newItem = {
        children: [],
        id: "admin",
        index: this.WardFloor.length,
        label: "按病区楼层",
      };
      this.WardFloor.push(newItem);
    },
    submit() {
      //添加数据
      this.addBuildingNoItem();
      this.addWardFloorItem();
      this.form.BuildingNoList = this.BuildingNoList;
      this.form.WardFloor = this.WardFloor;
      this.$emit('dialog-closed', false, "success");
      SaveLocationInfo(this.form).then(res => {
        this.$message({
          message: '保存成功',
          type: 'success',
        });
        this.getList();
        // 通知其他子组件刷新数据
        this.$bus.$emit('data-refresh')
        this.BuildingNoList = [];
        this.WardFloor = [];
      });
    },
    onDragEnd({ oldIndex, newIndex }) {
    },
    onDragEndTwo({ oldIndex, newIndex }) {
    },
    handleClose(done) {
      this.$emit('dialog-closed', false);
    }
  },

  created() {
    this.onResize(); // 初始化时调用
    window.addEventListener('resize', this.onResize); // 监听窗口大小变化
    this.getList();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.onResize); // 移除事件监听器
  },
};
</script>
<style scoped>
@media screen and (max-width: 1024px) {}

.sort_container {
  display: flex;
  justify-content: space-between;

  .left_sort {}

  .right_sort {}

  .button {
    display: flex;
    align-items: center;
  }
}

.task-list {
  display: grid;
  grid-template-rows: repeat(15, auto);
  /* 设置每列包含的行数 */
  grid-auto-flow: column;
  /* 项目按列流动 */
  gap: 2px;
  /* 项目间距 */
  list-style: none;
  padding: 0;
}

.task-item {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background: #f9f9f9;
  cursor: move;
  margin-bottom: 2px;
  list-style: none;
  text-align: center;
  width: 100%;
  white-space: nowrap;
  vertical-align: top;
}
</style>
