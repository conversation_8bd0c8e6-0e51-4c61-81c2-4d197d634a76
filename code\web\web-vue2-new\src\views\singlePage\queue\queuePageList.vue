<template>
  <div class="single-master">
    <div class="single-title">医技等待队列列表</div>
    <div class="single-element" >
      <div class="element-master" >
        <el-alert
          title="请注意每日08：00 - 次日07：59为当前日期查询!!!"
          type="info"
          effect="dark"
        :closable="false">
        </el-alert>
        <div class="queue-home" :style="'height:' + (tableHeight - 150) + 'px'">
          <div class="select-button">
            <el-button type="primary" class="button-item" round @click="selectClick">详情搜索</el-button>
          </div>
          <div class="queue-master">
            <div class="queue-title" v-for="(master,index) in queueData" :key="index"
                 :class="{clickUnit:unitIndex === index}" @click="unitClick(master,index)"
            >
              {{ master.unitName }}
            </div>
          </div>
          <div class="queue-window-master" >
            <div class="queue-border" v-for="(item,index) in unitData.windowList" :key="index">
              <div class="queue-border-title">
                <div class="queue-border-title-font">
                  <div class="title-item">
                    {{ item.windowName }}
                  </div>
                  <div class="title-item" style="color: #1e47a1">
                    {{ unitData.unitName }}
                  </div>
                </div>
              </div>
              <div class="queue-border-item">
                <div>
                  <div class="window-item await">
                    <el-popover
                      placement="bottom"
                      width="1050"
                      trigger="click"
                    >
                      <el-table class="queue-table" :data="item.awaitList" border height="400">
                        <el-table-column align="center" property="queueNo" width="60" label="队列号"></el-table-column>
                        <el-table-column align="center" property="patientId" label="患者id"></el-table-column>
                        <el-table-column align="center" property="patientName" label="姓名"></el-table-column>
                        <el-table-column align="center" property="sex" label="性别"></el-table-column>
                        <el-table-column align="center" property="priority" label="号别">
                          <template slot-scope="scope">
                            <el-tag v-if="scope.row.priority === 1" type="success">回诊</el-tag>
                            <el-tag v-else-if="scope.row.priority === 2">预约</el-tag>
                            <el-tag v-else-if="scope.row.priority === 3">普通</el-tag>
                            <el-tag v-else-if="scope.row.priority === 4" type="info">过号</el-tag>
                            <el-tag v-else-if="scope.row.priority === 5" type="danger">急诊</el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column align="center" property="start" label="登记时间"></el-table-column>
                        <el-table-column align="center" property="item" :show-overflow-tooltip="true" width="300" label="登记项目"></el-table-column>

                        <el-table-column align="center" v-if="!unitIdList.includes(unitData.unitId)" property="appointDate" width="100" label="预约时间"
                        ></el-table-column>
                        <el-table-column align="center" v-if="!unitIdList.includes(unitData.unitId)" property="awaitTime" width="80" label="等待时间"></el-table-column>
                      </el-table>
                      <el-button slot="reference" type="text">{{ item.awaitNumber }}</el-button>
                    </el-popover>
                  </div>
                  <div class="window-item-title">
                    等待中
                  </div>
                </div>

                <div>
                  <div class="window-item seeADoctor">
                    <el-popover
                      placement="bottom"
                      width="1050"
                      trigger="click"
                    >
                      <el-table :data="item.endList" border height="400">
                        <el-table-column align="center" property="queueNo" width="60"  label="队列号"></el-table-column>
                        <el-table-column align="center" property="patientId" width="90" label="患者id"></el-table-column>
                        <el-table-column align="center" property="patientName"  label="姓名"></el-table-column>
                        <el-table-column align="center" property="sex" width="50" label="性别"></el-table-column>
                        <el-table-column align="center" property="priority" width="70" label="号别">
                          <template slot-scope="scope">
                            <el-tag v-if="scope.row.priority === 1" type="success">回诊</el-tag>
                            <el-tag v-else-if="scope.row.priority === 2">预约</el-tag>
                            <el-tag v-else-if="scope.row.priority === 3">普通</el-tag>
                            <el-tag v-else-if="scope.row.priority === 4" type="info">过号</el-tag>
                            <el-tag v-else-if="scope.row.priority === 5" type="danger">急诊</el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column align="center" property="start" :show-overflow-tooltip="true" label="登记时间"></el-table-column>
                        <el-table-column align="center" property="item" width="300" label="登记项目"></el-table-column>
                        <el-table-column align="center" v-if="!unitIdList.includes(unitData.unitId)" property="appointDate" width="100" label="预约时间"
                        ></el-table-column>
                        <el-table-column align="center" v-if="!unitIdList.includes(unitData.unitId)" property="callOutTime" width="80" label="叫号时间"></el-table-column>
                        <el-table-column align="center" v-if="!unitIdList.includes(unitData.unitId)" property="awaitTime" width="80" label="等待时间"></el-table-column>
                      </el-table>
                      <el-button slot="reference" type="text">{{ item.endNumber }}</el-button>
                    </el-popover>
                  </div>
                  <div class="window-item-title">
                    已就诊
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
    <el-dialog
      title="详情搜索"
      :visible.sync="selectStatus"
      width="75%"
    >
      <div style="display: flex;justify-content: center;margin-bottom: 20px;">
        <el-input
          @keyup.enter.native="executeCallSelect"
          clearable
          style="width: 30%"
          v-model="patientId"
          placeholder="请输入患者ID号进行查询"></el-input>
        <el-button type="primary" @click="executeCallSelect">搜索</el-button>
      </div>
      <div style="height: 500px">
        <el-scrollbar style="height: 90%">
          <el-table ref="multipleTable3" :data="callSelectTable" size="mini" border style="width: 100%">
            <el-table-column align="center" prop="unitName"  label="单元" width="90"></el-table-column>
            <el-table-column align="center" prop="windowName"  label="窗口" width="90"></el-table-column>
            <el-table-column align="center" prop="queueId" width="90" label="ID号"></el-table-column>
            <el-table-column align="center" prop="queueName"  label="姓名" width="65"></el-table-column>
            <el-table-column align="center" prop="queueNo"  label="队列号" width="60"></el-table-column>
            <el-table-column align="center" prop="content" label="检查项目" width="180"></el-table-column>
            <el-table-column align="center" prop="priority"  label="号源" width="70">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.priority === 1" type="success">回诊</el-tag>
                <el-tag v-else-if="scope.row.priority === 2">预约</el-tag>
                <el-tag v-else-if="scope.row.priority === 3">普通</el-tag>
                <el-tag v-else-if="scope.row.priority === 4" type="info">过号</el-tag>
                <el-tag v-else-if="scope.row.priority === 5" type="danger">急诊</el-tag>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="status" label="状态" width="85">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.deleteFlag !== 0" type="info">已删除</el-tag>
                <el-tag v-else-if="scope.row.status === 1">等待中</el-tag>
                <el-tag v-else-if="scope.row.status === 2" type="success">就诊结束</el-tag>
                <el-tag v-else-if="scope.row.status === 3" type="success">就诊结束</el-tag>
                <el-tag v-else-if="scope.row.status === 4" type="success">就诊结束</el-tag>
                <el-tag v-else-if="scope.row.status === 5" type="info">过号</el-tag>
                <el-tag v-else-if="scope.row.status === 6" type="info">作废</el-tag>
              </template>
            </el-table-column>
            <el-table-column align="center" prop="callNum" label="呼叫次数" width="75"></el-table-column>
            <el-table-column align="center" prop="time" label="登记时间" width="75"></el-table-column>
            <el-table-column align="center" prop="memo" label="备注" width="90"></el-table-column>
          </el-table>
        </el-scrollbar>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { GetPatientThisDayQueueMessage,getQueueList } from '@/api/checkAndConfirm/queueList'

export default {
  name: 'queuePageList',
  props: [],
  components: {},
  data() {
    return {
      queueForm: {
        deptCode: ['0203','020901']
      },
      unitIdList:['04de22748853490895dd1934611a3ff9','4df0f4e0f4674260b8ffe836b3c6533d','d5a0e7c6570e4374b37bf628ff0b64c8'],
      queueData: [],
      unitIndex: 0,
      selectStatus: false,
      windowData: [],
      unitData: {},
      patientId: '',
      callSelectTable: [],
      registerData: {},
      registerStatus: false,
      tableHeight: undefined,
    }
  },
  created() {
    this.handleResize()
    this.queueMessage();
  },
  mounted() {
    window.addEventListener('resize', this.handleResize) // 添加监听器
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize) // 移除监听器
  },
  methods: {
    selectClick(){
      let row = this.$store.getters.rowTable;
      if (row){
        this.patientId = row.patientId;
      }else {
        this.patientId = "";
      }
      this.callSelectTable = [];
      this.executeCallSelect();
      this.selectStatus = true;
    },
    executeCallSelect() {
      if (this.patientId){
        GetPatientThisDayQueueMessage(this.patientId).then(res => {
          this.callSelectTable = res.data;
        })
      }
    },
    unitClick(data, index) {
      this.unitIndex = index
      this.unitData = data
      let unitData = {
        unitIndex: index,
        unitId: data.unitId
      }
      localStorage.setItem('unitData', JSON.stringify(unitData))
    },
    queueMessage() {
      const loading = this.$loading({
        lock: true,
        text: '休息一下,数据正在努力加载中(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      getQueueList(this.queueForm).then(res => {
        this.queueData = res.data
        let bufferUnitDate = JSON.parse(localStorage.getItem('unitData'))
        if (bufferUnitDate) {
          this.queueData.forEach((item, index) => {
            if (item.unitId === bufferUnitDate.unitId) {
              this.unitIndex = index
            }
          })
        }
        this.unitData = res.data[this.unitIndex]
        this.queueStatus = true
      }).finally(() => {
        loading.close();
      })
    },
    handleResize() {
      this.tableHeight = window.innerHeight // 更新高度数据
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../assets/styles/singlePage";
.queue-home {
  margin: 0;
  padding: 10px;

  .select-button {
    display: flex;
    justify-content: center;
    padding-bottom: 10px;

    .button-item {
      width: 200px;
      font-size: 20px;
    }
  }

  .queue-master {
    display: flex;
    justify-content: space-around;

    :hover {
      cursor: pointer;
      color: #ffffff;
      box-shadow: 0 0 0 0 grey;
      transform: scale(1.1);
      background: #1890ff !important;
    }

    .clickUnit {
      color: #ffffff;
      box-shadow: 0 0 0 0 grey;
      transform: scale(1.1);
      background: #1890ff !important;
    }

    .queue-title {
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 20%;
      height: 40px;
      padding: 10px;
      font-size: 18px;
      border: 1px solid #1e47a1;
      border-radius: 10px;
    }
  }

  .queue-window-master {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-around;
    width: 100%;

    .queue-border {
      border: 1px solid #DDD6EF;
      width: 45%;
      margin-top: 1%;
      height: 200px;
      border-radius: 10px;
      box-shadow: 1px 1px 10px 2px rgba(0, 0, 0, 0.2);

      .queue-border-title {
        height: 35px;
        border-bottom: 1px solid #00a19b;
        background-color: #DDD6EF;
        border-radius: 10px 10px 0 0;
        font-size: 20px;
        display: flex;
        justify-content: space-between;
        .queue-border-title-font{
          display: flex;
          align-items: center;
        }
        .title-item-button{
          margin-right: 20px;
          display: flex;
          align-items: center;
          border-radius: 5px;
          width: 60px;
          justify-content: center;
        }

        .title-item {
          margin-left: 15px;
        }
      }

      .queue-border-item {
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 165px;

        .window-item {
          font-size: 24px;
          width: 80px;
          border: 1px solid #00a19b;
          border-radius: 50px;
          display: flex;
          height: 80px;
          color: #606266;
          justify-content: space-around;
          align-items: center;

          ::v-deep.el-button--medium {
            padding: 15px;
            font-size: 26px;
            color: #606266;
          }
        }

        .window-item-title {
          display: flex;
          justify-content: space-around;
          font-size: 18px;
          margin-top: 5px;
          margin-bottom: -5px;
        }

        .await:hover {
          cursor: pointer;
          box-shadow: 0 0 0 0 grey;
          transform: scale(1.1);
          background: #96CDCD !important;

          ::v-deep.el-button--medium {
            color: #67B1FB !important;
          }
        }

        .await {
          background-color: #96CDCD;
        }

        .seeADoctor:hover {
          cursor: pointer;
          box-shadow: 0 0 0 0 grey;
          transform: scale(1.1);
          background: #4EEE94 !important;

          ::v-deep.el-button--medium {
            color: #67B1FB !important;
          }
        }

        .seeADoctor {
          background-color: #4EEE94;
        }

        .delete:hover {
          cursor: pointer;
          box-shadow: 0 0 0 0 grey;
          transform: scale(1.1);
          background: #e66037 !important;

          ::v-deep.el-button--medium {
            color: #67B1FB !important;
          }
        }

        .delete {
          background-color: #e66037;
        }
      }


    }
  }

  .queue-table {
    ::v-deep.el-table--medium .el-table__cell {
      padding: 2px 0;
    }
  }

  ::v-deep.el-dialog__body {
    padding: 10px 20px;
  }
  ::v-deep.el-input.is-disabled .el-input__inner {
    background-color: #ffffff;
    color: black;
    cursor: not-allowed;
  }

}

</style>
