<template>
  <div class="confirm-form-home">
    <el-form :model="selectForm" label-width="60px">
      <el-form-item label="预约日期:" label-width="70px">
        <div class="form-flex">
          <el-date-picker v-model="selectForm.beginDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
          <div class="date-f">-</div>
          <el-date-picker v-model="selectForm.endDate" type="date" value-format="yyyy-MM-dd"></el-date-picker>
        </div>
      </el-form-item>
      <div class="form-flex">
        <el-form-item class="table-width" label="检查号:">
          <el-input v-model="selectForm.examNo"></el-input>
        </el-form-item>
        <el-form-item class="table-width" label="姓名:">
          <el-input v-model="selectForm.name"></el-input>
        </el-form-item>
      </div>
      <div class="form-flex">
        <el-form-item class="table-width" label="项目名称:">
          <el-input v-model="selectForm.item" clearable></el-input>
        </el-form-item>
        <el-form-item class="table-width" label="病人ID:">
          <el-input v-model="selectForm.patientId" clearable></el-input>
        </el-form-item>
      </div>

    </el-form>
  </div>
</template>

<script>
/**
 * form查询
 */
import { GetKnowledgeBaseInitData } from '@/api/checkAndConfirm/checkCommon'
export default {
  name: 'formSelect',
  props: [],
  components: {},
  data() {
    return {
      selectForm: {
        beginDate: '',
        endDate: '',
        examNo: '',
        name: '',
        item: '',
        patientId: '',
        deptCode: this.$store.getters.examAuthDeptCodes,
        pageSize: 20,
        pageNum: 1,
        type: 1,
        diagType: 1,
        // deptCode: this.$store.getters.deptCode,
      },
      autherEntity: {
        autherKey: 'DA12FB821147938CA09641D3B51365C5',
        userGuid: '',
        serialNumber: '',
        doctorGuid: '',
        doctorName: '',
        department: '',
        hospitalGuid: '1',
        hospitalName: '河南宏力医院',
        customEnv: '2',
        flag: 'c'
      }
    }
  },
  created() {
    this.thisDate()
  },
  mounted() {
  },
  methods: {
    init(data) {
      HM.maysonLoader(data, function(mayson) {
        // mayson.closeMaysonPro();
        mayson.setDrMaysonConfig('m', 1)
        window.mayson = mayson
        mayson.ai()
      })
    },
    createInit(userGuid, serialNumber, department){
      this.autherEntity.userGuid = userGuid
      this.autherEntity.serialNumber = serialNumber
      this.autherEntity.doctorGuid = this.$store.getters.empNo
      this.autherEntity.doctorName = this.$store.getters.name
      this.autherEntity.department = department
      this.init(this.autherEntity)
    },
    thisDate() {
      let thisDate = this.formatDate(new Date())
      this.selectForm.beginDate = thisDate
      this.selectForm.endDate = thisDate
      this.$store.commit("SET_PATIENT",this.selectForm);
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0') // 月份从0开始，因此需要+1
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    patientIdCapture(patientId){
      this.selectForm.patientId = patientId;
      this.patientIdMonitor();
      //知识库识别
      this.knowledgeBase(patientId);
    },
    center() {
      let beginDate = this.selectForm.beginDate;
      let endDate = this.selectForm.endDate;
      this.selectForm = {
        beginDate: beginDate,
        endDate: endDate,
        examNo: '',
        name: '',
        item: '',
        patientId: '',
        deptCode: this.$store.getters.patient.deptCode,
        pageSize: 20,
        pageNum: 1,
        type: 1,
        diagType: 1,
        // deptCode: this.$store.getters.deptCode,
      }
      this.patientIdMonitor();
    },
    patientIdMonitor(){
      this.$store.commit("SET_PATIENT",this.selectForm);
      this.$emit("send-table",this.selectForm)
    },
    knowledgeBase(patientId){
      let deptCode = this.$store.getters.deptCode;
      if (patientId){
        GetKnowledgeBaseInitData(patientId,deptCode).then(t => {
          this.createInit(patientId,t.data.serialNumber,t.data.deptName)
        })
      }
    },
  }
}
</script>

<style scoped lang="scss">

.confirm-form-home {
  ::v-deep.el-input--medium .el-input__inner {
    height: 24px;
    line-height: 36px;
  }

  ::v-deep.el-form-item__label {
    font-size: 12px;
    padding: 0 6px 0 0;
  }

  ::v-deep.el-input__inner {
    padding: 0 5px;
  }

  ::v-deep.el-form-item {
    margin-bottom: 2px;
  }

  ::v-deep.el-date-editor.el-input, .el-date-editor.el-input__inner {
    width: 90px;
  }

  ::v-deep.el-input__icon {
    display: none;
  }

  .table-width {
    width: 150px;
  }

  .form-flex {
    display: flex;
  }

  .date-f {
    padding: 0 5px;
  }
}
</style>
