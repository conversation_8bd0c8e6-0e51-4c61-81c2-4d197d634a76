<template>
    <div class="single-master">
        <div class="single-title">制度管理模块</div>
        <div class="single-element">
            <div class="element-master">
                <!-- form -->
                <div class="element-form">
                    <el-form :inline="true" :model="queueForm">
                        <el-form-item label="委员会名称:">
                            <el-select v-model="queueForm.committeeName" filterable clearable placeholder="请选择">
                                <el-option v-for="item in committeeNameDict" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="年度:">
                            <el-date-picker v-model="queueForm.committeeYear" type="year" placeholder="选择年"
                                value-format="yyyy" format="yyyy" />
                        </el-form-item>
                        <el-form-item>
                            <div class="element-button">
                                <el-button type="primary" icon="el-icon-search" @click="getList">查询</el-button>
                                <el-button type="primary" icon="el-icon-plus" @click="add">新增</el-button>
                                <el-button type="primary" icon="el-icon-edit-outline" @click="historical">历史记录</el-button>
                                <el-button type="primary" icon="el-icon-delete" @click="del">删除</el-button>
                                <el-button type="primary" icon="el-icon-share" @click="indexOne">首页</el-button>
                            </div>
                        </el-form-item>
                    </el-form>
                </div>
                <!-- table -->
                <div class="element-table">
                    <div class="my-table">
                        <el-table :data="tableDate" style="width: 100%" border :height="tableHeight - 117"
                            highlight-current-row @row-click="handleRowClick" @cell-dblclick="doubleSelectionChange">
                            <el-table-column type="index" align="center" />
                            <el-table-column prop="id" align="center" label="委员会名称" />
                            <el-table-column prop="committeeyear" align="center" label="年度" />
                            <el-table-column prop="filename" align="center" label="文件名称" />
                        </el-table>
                    </div>
                </div>
                <!-- 新增dialog -->
                <el-dialog :visible.sync="dialogOpen" :title="titleDialog" width="75%">
                    <el-form :inline="true" :model="queueFormFile" :rules="rules" ref="dataForm">
                        <el-form-item label="委员会名称:" prop="committeeName">
                            <el-select v-model="queueFormFile.committeeName" filterable clearable placeholder="请选择">
                                <el-option v-for="item in committeeNameDict" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="年度:" prop="committeeYear">
                            <el-date-picker v-model="queueFormFile.committeeYear" type="year" placeholder="选择年"
                                value-format="yyyy" format="yyyy" />
                        </el-form-item>
                        <div>
                            <el-form-item label="文件上传:">
                                <el-upload class="upload-demo" ref="upload" action="#" :multiple="false"
                                    :show-file-list="true" :before-upload="beforeUpload" accept=".pdf"
                                    :on-preview="handlePreview" :on-exceed="handleExceed" :on-change="onChange"
                                    :file-list="fileList" :auto-upload="false" :limit="10" drag>
                                    <i class="el-icon-upload">PDF文件</i>
                                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                                    <div class="el-upload__tip" slot="tip">只能上传PDF文件</div>
                                </el-upload>
                            </el-form-item>
                        </div>
                    </el-form>
                    <span slot="footer">
                        <el-button type="primary" @click="dialogOpen = false">取 消</el-button>
                        <el-button type="primary" @click="saveUpload">确 定</el-button>
                    </span>
                </el-dialog>
                <!-- 双击drawer -->
                <el-drawer :title="titleDrawer" :visible.sync="drawerOpen" :direction="direction"
                    :before-close="handleClose" size="85%">
                    <iframe :src="srcFileUrl" frameborder="no" style="width: 100%; height: 100%" scrolling="auto" />
                </el-drawer>
                <!-- 历史记录drawer -->
                <el-drawer :title="titleDrawerX" :visible.sync="drawerOpenX" :direction="directionX"
                    :before-close="handleCloseHis" size="80%">
                    <div class="single-master">
                        <div class="single-element">
                            <div class="element-master">
                                <div class="element-form">
                                    <el-form :inline="true" :model="queueFormHis">
                                        <el-form-item label="委员会名称:">
                                            <el-select v-model="queueFormHis.committeeName" filterable clearable
                                                placeholder="请选择">
                                                <el-option v-for="item in committeeNameDict" :key="item.value"
                                                    :label="item.label" :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="年度:">
                                            <el-date-picker v-model="queueFormHis.committeeYear" type="year"
                                                placeholder="选择年" value-format="yyyy" format="yyyy" />
                                        </el-form-item>
                                        <el-form-item>
                                            <div class="element-button">
                                                <el-button type="primary" icon="el-icon-search"
                                                    @click="getHistorical">查询</el-button>
                                                <el-button type="primary" icon="el-icon-delete"
                                                    @click="delHis">删除</el-button>
                                            </div>
                                        </el-form-item>
                                    </el-form>
                                </div>
                                <div class="element-table">
                                    <div class="my-table">
                                        <el-table :data="tableDateHis" style="width: 100%" border
                                            :height="tableHeight - 279" highlight-current-row @row-click="handleRowClick"
                                            @cell-dblclick="doubleSelectionChange">
                                            <el-table-column type="index" align="center" />
                                            <el-table-column prop="id" align="center" label="委员会名称" />
                                            <el-table-column prop="committeeyear" align="center" label="年度" />
                                            <el-table-column prop="filename" align="center" label="文件名称" />
                                        </el-table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-drawer>
            </div>
        </div>
    </div>
</template>

<script>
import {
    GetCommitteeNameDict,
    GetCommitteeNameFile,
    SystemManagementFile,
    SystemManagementDel,
    GetCommitteeNameFileHistorical,
    SystemManagementDelHis
} from "@/api/singlePage/qualityCommittee/systemManagement";
import env from '@/utils/ApiConfig';
export default {
    name: '',
    props: [],
    data() {
        return {
            queueForm: {
                pageNum: 1,
                pageSize: 20,
                committeeName: '',
                committeeYear: ''
            },
            queueFormFile: { // 弹框参数
                committeeName: '',
                committeeYear: '',
                fileName: '',
                raw: ''
            },
            queueFormHis: { // 历史记录参数
                committeeName: '',
                committeeYear: '',
            },
            envUrl: env.get_file_url(),
            srcFileUrl: '', // 文件路径
            delForm: {}, // 删除参数
            doubleRowData: {}, // 双击某一行参数
            titleDialog: '制度管理文件上传', // 新增弹框标题
            dialogOpen: false, // 新增弹框标识
            drawerOpen: false, // 双击抽屉弹框标识
            titleDrawer: '制度管理文件查看', // 双击抽屉弹框标题
            direction: 'btt', // 双击抽屉弹框打开方式 (ltr:从左往右开,rtl:从右往左开,ttb:从上往下开,btt:从下往上开,)
            drawerOpenX: false, // 历史记录抽屉弹框标识
            titleDrawerX: '历史记录数据查看', // 历史记录抽屉弹框标题
            directionX: 'ttb', // 双击抽屉弹框打开方式 (ltr:从左往右开,rtl:从右往左开,ttb:从上往下开,btt:从下往上开,)
            fileList: [], // 文件上传
            tableDate: [],
            committeeNameDict: [], // 委员会名称字典数据
            tableDateHis: [], // 历史记录数据
            tableHeight: undefined,
            extraFunctionKey: 0,
            rules: {
                committeeName: [
                    { required: true, message: '请输入委员会名称', trigger: 'blur' },
                ],
                committeeYear: [
                    { required: true, message: '请输入年度', trigger: 'blur' },
                ]
            }
        }
    },

    created() {
        this.getList();
        this.handleResize();
    },

    mounted() {
        window.addEventListener('resize', this.handleResize); // 添加监听器
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize); // 移除监听器
    },

    methods: {
        // 初始化数据
        getList() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            // 查询制度管理上传文件
            GetCommitteeNameFile(this.queueForm).then(res => {
                this.tableDate = res.data.list;
            }).finally(() => {
                loading.close();
            });
            // 委员会名称字典数据查询
            GetCommitteeNameDict().then(res => {
                this.committeeNameDict = res.data.getCommitteeNameDictRetVueModels;
            }).finally(() => {
                loading.close();
            });
        },

        // 查询历史记录
        getHistorical() {
            const loading = this.$loading({
                lock: true,
                text: "休息一下,数据正在努力加载中(●" + "◡" + "●)",
                spinner: "el-icon-coffee-cup",
                background: "rgba(0, 0, 0, 0.7)",
            });
            // 查询制度管理上传文件
            GetCommitteeNameFileHistorical(this.queueFormHis).then(res => {
                this.tableDateHis = res.data.list;
            }).finally(() => {
                loading.close();
            });
        },

        // 历史记录按钮操作
        historical() {
            this.drawerOpenX = true;
            this.getHistorical();
        },

        // 历史记录删除按钮操作
        delHis() {
            if (this.delForm.fileid === undefined) {
                this.showMessageA("请选择要删除的数据！");
            } else {
                let fileid = this.delForm.fileid;
                this.$modal.confirm(
                    '确认要删除委员会名称为: ' + this.delForm.id +
                    '年度为:' + this.delForm.committeeyear +
                    '文件名称为:' + this.delForm.filename +
                    '的数据吗?')
                    .then(function () {
                        return SystemManagementDelHis(fileid);
                    })
                    .then(() => {
                        this.showMessageA("删除数据成功");
                        this.reset();
                        this.getHistorical();
                    })
                    .catch(() => {
                        this.reset();
                        this.showMessageA("删除数据失败");
                    });
            }
        },

        // 删除按钮操作
        del() {
            if (this.delForm.fileid === undefined) {
                this.showMessageA("请选择要删除的数据！");
            } else {
                let fileid = this.delForm.fileid;
                this.$modal.confirm(
                    '确认要删除委员会名称为: ' + this.delForm.id +
                    '年度为:' + this.delForm.committeeyear +
                    '文件名称为:' + this.delForm.filename +
                    '的数据吗?')
                    .then(function () {
                        return SystemManagementDel(fileid);
                    })
                    .then(() => {
                        this.showMessageA("删除数据成功");
                        this.reset();
                        this.getList();
                    })
                    .catch(() => {
                        this.reset();
                        this.showMessageA("删除数据失败");
                    });
            }
        },

        // 新增按钮操作
        add() {
            this.dialogOpen = true;
        },

        // 新增弹框确定按钮操作
        saveUpload: function () {
            this.$refs["dataForm"].validate((valid) => {
                if (valid) {
                    // 判断是否上传文件
                    if (this.fileList.length == 0) {
                        this.showMessageA("请上传文件");
                    } else {
                        if (this.fileList.length > 0) {
                            var param = new FormData();
                            this.fileList.forEach((item) => {
                                param.append('file', item.raw);
                                param.append('fileName', item.name);
                                param.append('committeeName', this.queueFormFile.committeeName);
                                param.append('committeeYear', this.queueFormFile.committeeYear);
                            });
                            SystemManagementFile(param).then((response) => {
                                if (response.code == 200) {
                                    this.showMessageA("上传文件成功");
                                    this.fileList = [];
                                    this.queueFormFile = {};
                                    this.getList();
                                    this.dialogOpen = false;
                                } else {
                                    this.showMessageA("上传文件失败,请联系信息科!!!");
                                }
                            });
                        }
                    }
                }
            })
        },

        /** 文件上传功能限制 */
        // 文件上传-限制文件大小
        beforeUpload(file) {
            const isLt10M = file.size / 1024 < 50;
            if (!isLt10M) {
                this.$message.error('上传文件过大 ');
                return false;
            }
        },
        // 文件上传-上传数量提示 共选择了
        handleExceed(files, fileList) {
            this.$message.warning(`当前最多选择 10 个文件上传,超出文件最大数量限制`);
        },
        // 文件上传-文件选取完文件触发事件
        onChange(a, fileList) {
            this.fileList = fileList;
        },
        handlePreview(file) { },

        // 首页按钮操作
        indexOne() {
            this.$router.push('/singlePage/qualityCommittee/transfer')
        },

        // 单击某一行操作
        handleRowClick(row) {
            this.delForm.fileid = row.fileid;
            this.delForm.id = row.id;
            this.delForm.committeeyear = row.committeeyear;
            this.delForm.filename = row.filename;
        },

        // 双击某一行操作
        doubleSelectionChange(row) {
            this.doubleRowData = row;
            ++this.extraFunctionKey;
            this.drawerOpen = true;
            this.srcFileUrl = this.envUrl + row.fileurl; // 获取文件路由
        },

        // draw弹框关闭提示消息
        handleClose(done) {
            this.$confirm('确认关闭？')
                .then(_ => {
                    done();
                    this.reset();
                    this.getList();
                })
                .catch(_ => { });
        },

        // drawHis弹框关闭提示消息
        handleCloseHis(done) {
            this.$confirm('确认关闭？')
                .then(_ => {
                    done();
                    this.reset();
                    this.getList();
                    this.getHistorical();
                })
                .catch(_ => { });
        },

        // 重置按钮操作
        reset() {
            this.delForm = {};
        },

        // 提示信息封装
        showMessageA(message) {
            const h = this.$createElement;
            this.$notify({
                title: '提示信息！',
                message: h('i', { style: 'color: teal' }, message)
            });
        },

        // 自定义高度变化更新高度
        handleResize() {
            this.tableHeight = window.innerHeight; // 更新高度数据
        },
    }
}
</script>
  
<style scoped lang="scss">
@import "../../../assets/styles/singlePage";

.my-table {
    ::v-deep.el-table--medium .el-table__cell {
        padding: 0;
    }

    ::v-deep.el-table .el-table__header-wrapper th,
    .el-table .el-table__fixed-header-wrapper th {
        word-break: break-word;
        background-color: #4f617238;
        color: #303133;
        height: 30px;
        font-size: 10px;
    }

    ::v-deep.el-table th.el-table__cell>.cell {
        padding: 0;
    }

    ::v-deep.el-table--border .el-table__cell:first-child .cell {
        padding: 0;
    }

    ::v-deep.el-button+.el-button {
        margin-left: 2px;
    }

    ::v-deep.el-table .cell {
        padding: 1px;
    }

    // 滚动条的滑块
    ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
        background-color: rgb(13, 192, 132);
        border-radius: 1px;
    }
}
</style>

