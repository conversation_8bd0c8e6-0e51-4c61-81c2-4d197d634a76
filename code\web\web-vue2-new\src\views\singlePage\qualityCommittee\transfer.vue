<template>
    <!-- 委员会管理中心路由跳转 -->
    <div :style="'height:' + tableHeight + 'px'">
        <div class="logo-home">
            <div class="login-master">
                <div class="login-form">
                    <div class="logo_container"></div>
                    <div class="logo_title">{{ systemTitle }}</div>
                    <div class="menu-list">
                        <div @click="clickEvent(item.path)" :style="menuWidth" class="menu-list-for"
                            v-for="item in menuList" :key="item.id">
                            <img class="menu-list-img" :src="item.icon">
                            <div class="menu-list-span">
                                <span class="menu-list-test">{{ item.text }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
  
<script>

export default {
    data() {
        return {
            systemTitle: '委员会管理',
            tableHeight: undefined,
            menuList: [{
                id: "1",
                icon: require("../../../assets/icons/hospital/one.png"),
                text: "制度管理",
                path: "/singlePage/qualityCommittee/systemManagement",
            }, {
                id: "2",
                icon: require("../../../assets/icons/hospital/two.png"),
                text: "框架管理",
                path: "/singlePage/qualityCommittee/frameworkManagement",
            }, {
                id: "3",
                icon: require("../../../assets/icons/hospital/three.png"),
                text: "会议管理",
                path: "/singlePage/qualityCommittee/meetingManagement",
            }, {
                id: "4",
                icon: require("../../../assets/icons/hospital/four.png"),
                text: "质量改进项目",
                path: "/singlePage/qualityCommittee/improvementProject",
            }, {
                id: "5",
                icon: require("../../../assets/icons/hospital/five.png"),
                text: "问题管理",
                path: "/singlePage/qualityCommittee/problemManagement",
            },],
            menuWidth: '',
            menuHeight: ''
        }
    },

    created() {
        // this.handleResize();
    },

    mounted() {
        window.addEventListener('resize', this.handleResize) // 添加监听器
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize) // 移除监听器
    },

    methods: {
        clickEvent(path) {
            this.$router.push(path);
        },

        handleResize() {
            this.tableHeight = window.innerHeight // 更新高度数据
        }
    },
}
</script>
  
<style scoped lang="scss">
.item-left {
    height: 100%;
    width: 15%;
    border-right: 1xp solid #DFECFD;

    ::v-deep.el-menu-item {
        border-bottom: 1px solid #DFECFD;
    }
}

.item-right {
    width: 85%;
}

.logo-home {
    background-color: #E8E8E8;
    height: 100%;

    .login-master {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80%;

        @media screen and (max-width: 2000px) {
            .login-form {
                width: 25%
            }
        }

        @media screen and (max-width: 1600px) {
            .login-form {
                width: 30%
            }
        }

        @media screen and (max-width: 1400px) {
            .login-form {
                width: 35%
            }
        }

        @media screen and (max-width: 1200px) {
            .login-form {
                width: 40%
            }
        }

        @media screen and (max-width: 1000px) {
            .login-form {
                width: 50%
            }
        }

        @media screen and (max-width: 600px) {
            .login-form {
                width: 80%
            }
        }

        @media screen and (max-height: 500px) {
            .login-form {
                height: 120% !important;
                width: 50%;

                .logo_container {
                    width: 60px;
                    height: 60px;
                }

                .logo_title {
                    font-size: 1rem;
                    letter-spacing: 0.3em;
                }

                .logo_subtitle {
                    font-size: 0.625rem;
                    line-height: 1rem;
                }

                .input_container {
                    width: 80% !important;
                }

                .input_label {
                    font-size: 0.65rem !important;
                }

                .input_field {
                    height: 35px !important;
                }
            }
        }

        .login-form {
            height: 80%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            gap: 15px;
            padding: 50px 40px 20px 40px;
            background-color: #ffffff;
            box-shadow: 0px 106px 42px rgba(0, 0, 0, 0.01),
                0px 59px 36px rgba(0, 0, 0, 0.05), 0px 26px 26px rgba(0, 0, 0, 0.09),
                0px 7px 15px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);
            border-radius: 11px;
            font-family: "Inter", sans-serif;

            .logo_container {
                box-sizing: border-box;
                width: 80px;
                height: 80px;
                background: url("../../../assets/logo/honliv.jpg") no-repeat;
                background-size: 100% 100%;
                border: 1px solid #F7F7F8;
                filter: drop-shadow(0px 0.5px 0.5px #EFEFEF) drop-shadow(0px 1px 0.5px rgba(239, 239, 239, 0.5));
                border-radius: 11px;
            }

            .logo_title {
                margin: 0;
                font-size: 1.35rem;
                font-weight: 700;
                color: #212121;
                letter-spacing: 0.5em;
            }

            .logo_subtitle {
                font-size: 0.725rem;
                max-width: 80%;
                text-align: center;
                line-height: 1.1rem;
                color: #8B8E98
            }

            .input_container {
                width: 100%;
                height: fit-content;
                position: relative;
                display: flex;
                flex-direction: column;
                gap: 5px;
            }

            .icon {
                width: 20px;
                position: absolute;
                z-index: 99;
                left: 12px;
                bottom: 9px;
            }

            .input_label {
                font-size: 0.75rem;
                color: #8B8E98;
                font-weight: 600;
            }

            .input_field {
                width: auto;
                height: 40px;
                padding: 0 0 0 40px;
                border-radius: 7px;
                outline: none;
                border: 1px solid #e5e5e5;
                filter: drop-shadow(0px 1px 0px #efefef) drop-shadow(0px 1px 0.5px rgba(239, 239, 239, 0.5));
                transition: all 0.3s cubic-bezier(0.15, 0.83, 0.66, 1);
            }

            .input_field:focus {
                border: 1px solid transparent;
                box-shadow: 0px 0px 0px 2px #242424;
                background-color: transparent;
            }
        }

    }

    ::v-deep.el-tabs__nav {
        width: 100%;
    }

    ::v-deep.el-tabs__item {
        width: 50%;
        text-align: center;
    }

    ::v-deep.el-tabs__item.is-active {
        color: #1890ff;
        text-align: center;
    }

    ::v-deep.el-tabs--card>.el-tabs__header .el-tabs__item {
        border-bottom: 1px solid #DFE4ED;
    }
}

.body {
    padding: 0;
    margin: 0;
}

.app-home {
    width: 100%;
    border: 1px solid #EFF0EB;
    background-color: #EFF0EB;
}

.menu-list {
    margin-top: 1px;
    display: flex;
    flex-wrap: wrap;

    :hover {
        cursor: pointer;
        color: #9f0176 !important;
        box-shadow: 0 0px 0px 0 grey;
        transform: translate(0, -0px);
        transform: scale(1.04);
    }

    .menu-list-for {
        display: flex;
        padding: 5px 10px 5px 10px;
        margin: 5px 10px 5px 3px;
        border: 1px solid #FFFFFF;
        background-color: #FFFFFF;
        border-radius: 5px 5px 5px 5px;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: wrap;
        align-content: space-between;
        flex-direction: row;

        .menu-list-img {
            width: 20%;
            height: 80%;
            margin-top: 2%;
            border: 1px solid #b4bccc;
            border-radius: 100px;
        }

        .menu-list-span {
            display: flex;
            flex-direction: column;
            margin: 0 auto;

            .menu-list-sum {
                margin: 0 auto;
                font-size: 20px;
                color: #1c84c6;
            }

            .menu-list-test {
                color: #001529;
            }
        }
    }
}
</style>
  