import { login, logout, getInfo, autoLogin } from '@/api/login'
import { getToken, setToken, removeToken,getAccessToken,getAccessId,removeAccessToken } from '@/utils/auth'

const user = {
  state: {
    token: getToken(),
    accessToken: getAccessToken(),
    accessId: getAccessId(),
    id: '',
    name: '',
    avatar: '',
    loginType: '',
    authDeptCodes: [],
    roles: [],
    permissions: [],
    patient: {},
    optForTable: [],
    rowTable: {},
    reservationTable: [],
    reservationRowTable: {},
    examButtonStatus: {},
    exportDictExam: [],
    appointmentExamNos: [],
    appointmentDeptDict: [],
  },

  mutations: {
    SET_AUTH_DEPT_CODES: (state, authDeptCodes) => {
      state.authDeptCodes = authDeptCodes
    },
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    LOGIN_TYPE: (state, loginType) => {
      state.loginType = loginType
    },
    SET_ID: (state, id) => {
      state.id = id
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_DEPTCODE: (state, deptCode) => {
      state.deptCode = deptCode
    },
    SET_PATIENT: (state, patient) => {
      state.patient = patient
    },
    SET_TABLE: (state, optForTable) => {
      state.optForTable = optForTable
    },
    SET_ROWS: (state, rowTable) => {
      state.rowTable = rowTable
    },
    SET_RESERVATION_TABLE: (state, reservationTable) => {
      state.reservationTable = reservationTable
    },
    SET_RESERVATION_ROW_TABLE: (state, reservationRowTable) => {
      state.reservationRowTable = reservationRowTable
    },
    SET_EXAM_BUTTON_STATUS: (state, examButtonStatus) => {
      state.examButtonStatus = examButtonStatus
    },
    SET_EXPORT_DICT_EXAM: (state, exportDictExam) => {
      state.exportDictExam = exportDictExam
    },
    SET_APPOINTMENTS_EXAM_NO: (state, appointmentExamNos) => {
      state.appointmentExamNos = appointmentExamNos
    },
    SET_APPOINTMENT_DEPT_DICT: (state, appointmentDeptDict) => {
      state.appointmentDeptDict = appointmentDeptDict
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      return new Promise((resolve, reject) => {
        login(username, password).then(res => {
          setToken(res.data.token)
          commit('SET_TOKEN', res.data.token)
          sessionStorage.setItem('token', res.data.token);
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    AutoLogin({ commit }, ssoInfo) {
      const appId = ssoInfo.appId
      const accessToken = ssoInfo.accessToken
      sessionStorage.setItem('Access-Token', ssoInfo.accessToken);
      return new Promise((resolve, reject) => {
        autoLogin(appId, accessToken).then(res => {
          setToken(res.data.token)
          commit('SET_TOKEN', res.data.token)
          sessionStorage.setItem('token', res.data.token);
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.data.user
          const avatar = (user.avatar == '' || user.avatar == null) ? require('@/assets/images/profile.jpg') : user.avatar
          if (res.data.roles && res.data.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.data.roles)
            commit('SET_PERMISSIONS', res.data.permissions)
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_AUTH_DEPT_CODES', user.authDeptCodes)
          commit('SET_ID', user.empNo)
          commit('SET_NAME', user.name)
          commit('SET_AVATAR', avatar)
          commit('SET_DEPTCODE', user.deptCode)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      sessionStorage.removeItem('Access-Token')
      sessionStorage.removeItem('token')
      removeAccessToken();
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        resolve()
      })
    }
  }
}

export default user
