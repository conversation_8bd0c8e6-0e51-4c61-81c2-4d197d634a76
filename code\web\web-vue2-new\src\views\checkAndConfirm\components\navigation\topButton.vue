<template>
  <div class="button-home">
    <el-popover placement="right" v-model="visible">
      <div style="display: flex;flex-direction: column;">
        <el-button size="mini" @click="refundInquiryClick">门诊退费查询</el-button>
        <el-button size="mini" @click="imageRecordClick">患者开单记录查询</el-button>
        <el-button size="mini" @click="checkSubclassesClick">检查子类汇总查询</el-button>
        <el-button size="mini" @click="workloadSummarizingClick">工作量汇总</el-button>
        <el-button size="mini" @click="patientInfoUpdateClick">患者信息调整</el-button>
        <el-button size="mini" @click="csExamReportClick">门诊超声报告</el-button>
      </div>
      <el-button slot="reference" icon="el-icon-s-fold" type="text"></el-button>
    </el-popover>
    <div>
      <image-billing-record ref="imageBillingRecordRefs"></image-billing-record>
      <refund-inquiry ref="refundInquiryRefs"></refund-inquiry>
      <check-subclass ref="checkSubclassesRefs"></check-subclass>
      <workload-summarizing-index ref="workloadSummarizingRefs"></workload-summarizing-index>
      <patient-info-update ref="patientInfoUpdateRefs"></patient-info-update>
    </div>
  </div>
</template>

<script>

import ImageBillingRecord from '../topFunction/imageBillingRecord.vue'
import RefundInquiry from '../topFunction/refundInquiry.vue'
import CheckSubclass from '../topFunction/checkSubclass.vue'
import WorkloadSummarizingIndex from '../topFunction/workloadSummarizingIndex.vue'
import PatientInfoUpdate from '../topFunction/patientInfoUpdate.vue'

export default {
  name: 'topButton',
  props: [],
  components: { PatientInfoUpdate, WorkloadSummarizingIndex, RefundInquiry, ImageBillingRecord, CheckSubclass },
  data() {
    return {
      visible: false
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    patientInfoUpdateClick(){
      this.$refs.patientInfoUpdateRefs.init(true);
    },
    checkSubclassesClick() {
      this.$refs.checkSubclassesRefs.init(true)
    },
    imageRecordClick() {
      this.$refs.imageBillingRecordRefs.billingRecordVerify(true)
    },
    refundInquiryClick() {
      this.$refs.refundInquiryRefs.init(true);
    },
    workloadSummarizingClick(){
      this.$refs.workloadSummarizingRefs.init(true);
    },
    csExamReportClick(){
      if (window.location.hostname.includes("192.168")){
        let path = "http://*************:10008/exam-report";
        window.open(path, '_blank');
      }else{
        let path = "http://*********:10008/exam-report";
        window.open(path, '_blank');
      }
    },
  }
}
</script>

<style scoped lang="scss">
.button-home {
  background-color: #00a19b;

  ::v-deep.el-button--text {
    width: 40px;
    color: #FFF;
    font-size: 1.8rem;
    height: 50px;
  }

  @media screen and (max-height: 650px) {
    ::v-deep.el-button--text {
      width: 30px;
      color: #FFF;
      font-size: 1rem;
      height: 30px;
    }
  }
}
</style>
<style scoped>
/deep/ .el-button+.el-button {
  margin-top: 2px;
  margin-left: 0px;
}
</style>
