<template>
  <div></div>
</template>

<script>
/**
 * 读卡器功能
 */
import wp from '@/utils/common/web-proxy';
import { CardReader } from '@/utils/common/pb-proxy'
import { CardRecognitionPatientId } from '@/api/checkAndConfirm/checkCommon'
export default {
  name: 'readCard',
  props: [],
  components: {},
  data() {
    return {}
  },
  methods: {
    read() {
      wp.card.readCard((result) => {
        if (result.success) {
          let input_content = result.data.card_no
          CardRecognitionPatientId(input_content).then(res => {
            this.$emit("card-no", res.data)
          })
        }else{
          CardReader().then(res => {
            let card_no = res.data.data.card_no;
            CardRecognitionPatientId(card_no).then(res => {
              this.$emit("card-no", res.data)
            })
          })
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">

</style>
