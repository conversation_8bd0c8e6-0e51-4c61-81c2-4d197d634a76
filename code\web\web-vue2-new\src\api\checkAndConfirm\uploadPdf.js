import request from '@/utils/request2'

export function uploadMsgInitialize(examNo,empNo) {
  return request({
    url: '/examPdfPath/initialize/'+ examNo + "/" + empNo,
    method: 'get',
  })
}

export function UploadPdf(data) {
  return request({
    url: '/examPdfPath/uploadPdf',
    method: 'post',
    data:data
  })
}

export function DelPdf(examNo,examItemNo) {
  return request({
    url: '/examPdfPath/del/' + examNo + "/" + examItemNo,
    method: 'delete',
  })
}

export function GetImages(examNo) {
  return request({
    url: '/examPdfPath/exam/pdfImages/' + examNo,
    method: 'get',
  })
}

export function DownloadPdf(examNo,examItemNo) {
  return request({
    url: '/examPdfPath/exam/downloadPdf/' + examNo + "/" + examItemNo,
    method: 'get',
  })
}


