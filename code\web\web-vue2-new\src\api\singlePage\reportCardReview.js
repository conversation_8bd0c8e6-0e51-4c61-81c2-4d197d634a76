import request from '@/utils/request'

// 初始化数据(临床)
export function GetReportCardReviewClinical(data) {
    return request({
        url: '/reportCardReview/GetReportCardReviewClinical',
        method: 'post',
        data: data
    })
}

// 初始化数据(临床)
export function GetReportCardReviewTechnology(data) {
    return request({
        url: '/reportCardReview/GetReportCardReviewTechnology',
        method: 'post',
        data: data
    })
}

// 删除
export function DelReportCardReview(patientId, serialNo) {
    return request({
        url: '/reportCardReview/DelReportCardReview?patientId=' + patientId + '&serialNo=' + serialNo,
        method: 'get'
    })
}

// 回显
export function DetailReportCardReview(data) {
    return request({
        url: '/reportCardReview/DetailReportCardReview',
        method: 'get',
        params: data
    })
}

// 更改状态按钮操作
export function ChangeStateButton(data) {
    return request({
        url: '/reportCardReview/ChangeStateButton',
        method: 'get',
        params: data
    })
}

// 审核通过按钮操作
export function ApprovedButton(data) {
    return request({
        url: '/reportCardReview/ApprovedButton',
        method: 'get',
        params: data
    })
}

// 驳回修改按钮操作
export function RejectModificationButton(data) {
    return request({
        url: '/reportCardReview/RejectModificationButton',
        method: 'get',
        params: data
    })
}

// 肺结核转诊单按钮操作
export function PulmonaryTuberculosisButton() {
    return request({
        url: '/reportCardReview/PulmonaryTuberculosisButton',
        method: 'get'
    })
}

// 判断lI_COUNT等于1还是0  1证明数据库中已经有该条数据,后续进行修改就行,0直接进行新增->(保存前端进行保存操作还是修改操作)
export function JudgeCountReportCardReview(data) {
    return request({
        url: '/reportCardReview/JudgeCountReportCardReview',
        method: 'post',
        data: data
    })
}

// 保存
export function SaveReportCardReview(data) {
    return request({
        url: '/reportCardReview/SaveReportCardReview',
        method: 'post',
        data: data
    })
}

// 修改
export function UpdateReportCardReview(data) {
    return request({
        url: '/reportCardReview/UpdateReportCardReview',
        method: 'post',
        data: data
    })
}