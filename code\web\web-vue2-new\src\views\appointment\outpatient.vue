<template xmlns:el-col="http://www.w3.org/1999/html">
  <div class="home" :style="bodyHeight">
    <!--    dialog详情信息-->
    <div class="my-dialog">
      <!--      <el-dialog :visible.sync="userDialogButton" width="70%">-->
      <!--      </el-dialog>-->
      <el-drawer :visible.sync="userDialogButton" size="70%" style="min-height: 700px;">
        <!--        顶部人员信息-->
        <div class="MyUser">
          <div class="My-el-avatar" v-if="userButton">
            <el-avatar :src="manImages" v-if="appointmentParticulars.user.sex === '男'" size="small"></el-avatar>
            <el-avatar :src="woManImages" v-else-if="appointmentParticulars.user.sex === '女'" size="small"></el-avatar>
            <el-avatar :src="manImages" v-else size="small"></el-avatar>
          </div>
          <div class="my-user-text">
            <span class="my-text" style="font-weight: 1000">{{ appointmentParticulars.user.name }}</span>
            <span class="my-text">/</span>
            <span class="my-text" style="font-weight: 1000">{{ appointmentParticulars.user.sex }}</span>
            <span class="my-text">/</span>
            <span class="my-text" style="font-weight: 1000">{{ appointmentParticulars.user.age }}</span>
            <span class="my-text">/</span>
            <span class="my-text" style="color: #1cbbb4">ID号:</span>
            <span class="my-text" style="color: red">{{ appointmentParticulars.user.patienT_ID }}</span>
            <span class="my-text">/</span>
            <span class="my-text" style="color: #1cbbb4">住院次数:</span>
            <span class="my-text" style="color: #1c84c6">{{ appointmentParticulars.user.visiT_ID }}</span>
            <span class="my-text">/</span>
            <span class="my-text" style="color: #1cbbb4">就诊日期:</span>
            <span class="my-text" style="color: #2c8d54">{{ appointmentParticulars.user.visiT_DATA }}</span>
          </div>
        </div>
        <!--        中间 检查列表、申请单内容-->
        <div class="my-drawer">
          <!--        检查列表-->
          <div class="my-drawer-table">
            <div class="my-header-text" :style="findSize.size3">检查列表</div>
            <el-table ref="multipleTable2" :data="appointmentParticulars.exams" size="mini" border style="width: 100%;">
              <el-table-column align="center" label="操作">
                <template slot-scope="scope">
                  <el-button icon="" size="mini" type="text" @click="chart(scope.row.id)">检查闭环，
                  </el-button>
                  <el-button icon="" size="mini" type="text" @click="handlePrint(scope.row)">打印，
                  </el-button>
                  <el-button icon="" size="mini" type="text" @click="chargeback(scope.row)">撤销
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="exaM_CLASS_NAME" label="主类"></el-table-column>
              <el-table-column align="center" prop="exaM_SUBCLASS_NAME" label="子类"></el-table-column>
              <el-table-column align="center" prop="description" label="项目名称"></el-table-column>
              <el-table-column align="center" prop="appointmenT_DATE" label="日期"></el-table-column>
              <el-table-column align="center" prop="appointmenT_TIME" label="预约时间"></el-table-column>
              <el-table-column align="center" prop="positioN_NAME" label="选择部位"></el-table-column>
              <el-table-column align="center" prop="executE_DEPT_NAME" label="执行科室"></el-table-column>
              <el-table-column align="center" prop="money" label="金额"></el-table-column>

            </el-table>
          </div>
          <!--        申请单内容-->
          <div class="my-drawer-in">
            <div class="my-header-text" :style="findSize.size3">申请单内容</div>
            <el-scrollbar style="height: 96%;">
              <span class="my-sketch-span">1·检查目的</span>
              <div class="my-sketch-input">
                <el-input type="textarea" disabled :rows="2" v-model="appointmentParticulars.user.inspectioN_PURPOSE">
                </el-input>
              </div>
              <span class="my-sketch-span">2·主诉</span>
              <div class="my-sketch-input">
                <el-input type="textarea" disabled :rows="2" v-model="appointmentParticulars.medicalRecord.illnessDesc">
                </el-input>
              </div>

              <span class="my-sketch-span">3·现病史</span>
              <div class="my-sketch-input">
                <el-input type="textarea" disabled :rows="2" v-model="appointmentParticulars.medicalRecord.clinSymp">
                </el-input>
              </div>
              <span class="my-sketch-span">4·体检</span>
              <div class="my-sketch-input">
                <el-input type="textarea" disabled :rows="2" v-model="appointmentParticulars.medicalRecord.physSign">
                </el-input>
              </div>
              <span class="my-sketch-span">5·过去检查</span>
              <div class="my-sketch-input">
                <el-input type="textarea" disabled :rows="2" v-model="appointmentParticulars.medicalRecord.formerlyExam">
                </el-input>
              </div>
              <span class="my-sketch-span">6·临床诊断</span>
              <div class="my-sketch-input">
                <el-input type="textarea" disabled :rows="2" v-model="appointmentParticulars.medicalRecord.clinDiag">
                </el-input>
              </div>
              <span class="my-sketch-span">7·其他诊断</span>
              <div class="my-sketch-input">
                <el-input type="textarea" disabled :rows="2" v-model="appointmentParticulars.medicalRecord.relevantDiag">
                </el-input>
              </div>
              <span class="my-sketch-span">8·相关化验结果</span>
              <div class="my-sketch-input">
                <el-input type="textarea" disabled :rows="2"
                  v-model="appointmentParticulars.medicalRecord.relevantLabTest">
                </el-input>
              </div>
            </el-scrollbar>
          </div>
          <!--                底部 医生签名、金额-->
          <div class="my-drawer-text">
            <div class="my-drawer-text-son">
              金额: {{ appointmentParticulars.user.suM_MONEY }}
            </div>
          </div>
        </div>


      </el-drawer>
      <!--        新弹框   展示流程图-->
      <el-drawer :visible.sync="chartBut" size="30%">
        <div><span style="color: black;margin-left: 10%; font-size: large; ">检查流程详情图</span>
          <hr style="width: 80%;" />
          <el-timeline :reverse="false" class="timeline">
            <el-timeline-item class="timeLineItem" v-for="(flow, index) in chartData" :key="index" :color="flow.color"
              :timestamp="flow.taskName">
              <span>{{ flow.execution }}</span>
              <div class="dates">
                {{ flow.createDate }}
              </div>
              {{ flow.userName }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-drawer>

      <!--        新弹框   展示流程图-->
      <el-drawer :visible.sync="chartBut" size="30%">
        <div><span style="color: black;margin-left: 10%; font-size: large; ">检查流程详情图</span>
          <hr style="width: 80%;" />
          <el-timeline :reverse="false" class="timeline">
            <el-timeline-item class="timeLineItem" v-for="(flow, index) in chartData" :key="index" :color="flow.color"
              :timestamp="flow.taskName">
              <span>{{ flow.execution }}</span>
              <div class="dates">
                {{ flow.createDate }}
              </div>
              {{ flow.userName }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-drawer>

      <!-- 弹出框 展示打印界面 -->
      <el-drawer :visible.sync="chartPrint" size="60%">
        <div :style="findSize.size2" style="margin-top: 10%;" id="printDiv">
          <span style="color: black; font-size: large;" class="center-text">河南宏力医院住院{{
            printDate.examClass
          }}申请单</span>
          <div style="margin-top: 2%;">
            <!-- <vue-barcode value="11111111" :width="1.5" :height="50"
                style="margin-left: 10%;margin-bottom: -2.9%;"></vue-barcode> -->
            <span style="margin-left: 6%;">住院号：{{ printDate.hospitalizedId }}</span>
            <span style="margin-left: 4%;">病人ID：{{ printDate.patientId }}</span>
            <span style="margin-left: 4%;">申请号：{{ printDate.examNo }}</span>
            <span style="margin-left: 4%;">超声号：</span>
          </div>
          <hr style="width: 95%;" />
          <div style="margin-top: 2%;">
            <span style="margin-left: 6%;">姓名：{{ printDate.patientName }}</span>
            <span style="margin-left: 4%;">性别：{{ printDate.sex }}</span>
            <span style="margin-left: 4%;">年龄：{{ printDate.age }}</span>
            <span style="margin-left: 4%;">科别：{{ printDate.deptName }}</span>
            <span style="margin-left: 4%;">床号：{{ printDate.bedNumber }}</span>
            <span style="margin-left: 4%;">身份：{{ printDate.identity }}</span>
          </div>
          <hr style="width: 95%;" />
          <div style="min-height: 50px;margin-top: 15px;">
            <span style="margin-left: 10%;">症状：{{ printDate.symptom }}</span>
          </div>
          <div style="min-height: 50px;margin-top: 15px; ">
            <span style="margin-left: 10%;word-wrap: break-word;overflow-wrap: break-word;">体征：{{
              printDate.sign
            }}</span>
            <span></span>
          </div>
          <div style="min-height: 50px;margin-top: 15px;">
            <span style="margin-left: 10%;">临床诊断：{{ printDate.clinicalDiagnosis }}</span>
          </div>
          <div style="min-height: 50px;margin-top: 15px;">
            <span style="margin-left: 10%;float: left;">检查项目：{{ printDate.examClassName }}</span>
            <span style="margin-left: 8%;">检查部位：{{ printDate.positionName }}</span>
          </div>
          <div style="min-height: 50px;margin-top: 15px;">
            <span style="margin-left: 10%;">注意事项：{{ printDate.mattersNeedAttention }}</span>
          </div>
          <div style="margin-top: 2%;">
            <span style="margin-left: 6%;">申请医师：{{ printDate.applyDoctorName }}</span>
            <span style="margin-left: 4%;">申请时间：{{ printDate.createDate }}</span>
            <span style="margin-left: 4%;">检查位置：{{ printDate.position }}</span>
          </div>
          <hr style="width: 95%;" />
        </div>
        <el-button type="primary" round @click="printClick" style="margin-left: 80%;">点击打印</el-button>
      </el-drawer>

      <!-- 批量打印 -->
      <el-drawer :visible.sync="chartAllPrint" size="60%">
        <div id="printDiv">
          <div v-for="item in printAllDate" :key="item.id" :style="findSize.size2" style="margin-top: 10%;">
            <span style="color: black; font-size: large;" class="center-text">河南宏力医院住院{{
              item.examClass
            }}申请单</span>
            <div style="margin-top: 2%;">
              <span style="margin-left: 4%;">住院号：{{ item.hospitalizedId }}</span>
              <span style="margin-left: 2%;">病人ID：{{ item.patientId }}</span>
              <span style="margin-left: 2%;">申请号：{{ item.examNo }}</span>
              <span style="margin-left: 2%;">超声号：</span>
            </div>
            <hr style="width: 95%;" />
            <div style="margin-top: 2%;">
              <span style="margin-left: 4%;">姓名：{{ item.patientName }}</span>
              <span style="margin-left: 2%;">性别：{{ item.sex }}</span>
              <span style="margin-left: 2%;">年龄：{{ item.age }}</span>
              <span style="margin-left: 2%;">科别：{{ item.deptName }}</span>
              <span style="margin-left: 2%;">床号：{{ item.bedNumber }}</span>
              <span style="margin-left: 2%;">身份：{{ item.identity }}</span>
            </div>
            <hr style="width: 95%;" />
            <div style="min-height: 50px;margin-top: 15px;">
              <span style="margin-left: 10%;">症状：{{ item.symptom }}</span>
            </div>
            <div style="min-height: 50px;margin-top: 15px; ">
              <span style="margin-left: 10%;word-wrap: break-word;overflow-wrap: break-word;">体征：{{
                item.sign
              }}</span>
              <span></span>
            </div>
            <div style="min-height: 50px;margin-top: 15px;">
              <span style="margin-left: 10%;">临床诊断：{{ item.clinicalDiagnosis }}</span>
            </div>
            <div style="min-height: 50px;margin-top: 15px;">
              <span style="margin-left: 10%;float: left;">检查项目：{{ item.examClassName }}</span>
              <span style="margin-left: 8%;">检查部位：{{ item.positionName }}</span>
            </div>
            <div style="min-height: 50px;margin-top: 15px;">
              <span style="margin-left: 10%;">注意事项：{{ item.mattersNeedAttention }}</span>
            </div>
            <div style="margin-top: 2%;">
              <span style="margin-left: 4%;">申请医师：{{ item.applyDoctorName }}</span>
              <span style="margin-left: 2%;">申请时间：{{ item.createDate }}</span>
              <span style="margin-left: 2%;">检查位置：{{ item.position }}</span>
            </div>
            <hr style="width: 95%;" />
          </div>
        </div>
        <el-button class="pdm-header" type="primary" round @click="printClick" style="margin-left: 80%;">批量打印
        </el-button>
      </el-drawer>
    </div>
    <!--      左侧table展示-->
    <div class="MyTable">
      <div class="TableText">
        {{ tableText }}
      </div>
      <div class="MyForm1">
        <el-form :model="queryForm" size="mini">
          <el-form-item label-width="" label="检查名称:">
            <el-select style="width: 55%" v-model="pageQuery.examClassName" filterable placeholder="请选择"
              @change="getExamSelectTreeList" :clearable="true">
              <el-option v-for="item in examClassNameList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="" label="检查项目:">
            <el-select style="width: 55%" v-model="pageQuery.examSubClassName" filterable placeholder="请选择"
              @change="getExamSelectTreeList" :clearable="true">
              <el-option v-for="item in examSubClassNameList" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label-width="" label="检查方法:">
            <el-select style="width: 55%" v-model="pageQuery.pattern" filterable placeholder="请选择" :clearable="true">
              <el-option v-for="item in patternLists" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="my-table">
        <el-scrollbar style="height: 92%;overflow-x: hidden;">
          <el-row class="my-table-col" v-for="(item, index) in registerListPage" :key="index">
            <el-col :span="6" class="my-table-el-col" :style="findSize.size2">
              <span>{{ item.name }}</span>
            </el-col>
            <el-col :span="9" class="my-table-el-col" :style="findSize.size2">
              <span>{{ item.appointmentDate }}</span>
            </el-col>
            <el-col :span="9" class="my-table-el-col" :style="findSize.size2">
              <span>{{ item.appointmentTime }}</span>
            </el-col>
            <el-col :span="24" v-for="(items, index) in item.projects" :key="index" class="my-table-el-col">
              <span @click="getExamParticulars(item.id)" class="my-table-text" :style="findSize.size2">{{
                items.description }}</span>
            </el-col>
            <div style="margin-left: 70%;">
              <el-tooltip effect="dark" content="详情列表" placement="bottom">
                <el-button icon="el-icon-tickets" type="text" @click="getExamParticulars(item.id)">
                </el-button>
              </el-tooltip>
              <el-tooltip effect="dark" content="批量打印" placement="bottom">
                <el-button icon="el-icon-printer" type="text" @click="printAllClick(item)" class="pringAll"
                  style="margin-left: 10%;"></el-button>
              </el-tooltip>
            </div>
          </el-row>
        </el-scrollbar>
        <div class="my-table-page">
          <!--          <el-pagination-->
          <!--            layout="prev, pager, next"-->
          <!--            :total="total"-->
          <!--            :limit.sync="pageQuery.pageSize"-->
          <!--            :page.sync="pageQuery.pageNum"-->
          <!--            v-show="total > 0"-->
          <!--            @pagination="getExamAppointmentRegisterList">-->

          <!--          </el-pagination>-->
          <pagination v-show="total > 0" :limit.sync="pageQuery.pageSize" :page.sync="pageQuery.pageNum" :total="total"
            :pager-count="5" layout="prev, pager, next" @pagination="getExamAppointmentRegisterList" />
        </div>
      </div>
    </div>

    <!--    中间检查项目-->
    <div class="MyCentre">

      <!--      顶部用户信息展示-->
      <div class="MyUser">
        <div class="My-el-avatar" v-if="userButton">
          <el-avatar :src="manImages" v-if="users.sex === '男'" size="small"></el-avatar>
          <el-avatar :src="woManImages" v-else-if="users.sex === '女'" size="small"></el-avatar>
          <el-avatar :src="manImages" v-else size="small"></el-avatar>
        </div>
        <div class="my-user-text">
          <span class="my-text" style="font-weight: 1000">{{ users.userName }}</span>
          <span class="my-text">/</span>
          <span class="my-text" style="font-weight: 1000">{{ users.sex }}</span>
          <span class="my-text">/</span>
          <span class="my-text" style="font-weight: 1000">{{ users.age }}</span>
          <span class="my-text">/</span>
          <span class="my-text" style="color: #1cbbb4">ID号:</span>
          <span class="my-text" style="color: red">{{ users.patientId }}</span>
          <span class="my-text">/</span>
          <span class="my-text" style="color: #1cbbb4">住院次数:</span>
          <span class="my-text" style="color: #1c84c6">{{ users.visitId }}</span>
          <span class="my-text">/</span>
          <span class="my-text" style="color: #1cbbb4">就诊日期:</span>
          <span class="my-text" style="color: #2c8d54">{{ users.treatmentDate }}</span>

          <!--          <el-tooltip effect="dark" content="医嘱列表" placement="bottom">-->
          <!--            <el-button style="float: right;margin-right: 50px;"-->
          <!--                       icon="el-icon-notebook-2" size="medium" type="text" @click="getAdviceList">-->
          <!--            </el-button>-->
          <!--          </el-tooltip>-->

        </div>
      </div>
      <!--      顶部选择区域-->
      <div class="my-select-area">

        <el-form style="display: flex;justify-content: flex-end;margin-right: 25%;" :style="selectMargen.top" ref="form"
          :model="users" label-width="80px">
          <el-form-item>
            <el-radio v-model="users.fast" label="'1'">急诊</el-radio>
          </el-form-item>
          <el-form-item label="_医保属性" style="margin-left: 2%;">
            <el-select style="width: 70%;" v-model="users.personType" placeholder="请选择">
              <el-option v-for="item in insuranceSelect" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <!--      检查项目树结构展示-->
      <div class="MyTree">
        <div class="my-header-text" :style="findSize.size3" style="text-align: left;margin-left: 2%">检查项目</div>
        <div class="">
          <el-input style="width: 100%" placeholder="输入关键字进行过滤" v-model="treeSearch">
          </el-input>
        </div>
        <div class="" style="height: 95%" :style="findSize.size2">
          <el-scrollbar style="height: 97%;overflow-x: hidden;" :style="findSize.size2">
            <el-tree :style="findSize.size2" :highlight-current="true" :data="examTree" :props="defaultProps"
              node-key="label" :filter-node-method="filterNode" @node-click="getCheckedNodes" ref="tree">
            </el-tree>
          </el-scrollbar>
        </div>
      </div>

      <!--      中间检查项目多选-->
      <div class="MyExam">
        <div class="MyExam1">
          <div class="my-header-text" :style="findSize.size3">检查方法</div>
          <el-scrollbar style="height: 91%;">
            <el-checkbox-group v-model="patternSetList">
              <el-descriptions :column="size.size1" size="small" direction="horizontal" border :contentStyle="CS"
                :label-style="LS">
                <el-descriptions-item v-for="(item, index) in patternList" :key="index" contentStyle="" align="center">
                  <el-checkbox style="float: left;color: #00afff" :label="item.label" @change="getCheckBox(item.label)">
                  </el-checkbox>
                </el-descriptions-item>
              </el-descriptions>
            </el-checkbox-group>
          </el-scrollbar>

        </div>
        <div class="MyExam2">
          <div class="my-header-text" :style="findSize.size3">已选择项目列表</div>
          <div class="label-table" :style="findSize.size3">
            <el-table ref="multipleTable" :data="patternDetailsList" size="mini" :height="tableHeight" border
              style="width: 100%;">
              <el-table-column align="center" label="操作" :width="tables.width1">
                <template slot-scope="scope">
                  <el-button icon="" size="mini" type="text" @click="deleteAppointmentTime(scope.row)">删除
                  </el-button>
                  <el-button icon="" size="mini" type="text" @click="AppointmentTime(scope.row)">预约
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column align="center" show-overflow-tooltip prop="pattern" label="项目名称"
                :width="tables.width2"></el-table-column>
              <el-table-column align="center" prop="appointmentTime" label="时间" :width="tables.width3"></el-table-column>
              <el-table-column align="center" label="部位" :width="tables.width4">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.positionCode" @change="positionSelect(scope.row)" filterable
                    placeholder="请选择">
                    <el-option v-for="item in positionData" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="deptName" :show-overflow-tooltip="true" label="执行科室"
                :width="tables.width5">
                <template slot-scope="scope">
                  <div v-if="scope.row.deptList.length > 1">
                    <el-select v-model="scope.row.deptCode" @change="deptSelect(scope.row)" filterable>
                      <el-option v-for="item in scope.row.deptList" :key="item.value" :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>
                  </div>
                  <div v-else>{{ scope.row.deptName }}</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div class="MyExam3">
          <div class="my-header-text" :style="findSize.size3">注意事项</div>
          <el-scrollbar style="height: 75%;">
            <div v-for="(item, index) in patternDetailsList" :key="item.patternCode">
              <div :style="findSize.size2" v-if="item.attention != null">{{ index + 1 }}： {{ item.pattern }} ->
                {{ item.attention }}</div>
            </div>
          </el-scrollbar>
        </div>
      </div>
      <!--      选择时间 dialog对话框-->
      <div class="">
        <el-dialog :visible.sync="timeButton">
          <div class="my-header-text" :style="findSize.size3">预约时间选择</div>
          <div class="date-my" style="text-align: center">
            <el-date-picker @change="datePicker" v-model="ApplyTimeDate" type="date" format="yyyy-MM-dd"
              placeholder="选择日期">
            </el-date-picker>
          </div>
          <el-scrollbar style="height: 580px;text-align: center">

            <el-radio-group v-model="timeSetList">
              <el-descriptions title="上午: " style="margin-top:3%;margin-bottom: 28px;" :column="size.size2" size="small"
                direction="horizontal" border :contentStyle="CS" :label-style="LS">
                <el-descriptions-item v-for="(item, index) in forenoonTimeList" :key="index" contentStyle=""
                  align="center">
                  <el-radio style="float: left;color: #00afff" :label="item.value" :style="findSize.size2"
                    v-if="item.numbers <= item.sum || !item.status" disabled @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                  <el-radio style="float: left;color: #00afff" :style="findSize.size2" :label="item.value" v-else
                    @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                </el-descriptions-item>
              </el-descriptions>
            </el-radio-group>

            <el-radio-group v-model="timeSetList">
              <el-descriptions title="下午: " :style="findSize.size2" style="margin-bottom: 28px;" :column="size.size2"
                size="small" direction="horizontal" border :contentStyle="CS" :label-style="LS">
                <el-descriptions-item v-for="(item, index) in afternoonTimeList" :key="index" contentStyle=""
                  align="center">
                  <el-radio style="float: left;color: #00afff" :style="findSize.size2" :label="item.value"
                    v-if="item.numbers <= item.sum || !item.status" disabled @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                  <el-radio :style="findSize.size2" style="float: left;color: #00afff" :label="item.value" v-else
                    @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                </el-descriptions-item>
              </el-descriptions>
            </el-radio-group>

            <el-radio-group v-if="querySubmit.type === '2'" v-model="timeSetList">
              <el-descriptions title="凌晨: " :style="findSize.size2" style="margin-bottom: 28px;" :column="size.size2"
                size="small" direction="horizontal" border :contentStyle="CS" :label-style="LS">
                <el-descriptions-item v-for="(item, index) in weeHoursTimeList" :key="index" contentStyle=""
                  align="center">
                  <el-radio style="float: left;color: #00afff" :style="findSize.size2" :label="item.value"
                    v-if="item.numbers <= item.sum || !item.status" disabled @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                  <el-radio :style="findSize.size2" style="float: left;color: #00afff" :label="item.value" v-else
                    @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                </el-descriptions-item>
              </el-descriptions>
            </el-radio-group>

            <el-radio-group v-if="querySubmit.type === '2'" v-model="timeSetList">
              <el-descriptions title="早晨: " :style="findSize.size2" style="margin-bottom: 28px;" :column="size.size2"
                size="small" direction="horizontal" border :contentStyle="CS" :label-style="LS">
                <el-descriptions-item v-for="(item, index) in morningTimeList" :key="index" contentStyle=""
                  align="center">
                  <el-radio style="float: left;color: #00afff" :style="findSize.size2" :label="item.value"
                    v-if="item.numbers <= item.sum || !item.status" disabled @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                  <el-radio :style="findSize.size2" style="float: left;color: #00afff" :label="item.value" v-else
                    @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                </el-descriptions-item>
              </el-descriptions>
            </el-radio-group>

            <el-radio-group v-if="querySubmit.type === '2'" v-model="timeSetList">
              <el-descriptions title="中午: " :style="findSize.size2" style="margin-bottom: 28px;" :column="size.size2"
                size="small" direction="horizontal" border :contentStyle="CS" :label-style="LS">
                <el-descriptions-item v-for="(item, index) in noonTimeList" :key="index" contentStyle="" align="center">
                  <el-radio style="float: left;color: #00afff" :style="findSize.size2" :label="item.value"
                    v-if="item.numbers <= item.sum || !item.status" disabled @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                  <el-radio :style="findSize.size2" style="float: left;color: #00afff" :label="item.value" v-else
                    @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                </el-descriptions-item>
              </el-descriptions>
            </el-radio-group>

            <el-radio-group v-if="querySubmit.type === '2'" v-model="timeSetList">
              <el-descriptions title="傍晚: " :style="findSize.size2" style="margin-bottom: 28px;" :column="size.size2"
                size="small" direction="horizontal" border :contentStyle="CS" :label-style="LS">
                <el-descriptions-item v-for="(item, index) in atDuskTimeList" :key="index" contentStyle="" align="center">
                  <el-radio style="float: left;color: #00afff" :style="findSize.size2" :label="item.value"
                    v-if="item.numbers <= item.sum || !item.status" disabled @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                  <el-radio :style="findSize.size2" style="float: left;color: #00afff" :label="item.value" v-else
                    @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                </el-descriptions-item>
              </el-descriptions>
            </el-radio-group>

            <el-radio-group v-if="querySubmit.type === '2'" v-model="timeSetList">
              <el-descriptions title="晚上: " :style="findSize.size2" style="margin-bottom: 28px;" :column="size.size2"
                size="small" direction="horizontal" border :contentStyle="CS" :label-style="LS">
                <el-descriptions-item v-for="(item, index) in nightTimeList" :key="index" contentStyle="" align="center">
                  <el-radio style="float: left;color: #00afff" :style="findSize.size2" :label="item.value"
                    v-if="item.numbers <= item.sum || !item.status" disabled @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                  <el-radio :style="findSize.size2" style="float: left;color: #00afff" :label="item.value" v-else
                    @change="setAppointmentTime(item.label)">
                    {{ item.label }}
                    <span :style="findSize.size2" style="color: red" v-if="(item.numbers / 1.2) < item.sum">[{{
                      item.numbers
                    }}][{{ item.sum }}]</span>
                    <span :style="findSize.size2" v-else>[{{ item.numbers }}][{{ item.sum }}]</span>
                  </el-radio>
                </el-descriptions-item>
              </el-descriptions>
            </el-radio-group>
          </el-scrollbar>
        </el-dialog>
      </div>

      <!--      右侧申请主诉-->
      <div class="MySketch">
        <div class="my-header-text" :style="findSize.size3">申请单内容
          <el-tooltip effect="dark" content="病历列表" placement="bottom">
            <el-button style="margin-right: -20px" icon="el-icon-s-claim" size="medium" type="text"
              @click="getCseHistoryList">
            </el-button>
          </el-tooltip>
        </div>
        <el-alert type="info" description="上方申请单内容旁的按钮,可快速修改患者门诊病历信息" show-icon>
        </el-alert>
        <el-scrollbar style="height: 86.5%;">
          <span class="my-sketch-span">1·检查目的</span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="users.inspectionPurpose">
            </el-input>
          </div>
          <span class="my-sketch-span">2·主诉<i style="color: red" :style="findSize.size3">*</i></span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="users.illnessDesc">
            </el-input>
          </div>

          <span class="my-sketch-span">3·现病史<i style="color: red" :style="findSize.size3">*</i></span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="users.medHistory">
            </el-input>
          </div>
          <span class="my-sketch-span">4·体检<i style="color: red" :style="findSize.size3">*</i></span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="users.bodyExam">
            </el-input>
          </div>
          <span class="my-sketch-span">5·过去检查</span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="users.formerlyExam">
            </el-input>
          </div>
          <span class="my-sketch-span">6·临床诊断<i style="color: red" :style="findSize.size3">*</i></span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="users.diagDesc">
            </el-input>
          </div>
          <span class="my-sketch-span">7·其他诊断</span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="users.relevantDiag">
            </el-input>
          </div>
          <span class="my-sketch-span">8·相关化验结果</span>
          <div class="my-sketch-input">
            <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="users.relevantLabTest">
            </el-input>
          </div>
        </el-scrollbar>
      </div>
      <!--      最底部-->
      <div class="my-bottom">
        <div class="mu-bottom-text" v-if="bottomButton">
          金额: {{ money }}
          <el-button class="my-bottom-button" type="success" size="mini" @click="submitVerify">发送</el-button>
        </div>
      </div>

      <!--      医嘱dialog 弹窗列表-->
      <div class="my-dialog">
        <el-drawer :visible.sync="adviceButton" title="医嘱列表" size="50%" style="min-height: 700px;">
          <el-table ref="multipleTable2" :style="findSize.size1" :data="adviceList" size="mini" border
            style="width: 100%;" height="650">
            <el-table-column align="center" prop="ordeR_NO" label="编号" width="40"></el-table-column>
            <el-table-column align="center" prop="ordeR_TEXT" label="医嘱名称"></el-table-column>
            <el-table-column align="center" prop="ordeR_CLASS" label="状态" width="70"></el-table-column>
            <el-table-column align="center" prop="starT_DATE_TIME" label="开单日期" width="140"></el-table-column>
          </el-table>
          <el-alert title="注意：" type="info" description="此处不可修改医嘱任何信息,只提供查询,如需要操作,请到对应功能处操作!!!" show-icon>
          </el-alert>
        </el-drawer>
      </div>
      <!--      门诊病历dialog 弹窗列表-->
      <div class="my-dialog">
        <el-dialog title="病历选项" :visible.sync="cseHistoryButton" width="60%">
          <div class="my-dialog-cseHistory">
            <el-scrollbar style="height: 550px; width: 55%; overflow-x: hidden;">
              <el-menu class="el-menu-vertical-demo" style="width: 100%;" v-for="item in cseHistoryList"
                :key="item.visitId">
                <el-menu-item @click="cseHistoryClick(item)" :index="item.visitId.toString()">
                  <div class="el-menu-vertical-demo-item">
                    <span>{{ item.treatmentDate }}</span>
                    <span>{{ item.deptName }}</span>
                    <span>{{ item.doctorName }}</span>
                  </div>
                </el-menu-item>
              </el-menu>
            </el-scrollbar>
            <div style="width: 50%;">
              <span class="my-sketch-span">1·主诉</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="6" v-model="cseHistory.illnessDesc">
                </el-input>
              </div>

              <span class="my-sketch-span">2·现病史</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="6" v-model="cseHistory.medHistory">
                </el-input>
              </div>
              <span class="my-sketch-span">3·体检</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="6" v-model="cseHistory.bodyExam">
                </el-input>
              </div>
              <span class="my-sketch-span">4·临床诊断</span>
              <div class="my-sketch-input">
                <el-input disabled type="textarea" :rows="6" v-model="cseHistory.diagDesc">
                </el-input>
              </div>
            </div>
          </div>
          <div class="my-dialog-cseHistory-button">
            <el-button type="success" @click="cseHistoryButtonSucess">确定</el-button>
            <el-button type="danger" @click="cseHistoryButton = false"> 取消</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>


<script>
import {
  GetExamPositionItems,
  GetExamTree,
  GetExamProjectParticularsList,
  GetExamPatternParticulars,
  SaveExamAppointmentRegister,
  GetExamAppointmentRegisterList,
  GetExamSelectTreeList,
  GetExamParticularsById,
  GetExamTimeDictNumber,
  GetExamFlowChart,
  GetExamHospitalizedPrint,
  GetExamHospitalizedPrintAll,
  Chargeback,
  GetAdviceList,
  GetCseHistoryList,
} from "@/api/appointment/register";
import man from "@/assets/icons/svg/man.png";
import woMan from "@/assets/icons/svg/woman.png";
// 条码
// import VueBarcode from 'vue-barcode';
// 打印
import print from 'print-js'

export default {
  directives: {
    print
  },
  name: "register",
  // components: {
  //   VueBarcode
  // },
  data() {
    return {
      positionBut: false,
      total: 0,
      vCs: {},
      chartData: [],
      chartBut: false,
      chartPrint: false,
      chartAllPrint: false,
      tableHeight: "180px",
      CS: {
        'text-align': 'center', //文本居中
        'word-break': 'break-all', //过长时自动换行
        'font-weight': '400',
        'font-size': '16px',
        'float': 'left',
        'width': '100%',
      },
      LS: {
        color: '#000',
        'text-align': 'center',
        'font-weight': '0',
        'font-size': '0px',
        'min-width': '-100px',
        'word-break': 'keep-all',
      },
      userDialogButton: false,
      bottomButton: false,
      TreeTrue: true,
      screenWidth: 0,
      screenHeight: '800px',
      manImages: man,
      numberNo: 0,
      woManImages: woMan,
      tableText: '预约检查记录',
      begetterExamName: '',
      sonExamName: '',
      ApplyTimeDate: '',
      rowNo: 0,
      timeSetList: '',
      timeButton: false,
      forenoonTimeList: [],
      afternoonTimeList: [],
      weeHoursTimeList: [],
      morningTimeList: [],
      noonTimeList: [],
      atDuskTimeList: [],
      nightTimeList: [],
      examTreeTimeList: [],
      examTree: [],
      // examTree: [
      //   {
      //     label: '内科楼',
      //     value: '内科楼',
      //     children: [{
      //       label: '超声科',
      //       value: '超声科',
      //       children: [{
      //         label: '超声多普勒胎音仪',
      //         value: '超声多普勒胎音仪',
      //         children: [{
      //           label: '头颈甲乳',
      //           value: '头颈甲乳',
      //           children: [{
      //             label: '甲状腺+颈部淋巴结彩超检查',
      //             value: '甲状腺+颈部淋巴结彩超检查',
      //           },{
      //             label: '腮腺+颈部淋巴结彩超检查',
      //             value: '腮腺+颈部淋巴结彩超检查',
      //           },{
      //             label: '颌下腺+颈部淋巴结彩超检查',
      //             value: '颌下腺+颈部淋巴结彩超检查',
      //           },],
      //         },],
      //       },{
      //         label: '彩色超声诊断系统',
      //         value: '彩色超声诊断系统',
      //         children: [{
      //           label: '妇产四维',
      //           value: '妇产四维',
      //           children: [{
      //             label: '四维彩超检查（三胞胎）',
      //             value: '四维彩超检查（三胞胎）',
      //           },{
      //             label: '卵泡监测（经阴）',
      //             value: '卵泡监测（经阴）',
      //           },{
      //             label: '同期卵泡监测每加一次（经阴）',
      //             value: '同期卵泡监测每加一次（经阴）',
      //           },],
      //         },],
      //       },],
      //     },{
      //       label: '1-6病区',
      //       value: '1-6病区',
      //       children: [{}],
      //     },{
      //       label: '电生理科',
      //       value: '电生理科',
      //       children: [{}],
      //     },{
      //       label: '病理科',
      //       value: '病理科',
      //       children: [{}],
      //     },{
      //       label: '医学影像科',
      //       value: '医学影像科',
      //       children: [{}],
      //     },],
      //   },
      //   {
      //     label: '外科楼',
      //     value: '外科楼',
      //     children: [{
      //       value:'超声科',
      //       label: '超声科',
      //       children: [{}]
      //     },
      //
      //     ],
      //   },
      // ],
      options: [],
      money: undefined,
      bufferPatternList: [],
      patternList: [],
      patternSetList: [],
      queryPattern: [],
      patternDetailsList: [],
      userButton: false,
      treeSearch: '',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      users: {
        userName: '',
        sex: '',
        age: '',
        patientId: '',
        bed: '',
        treatmentDate: '',
        visitId: '',
        costType: '',
        diagnose: '',
        fast: undefined,
        personType: '门诊普通',
      },
      queryForm: {
        examClassName: '',
        examSubClassName: '',
        description: '',
      },
      querySubmit: {
        users: undefined,
        patternDetails: undefined,
        visitNo: undefined,
        sumMoney: undefined,
        type: undefined,
        attention: undefined,
      },
      query: {
        patientId: '',
        visitId: '',
        empNo: '',
        data: [],
      },
      registerListPage: [],
      pageQuery: {
        pageNum: 1,
        pageSize: 10,
        patientId: '',
        examClassName: '',
        examSubClassName: '',
        pattern: ''
      },
      examClassNameList: [],
      examSubClassNameList: [],
      patternLists: [],
      appointmentParticulars: {
        user: [],
        exams: [],
        medicalRecord: [],
      },
      positionData: [],
      bodyHeight: '',
      tables: {
        width1: '',
        width2: '',
        width3: '',
        width4: '',
        width5: '',
      },
      findSize: {
        size1: undefined,
        size2: undefined,
        size3: undefined,
      },
      size: {
        size1: undefined,
        size2: undefined,
      },
      selectMargen: {
        top: '',
      },
      printDate: {},
      printAllDate: [],
      datePiclerData: {},
      sonId: '',
      type: '',
      patientId: '',
      visitId: '',
      adviceList: [],
      adviceButton: false,
      cseHistoryList: [],
      cseHistoryButton: false,
      cseHistory: {},
      insuranceSelect: [{
        value: '门诊普通',
        label: '门诊普通'
      }, {
        value: '居民统筹',
        label: '居民统筹'
      }, {
        value: '职工统筹',
        label: '职工统筹'
      }, {
        value: '门诊重大',
        label: '门诊重大'
      }],
    }
  },
  methods: {
    //病历确定事件
    cseHistoryButtonSucess() {
      let cas = this.cseHistory;
      this.users.illnessDesc = undefined;
      this.users.medHistory = cas.medHistory;
      this.users.bodyExam = cas.bodyExam;
      this.users.diagDesc = cas.diagDesc;
      this.cseHistory = {};
      this.cseHistoryButton = false;
    },
    //病历单词点击事件
    cseHistoryClick(item) {
      this.cseHistory = item;
    },
    //获取门诊病历列表
    getCseHistoryList() {
      GetCseHistoryList(this.patientId, this.type).then(res => {
        this.cseHistoryList = res.data;
        this.cseHistoryButton = true;
        let us = this.users;
        this.cseHistory.illnessDesc = us.illnessDesc;
        this.cseHistory.medHistory = us.medHistory;
        this.cseHistory.bodyExam = us.bodyExam;
        this.cseHistory.diagDesc = us.diagDesc;
      })
    },
    //医嘱列表
    getAdviceList() {
      GetAdviceList(this.patientId, this.visitId, this.type).then(res => {
        this.adviceList = res.data;
        this.adviceButton = true;
      })
    },
    //医嘱作废
    chargeback(row) {
      this.$confirm('确定要作废当前《' + row.description + '》检查项目吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        Chargeback(row.id, this.querySubmit.type).then(res => {
          this.$message({
            type: 'success',
            message: res.message
          });
          this.getExamParticularsById(this.sonId);
          this.getExamAppointmentRegisterList();
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });

    },
    //预约事件处理
    datePicker() {
      let a = this.ApplyTimeDate;
      this.ApplyTimeDate = a.toLocaleDateString().toString();
      this.AppointmentTime(this.datePiclerData);
    },
    //部位处理
    positionSelect(row) {
      let positionCode = row.positionCode;
      let positionData = this.positionData;
      positionData.forEach(function (item, index) {
        if (positionCode === item.value) {
          row.positionName = item.label;
          return;
        }
      })
    },
    //执行科室处理
    deptSelect(row) {
      let deptCode = row.deptCode;
      let deptData = row.deptList;
      deptData.forEach(function (item, index) {
        if (deptCode === item.value) {
          row.deptName = item.label;
          return;
        }
      })
    },
    //获取检查部位下拉选
    getPositionTree() {
      GetExamPositionItems().then(res => {
        this.positionData = res.data;
      })
    },
    //获取检查项目下拉树结构
    getTree() {
      GetExamTree(this.query).then(res => {
        let user = res.data.user;
        this.examTree = res.data.treeList;
        this.users = user;
        this.users.personType = "门诊普通";
        this.userButton = true;
        if (user.illnessDesc === null || user.medHistory === null || user.bodyExam === null || user.diagDesc === null) {
          this.$alert(`申请单内容自动提取异常，请点击确定后手动选择：现病史、体检、诊断等内容`, '数据异常提示', {
            confirmButtonText: '确定',
            callback: action => {
              this.getCseHistoryList();
            }
          });
        }
      })
    },
    //左侧检查项目 树结构点击 回调
    getCheckedNodes() {
      this.begetterExamName = '';
      this.sonExamName = '';
      //1·获取点击节点详情
      let v = this.$refs.tree.getCurrentNode();
      // 2·判断节点点击是否为空
      if (v !== null) {
        let begetterExamName = v.value;
        let sonExamName = v.label
        if (begetterExamName !== null && sonExamName !== null) {
          // 3·如果两者都不为null证明是子节点点击    如果是父节点点击，什么都不做
          this.begetterExamName = begetterExamName;
          this.sonExamName = sonExamName;
          //  4·接口获取检查项目信息
          GetExamProjectParticularsList(this.begetterExamName, this.sonExamName).then(res => {
            this.patternList = res.data;
          })
        }
      }
    },
    // Todo 如有需要  增加删除提示，
    //删除已选择项目列表
    deleteAppointmentTime(row) {
      /**
       * 1·根据 删除按钮 传递的row  删除指定数据
       */
      let c = this.patternDetailsList;
      let index1 = undefined;
      let index2 = undefined;
      //记录 相等的 item 下标
      c.forEach(function (item, index) {
        if (row.no === item.no) {
          index1 = index;
        }
      })
      //如果 记录的下标符合条件  进行数据处理
      if (index1 !== undefined) {
        //删除指定数据
        delete c[index1];
        //数据判空
        let nweArray = [];
        c.forEach(function (item, index) {
          if (item !== null) {
            nweArray.push(item)
          }
        })
        this.patternDetailsList = nweArray;
        this.bufferPatternList = nweArray;
        //2·处理玩 数据后  处理多选框的数据
        let patternSetList1 = [];
        let patternSetList2 = this.patternSetList;
        patternSetList2.forEach(function (item, index) {
          if (item === row.pattern) {
            index2 = index;
          }
        })
        if (index2 !== undefined) {
          delete patternSetList2[index2]
        }
        //如果不重新赋值   数据不完整
        patternSetList2.forEach(function (item, index) {
          if (item !== null) {
            patternSetList1.push(item)
          }
        })
        this.patternSetList = patternSetList1;

        //3· 删除查询参数 列表
        let index3 = undefined
        let queryPattern1 = [];
        let queryPattern2 = this.queryPattern;
        queryPattern2.forEach(function (item, index) {
          if (item.no === row.no) {
            index3 = index;
          }
        })
        //删除
        if (index3 !== undefined) {
          delete queryPattern2[index3]
        }
        //如果不重新赋值   数据不完整
        queryPattern2.forEach(function (item, index) {
          if (item !== null) {
            queryPattern1.push(item);
          }
        })
        this.queryPattern = queryPattern1;
      }
      this.tableDataProcessing();
      this.$nextTick(() => {
        this.$refs['multipleTable'].doLayout();
      })
    },
    //点击预约时间处理类
    AppointmentTime(row) {
      this.datePiclerData = row;
      GetExamTimeDictNumber(row.begetterExamName, row.sonExamName, this.ApplyTimeDate).then(res => {
        this.forenoonTimeList = res.data.forenoon;
        this.afternoonTimeList = res.data.afternoon;
        this.weeHoursTimeList = res.data.weeHours;
        this.morningTimeList = res.data.morning;
        this.noonTimeList = res.data.noon;
        this.atDuskTimeList = res.data.atDusk;
        this.nightTimeList = res.data.night;
      })
      // 打开 时间选择弹出框
      this.timeButton = true;
      //缓存 本条数据的 编号，选择时间后 赋值使用
      this.rowNo = row.no;
      // 如果本条数据有 时间相关数据  进行回显
      this.timeSetList = row.appointmentTime;
    },
    //选择预约时间后的回调
    setAppointmentTime: function (label) {
      /**
       * 选择时间后
       * 1·对指定表单数据 赋值
       * 2·buffer缓存本次数据，下次新点击报告如果有新增的话 将原有的时间  放入指定的 数据中
       */
      let date = this.ApplyTimeDate;
      let no = this.rowNo;
      let c = [];
      //判断no 是否一致  如果一致 将选择的时间放入 时间字段中
      this.patternDetailsList.forEach(function (item, index) {
        if (item.no === no) {
          item.appointmentTime = label;
          item.appointmentDate = date;
        }
        c.push(item)
      })
      let bo = false;
      //判断 选择项目中有无冲突时间，如果有，进行提醒
      c.forEach(function (item, index) {
        if (item.no !== no) {
          if (item.appointmentTime !== item.appointmentTime !== undefined || item.appointmentTime !== "") {
            if (item.appointmentTime === label) {
              bo = true;
            }
          }
        }
      })
      if (bo) {
        this.$message({
          message: "警告: " + label + "当前时间点已有预约项目,可能会检查冲突,请仔细核查!",
          type: 'warning',
          duration: 10000,
          showClose: true,
        });
      }
      //将处理的结果重新赋值给 表单绑定数据
      this.patternDetailsList = c;
      //将处理的结果 放入缓冲集合中  下次新点击报告如果有新增的话 将原有的时间  放入指定的 数据中
      this.bufferPatternList = c;
      //关闭弹出框
      this.timeButton = false;
      //单选框绑定值清楚
      this.timeSetList = '';

      //动态刷新 table表格数据
      this.$nextTick(() => {
        this.$refs['multipleTable'].doLayout();
      })
    },
    //table 数据处理类
    tableDataProcessing() {
      /**
       * 1·将 table现有数据 和 buffer缓冲数据 进行比对
       * 2·比对成功后  现有table进行 appointmentTime 赋值操作
       */
      let buffer = this.bufferPatternList;
      if (buffer !== null) {
        //如果缓冲区不等null   进行数据配对
        let c = [];
        let patternDetailsList = this.patternDetailsList;
        if (patternDetailsList !== null) {
          patternDetailsList.forEach(function (item, index) {
            let array = buffer.filter(t => item.no === t.no);
            if (array.length > 0) {
              item.appointmentTime = array[0].appointmentTime;
              item.positionName = array[0].positionName;
              item.positionCode = array[0].positionCode;
            }
            c.push(item)
          })
        }

        this.bufferPatternList = c;
        this.patternDetailsList = c;
        this.$nextTick(() => {
          this.$refs['multipleTable'].doLayout();
        })
      }
      let money = 0;
      if (this.patternDetailsList.length > 0) {
        this.patternDetailsList.forEach(function (item, index) {
          money += item.money
        })
        this.bottomButton = true;
      } else {
        money = 0;
        this.bottomButton = false;
      }

      this.money = money;
    },
    //3· 多选框点击事件
    getCheckBox(label) {
      // 1·维护集合
      let ca = {
        no: ++this.numberNo,
        begetterExamName: this.begetterExamName,
        sonExamName: this.sonExamName,
        pattern: label,
      };
      //2·获取 多选框绑定值，已方便计算手动维护数据那些有，那些没有
      let patternSetList = this.patternSetList;
      // 查询参数维护集合
      let patternList = this.queryPattern;
      // 下标集合，方便记录多选框中没有的下标值 最终数据处理
      let noIndex = [];
      patternList.push(ca);
      //3·自定义 集合循环判断  多选框patternSetList中是否包含本次patternList中数据
      //如果没有 noIndex 中记录下标
      patternList.forEach(function (item, index) {
        if (patternSetList.includes(item.pattern)) {
        } else {
          noIndex.push(index)
        }
      })
      // 4·如果下标集合中 有负责条件的进行处理
      // 集合到排序后  patternList删除下标不符合的集合
      if (noIndex.length > 0) {
        noIndex.sort().reverse();
        noIndex.forEach(function (item, index) {
          delete patternList[item]
        })
      }
      //5·数据处理完毕后，创建新集合
      //重新赋值给 查询集合 this.queryPattern
      let patternListTwo = [];
      patternList.forEach(function (item, index) {
        if (item !== null) {
          patternListTwo.push(item)
        }
      })
      //6· 重新赋值
      this.queryPattern = patternListTwo;
      //7· 查询数据
      this.getExamPatternParticulars();
    },
    //获取 检查项目详情信息（金额、注意事项等。）
    getExamPatternParticulars() {
      this.query.data = this.queryPattern;
      GetExamPatternParticulars(this.query).then(res => {
        this.patternDetailsList = res.data;
        //数据比对
        this.tableDataProcessing();
      })
    },
    //数据提交按钮
    submitVerify() {
      let user = this.users;
      if (user.illnessDesc === null || user.medHistory === null || user.bodyExam === null || user.diagDesc === null) {
        this.$confirm('申请单内容不完善，是否要继续？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.submitExamApp();
        }).catch(() => {
          this.getCseHistoryList();
        });
      } else {
        this.submitExamApp();
      }
    },
    submitExamApp() {
      const loading = this.$loading({
        lock: true,
        text: '休息一下,数据正在保存中(●' + '◡' + '●)',
        spinner: 'el-icon-coffee-cup',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      let ta = this.patternDetailsList;
      let attention = '';
      ta.forEach(function (item, index) {
        if (item.attention != null) {
          attention += ((index + 1) + "：  " + item.pattern + "  ->  " + item.attention + ',');
        }
      })
      this.querySubmit = {
        users: this.users,
        patternDetails: this.patternDetailsList,
        sumMoney: this.money,
        attention: attention,
      }
      this.querySubmit.type = this.$route.query && this.$route.query.type;
      SaveExamAppointmentRegister(this.querySubmit).then(res => {
        if (res.code === 200) {
          this.$message.success(res.message)
          this.getExamAppointmentRegisterList();
          this.bufferPatternList = [];
          this.patternDetailsList = [];
          this.patternSetList = [];
          this.patternList = [];
          loading.close();
        }
      }).catch(res => {
        loading.close()
      })
    },
    //树结构筛选
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    //获取预约检查记录分页信息
    getExamAppointmentRegisterList() {
      GetExamAppointmentRegisterList(this.pageQuery).then(res => {
        this.registerListPage = res.data.data;
        this.total = res.data.total;
      })
    },
    //获取预约查询 下拉书选择详情
    getExamSelectTreeList() {
      this.getExamAppointmentRegisterList();
      GetExamSelectTreeList(this.pageQuery).then(res => {
        this.patternLists = res.data.projects;
        this.examClassNameList = res.data.classNames;
        this.examSubClassNameList = res.data.subClassNames;
      })
    },
    //预约记录dialog对话框
    getExamParticulars(id) {

      this.getExamParticularsById(id);
    },
    //获取预约记录详情细腻
    getExamParticularsById(id) {
      this.sonId = id;
      GetExamParticularsById(id).then(res => {
        this.appointmentParticulars = {
          user: res.data.user,
          exams: res.data.exams,
          medicalRecord: res.data.medicalRecord,
        }
        console.log(this.appointmentParticulars);
        this.userDialogButton = true;
      }).catch(() => {
        this.userDialogButton = false;
      })
    },
    //查询重置
    pageResults() {
      this.pageQuery = {
        pageNum: 1,
        pageSize: 10,
        examClassName: '',
        examSubClassName: '',
        pattern: ''
      }
      this.getExamSelectTreeList();
      this.getExamAppointmentRegisterList();
    },
    timeCreate() {
      let date = new Date();
      this.ApplyTimeDate = date.toLocaleDateString().toString();
    },
    //流程图回调
    chart(id) {
      GetExamFlowChart(id).then(res => {
        this.chartData = res.data;
        this.chartBut = true;
      })
    },
    // 打印按钮操作
    handlePrint(row) {
      GetExamHospitalizedPrint(row.id, this.querySubmit.type).then(res => {
        this.printDate = res.data;
      })
      this.chartPrint = true;
    },
    // 批量打印操作
    printAllClick(item) {
      const id = item.id;
      GetExamHospitalizedPrintAll(id, this.querySubmit.type).then(res => {
        this.printAllDate = res.data;
      })
      this.chartAllPrint = true;
    },
    // 打印
    printClick() {
      const style = '@page {margin:0 10mm};'//打印时去掉眉页眉尾
      printJS({
        printable: 'printDiv',
        type: 'html',
        header: '',
        targetStyles: ['*'],
        style,
        scanStyle: false,
      });
    },
  },
  mounted() {
    this.$nextTick(() => {
      const bodyStyle = document.body.style, // 获取body节点样式
        htmlStyle = document.getElementsByTagName("html")[0].style, // 获取html节点样式
        docEl = document.documentElement,
        docBody = document.body,
        winWidth = docEl.clientWidth || docBody.clientWidth, // 获取浏览器的宽度
        WinHeight = docEl.clientHeight || docBody.clientHeight; // 获取浏览器的高
      this.bodyHeight = "height:" + WinHeight + "px";
      bodyStyle.minWidth = "1024px";
      bodyStyle.minHeight = "768px";
      htmlStyle.minHeight = "768px";
      htmlStyle.minWidth = "1024px";
      this.tables = {
        width1: winWidth / 20,
        width2: winWidth / 8.5,
        width3: winWidth / 17,
        width4: winWidth / 12.8,
        width5: winWidth / 9.3,
      }
      if (winWidth <= 1240) {
        this.tableHeight = '170px'
        this.size = {
          size1: 1,
          size2: 2
        }
        this.selectMargen.top = 'margin-top: -5px;'
        this.findSize = {
          size1: 'font-size: 10px !important;',
          size2: 'font-size: 12px !important;',
          size3: 'font-size: 14px !important;',
        }
      } else if (winWidth <= 1500) {
        this.tableHeight = '215px'
        this.size = {
          size1: 1,
          size2: 3
        }
        this.selectMargen.top = 'margin-top: -2px;'
        this.findSize = {
          size1: 'font-size: 12px !important;',
          size2: 'font-size: 14px !important;',
          size3: 'font-size: 16px !important;',
        }
      } else {
        this.tableHeight = '215px'
        this.size = {
          size1: 2,
          size2: 4
        }
        this.selectMargen.top = 'margin-top: -1px;'
        this.findSize = {
          size1: 'font-size: 14px !important;',
          size2: 'font-size: 16px !important;',
          size3: 'font-size: 18px !important;',
        }
      }
    });
  },
  watch: {
    treeSearch(val) {
      this.$refs.tree.filter(val);
    },

  },
  computed: {
    scrollerHeight: function () {
      return (window.innerHeight - 250) + 'px';
    }
  },
  created() {
    this.query = {
      patientId: this.$route.query && this.$route.query.patientId,
      visitId: this.$route.query && this.$route.query.visitId,
      empNo: this.$route.query && this.$route.query.empNo,
      visitNo: this.$route.query && this.$route.query.visitNo,
    };
    this.pageQuery.patientId = this.query.patientId;
    this.type = this.$route.query && this.$route.query.type;
    this.patientId = this.$route.query && this.$route.query.patientId;
    this.visitId = this.$route.query && this.$route.query.visitId;
    this.querySubmit.type = this.$route.query && this.$route.query.type;
    this.querySubmit.visitNo = this.$route.query && this.$route.query.visitNo;
    this.timeCreate();
    this.getExamSelectTreeList();
    this.getExamAppointmentRegisterList();
    this.getTree();
    this.getPositionTree();

    // this.query.patientId = this.$route.query && this.$route.query.patientId;
    // this.query.visitId = this.$route.query && this.$route.query.visitId;
    // this.query.empNo = this.$route.query && this.$route.query.empNo;
  },
}
</script>

<style scoped>
.home {
  padding: 0;
  border: 2px solid #b0b1b0;
  min-width: 1014px;
  min-height: 768px;
  position: relative;
  margin: 0 auto;
}

.home::after {
  content: "";
  clear: both;
  display: block;
}

.my-dialog {}

.TableText {
  background-color: #1c84c6;
  color: #f2f2f2;
  height: 3%;
  font-size: 16px;
  margin: 0 auto;
  text-align: center;
}

.MyTable {
  width: 18%;
  border: 1px solid saddlebrown;
  float: left;
  height: 99%;
}

.my-table-col {
  border: 1px solid #00a19b;
  float: left;
  width: 100%;
}

my-advice-button {
  float: right;
  margin-right: 30px;
}

.my-table-text {
  font-size: 12px;
  color: #3A5FCD;
  font-family: PingFang SC;
}

.my-table-text:hover {
  color: #1e47a1 !important;
  border-bottom: 1px solid #1e47a1;
  cursor: pointer;
}

.my-table-el-col {
  font-size: 12px;
}

.my-table-page {
  /*width: 60%;*/
}

.my-table-button {
  margin-left: 80%;
  margin-top: -13%;
  float: right;
  margin-right: 3%;
  position: relative;
}

.MyForm1 {
  margin-top: 2%;
  margin-left: 5%;
  margin-bottom: 10px;
  height: 11%;
}


.my-table {
  margin-left: 1%;
  border: 1px solid #00afff;
  height: 84%;
}

::v-deep .el-form-item--mini.el-form-item {
  margin-bottom: 2px;
}

/deep/ .el-input--mini .el-input__inner {
  height: 26px;
  line-height: 28px;
}

::v-deep .el-input {
  width: 70%;
}

/deep/ .el-checkbox {
  font-size: 10px !important;
}

::v-deep .el-form-item__label {
  font-size: 12px;
  padding: 0px 5px 0 0;
}

.MyCentre {
  float: left;
  width: 82%;
  height: 99.5%;
}


.MyUser {
  border: 1px solid slateblue;
  width: 99%;
  height: 5.5%;
  margin-top: 0.2%;
}

.My-el-avatar {
  margin-left: 1%;
  margin-outside: 1%;
  margin-top: 0.1%;
  float: left;
}

.my-user-text {
  margin-top: 0.5%;
}

.my-text {
  margin-left: 1%;
}

.MyTree {
  margin-left: 1%;
  margin-top: 0.5%;
  border: 1px solid darkslateblue;
  width: 20%;
  height: 88%;
  float: left;
}

.MyExam {
  margin-top: 0.5%;
  float: left;
  border: 1px solid midnightblue;
  margin-left: 1%;
  width: 49%;
  height: 82%;
}

.my-header-text {
  border: 1px solid #F9F9FA;
  background-color: #F9F9FA;
  text-align: center;
  font-size: 14px;
  letter-spacing: 10px;
  color: #1b2947;
  font-weight: bolder;
}

.MyExam1 {
  height: 48%;
  width: 100%;
  border: 1px solid black;

}

.MyExam2 {
  height: 32%;
  width: 100%;
  border: 1px solid black;
}

.MyExam3 {
  height: 20%;
  width: 100%;
  border: 1px solid black;
}

.my-select-area {
  width: 99%;
  margin-top: 0.5%;
  height: 4%;
  float: left;
  border: 1px solid #bfcbd9;
}

.MySketch {
  margin-top: 0.5%;
  float: left;
  border: 1px solid midnightblue;
  margin-left: 1%;
  width: 28%;
  height: 82%;
}

.my-sketch-span {
  font-size: 12px;
  font-weight: bolder;
  margin-left: 2%;
}

.my-sketch-input {}

.my-bottom {
  float: right;
  width: 77%;
  height: 6%;
  border: 1px solid #5ac725;
  margin-right: 1%;
  margin-top: 2px;
  text-align: center;
}

.mu-bottom-text {
  font-size: 32px;
  color: red;
  font-weight: 700;
}

.my-bottom-button {
  margin-left: 23%;
  width: 15%;
  height: 5%;
  font-size: 26px !important;
}

.my-drawer {
  margin-top: 0.1%;
  border: 1px solid #5ac725;
  height: 90%;
  width: 99%;
}

.my-drawer-table {
  width: 68%;
  height: 85%;
  border: 1px solid #3A5FCD;
  float: left;
  position: relative;
}

.my-drawer-in {
  margin-left: 1%;
  width: 31%;
  height: 85%;
  border: 1px solid #3A5FCD;
  float: left;
  position: relative;
}

.my-drawer-text {
  margin-top: 0.4%;
  width: 100%;
  height: 14.3%;
  border: 1px solid #3A5FCD;
  float: left;
}

.my-drawer-text-son {
  margin-top: 3.5%;
  margin-left: 25%;
  color: red;
  font-weight: bolder;
  font-size: 32px;
}

::v-deep.is-horizontal {
  display: none !important;
}

::v-deep.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: #00afff;
}

::v-deep.el-textarea {
  position: relative;
  width: 95%;
  vertical-align: bottom;
  font-weight: 1500;
  font-size: 12px;
  margin-left: 1.5%;
  border: 1px solid #00afff;
  border-radius: 4px;
}

/deep/ .el-scrollbar__wrap {
  overflow-x: hidden !important;
  height: 98%;
}

/deep/ .el-checkbox__label {
  display: inline-grid;
  font-size: 12px !important;
  white-space: pre-line;
  word-wrap: break-word;
  line-height: 20px;
}

/deep/ .el-textarea.is-disabled .el-textarea__inner {
  background-color: #FFFFFF;
  border-color: #E4E7ED;
  color: black;
  cursor: not-allowed;
}

/deep/ .pagination-container {
  background: #fff;
  padding: 0px !important;
  height: 0px;
  margin-top: 0px;
}

/deep/ .el-pagination .el-select .el-input .el-input__inner {
  padding-right: 0px;
  border-radius: 0px;
}

/deep/ .el-pagination {
  font-size: 10px;
  white-space: nowrap;
  color: #303133;
  font-weight: 600;
  float: left;
  position: relative;
}

/deep/ .el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next,
.el-pagination.is-background .el-pager li {
  margin: 0px 1px;
  background-color: #f4f4f5;
  color: #606266;
  min-width: 10px;
  border-radius: 2px;
}

/deep/ .el-pager li {
  padding: 0 4px;
  min-width: 10px !important;
  background: #FFFFFF;
  vertical-align: top;
  display: inline-block;
  font-size: 10px;
  height: 27px;
  line-height: 28px;
  cursor: pointer;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  text-align: center;
  margin: 0;
}

.el-pagination .btn-prev .el-icon,
.el-pagination .btn-next .el-icon {
  min-width: 10px !important;
  display: block;
  font-size: 12px;
  font-weight: bold;
}

/deep/ .el-drawer__header {
  margin-bottom: 0px;
  text-align: center;
  background-color: #9feae3;
  color: black;
  font-weight: 800;
  font-size: 18px;
  padding: 5px;
}

::v-deep.el-message__content {
  padding: 0;
  font-size: 24px;
  line-height: 1;
}


/deep/ .el-tree-node__label {
  font-size: 12px;
}

/deep/ .el-tree-node__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 22px;
  cursor: pointer;
}

/deep/ .el-transfer-panel__item .el-checkbox__input {
  position: absolute;
  top: 8px;
  /* left: 12px; */
  margin-left: -45px;
}

/deep/ .el-transfer-panel .el-transfer-panel__header .el-checkbox .el-checkbox__label span {
  position: absolute;
  right: 15px;
  color: #909399;
  font-size: 12px;
  font-weight: normal;
  top: 10px;
}

/deep/ .el-transfer-panel__body {
  height: 450px;
}

/deep/ .el-input {
  width: 86%;
}

/deep/ .el-table .el-table__header-wrapper th {
  word-break: break-word;
  background-color: #f8f8f9;
  color: #515a6e;
  height: 20px;
  font-size: 10px;
}

/deep/ .el-table--border .el-table__cell:first-child .cell {
  padding-left: 0;
}

/deep/ .el-table--mini .el-table__cell {
  padding: 1px;
}

/deep/ .el-table--mini {
  font-size: 10px;
}

/deep/ .el-input--medium .el-input__inner {
  height: 28px;
  line-height: 36px;
  font-size: 12px;
  text-align: center;
}

/deep/ .el-input__inner {
  padding: 0;
}

/deep/ .el-input__icon {
  width: 12px;
}

/deep/ .el-table .cell {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  word-break: break-all;
  line-height: 23px;
  padding: 0;
}

/deep/.el-button--medium {
  padding: 5px 0px;
  font-size: 20px;
  border-radius: 4px;
}

/deep/ .el-button+.el-button {
  margin-left: 1px;
  margin-right: 1px;
}

/deep/ .el-button--mini {
  padding: 0;
  font-size: 10px;
  border-radius: 1px;
}

::v-deep.el-select-dropdown__item {
  font-size: 12px;
  padding: 0px 20px;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #606266;
  height: 24px;
  line-height: 34px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
}

/deep/ .el-input--medium .el-input__icon {
  line-height: 25px;
}

.my-dialog-cseHistory {
  display: flex;
}

.el-menu-vertical-demo-item {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

/deep/ .el-dialog__header {
  margin-bottom: -25px;
  text-align: center;
  background-color: #9feae3;
  color: black;
  font-weight: 800;
  font-size: 18px;
  padding: 5px;
}

.my-dialog-cseHistory-button {
  display: flex;
  justify-content: space-evenly;
}

/deep/ .el-menu-item.is-active {
  color: black;
}

/*/deep/.el-input--mini .el-input__inner {*/
/*  height: 28px;*/
/*  line-height: 28px;*/
/*}*/
/*::v-deep.el-row {*/
/*  position: absolute;*/
/*  -webkit-box-sizing: border-box;*/
/*  box-sizing: border-box;*/
/*}*/
</style>
<style lang="scss"> //日期选择器
 .el-date-picker {
   width: 33%;
 }

 .my-dialog-2 {
 }

 .el-date-picker .el-picker-panel__content {
   width: 90%;
 }

 .timeLineItem {
   position: relative;

   span {
     position: absolute;
     margin-left: -55px;
     margin-top: 2px;
   }
 }

 .date-my {}

 .timeline {
   margin-left: 30%;
   margin-top: 10%;
 }

 .center-text {
   display: flex;
   justify-content: center;
   align-items: center;
 }

 .pdm-header {
   position: fixed;
   bottom: 1px;
   right: 50px;
 }
</style>


