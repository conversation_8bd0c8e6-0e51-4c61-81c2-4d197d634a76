<template>
  <div>
    <el-drawer title="病历查阅" :visible.sync="drawer" size="85%">
      <!-- 门诊数据查看 -->
      <div v-if="this.queryParams.patientSource == 1 || this.queryParams.patientSource == 4" class="my-table">
        <el-table :data="tableMzList" stripe border>
          <el-table-column prop="visit_date" align="center" label="就诊日期" :formatter="formatterTime"/>
          <el-table-column prop="visit_no" align="center" label="就诊号"/>
          <!-- <el-table-column prop="patient_id" align="center" label="患者ID" /> -->
          <el-table-column prop="title" align="center" label="标题"/>
          <el-table-column prop="create_user" align="center" label="医生"/>
          <el-table-column prop="create_title" align="center" label="医生职称"/>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button class="my-table-text" type="text" plain
                         @click="lookPdfMz(scope.row)"
              >查看PDF报告
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- 住院数据查看 -->
      <div v-else-if="this.queryParams.patientSource == 2" class="my-table">
        <el-table :data="tableZyList" stripe border>
          <!-- <el-table-column prop="patient_id" align="center" label="患者ID" /> -->
          <el-table-column prop="visit_id" align="center" label="住院次数"/>
          <el-table-column prop="topic" align="center" label="标题"/>
          <el-table-column prop="creator_name" align="center" label="医生"/>
          <el-table-column prop="create_date_time" align="center" label="创建时间" :formatter="formatterTime"/>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button class="my-table-text" type="text" plain
                         @click="lookPdfZy(scope.row)"
              >查看PDF报告
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
    <!-- 门诊PDF报告数据查看 -->
    <el-drawer title="门诊PDF报告数据查看" :visible.sync="drawerMz" size="85%">
            <span>
                <iframe :src="firPdfMz" alt class="iframe"></iframe>
            </span>
      <span style="display: flex;justify-content: space-around;">
                <el-button style="width: 200px;" type="danger" round @click="drawerMz = false">关 闭</el-button>
            </span>
    </el-drawer>
    <!-- 住院PDF报告数据查看 -->
    <el-drawer title="住院PDF报告数据查看" :visible.sync="drawerZy" size="85%">
            <span>
                <iframe :src="firPdfZy" alt class="iframe"></iframe>
            </span>
      <span style="display: flex;justify-content: space-around;">
                <el-button style="width: 200px;" type="danger" round @click="drawerZy = false">关 闭</el-button>
            </span>
    </el-drawer>
  </div>
</template>

<script>
import {
  GetMedicalRecordZyList, GetMedicalRecordMzList,
  GetMedicalRecordZyPdfList, GetMedicalRecordMzPdfList
} from '@/api/checkAndConfirm/medicalRecord'

export default {
  name: 'medicalRecord',
  props: [],
  components: {},
  data() {
    return {
      drawer: false,
      drawerMz: false,
      drawerZy: false,
      queryParams: {},
      queryParamsMz: {},
      queryParamsZy: {},
      tableMzList: [],
      tableZyList: [],
      firPdfMz: '',
      firPdfZy: ''
    }
  },

  created() {
  },

  mounted() {
  },

  methods: {
    medicalRecordView(data) {
      if (data) {
        this.vistry()
      } else {
        this.$msgbox.alert(
          '<div style="font-size: 24px !important;color: red; text-align: center;font-weight: 800">' +
          '请先选择列表需要查询的检查数据!!!' + '</div>',
          '系统提示',
          {
            confirmButtonText: '确定',
            type: 'warning',
            dangerouslyUseHTMLString: true
          })
        return
      }
    },

    // 查看门诊/住院列表数据
    vistry() {
      // patientSource=1/4代表门诊 patientSource=2代表住院
      const patientSource = this.$store.getters.rowTable.patientSource
      this.queryParams.patientSource = patientSource
      this.queryParamsMz.PatientId = this.$store.getters.rowTable.patientId
      this.queryParamsZy.PatientId = this.$store.getters.rowTable.patientId
      this.queryParamsZy.VisitId = this.$store.getters.rowTable.visitId

      // 门诊
      if (patientSource !== null && patientSource == '1' || patientSource == '4') {
        const loading = this.$loading({
          lock: true,
          text: '稍等,数据加载中(●' + '◡' + '●)',
          spinner: 'el-icon-coffee-cup',
          background: 'rgba(0, 0, 0, 0.7)'
        })
        GetMedicalRecordMzList(this.queryParamsMz).then(res => {
          this.tableMzList = res.data.electronicMzMedicalRetModels
          this.drawer = true
        }).finally(t => {
          loading.close()
        })
      } else {
        // 住院
        if (patientSource !== null && patientSource == '2') {
          const loading = this.$loading({
            lock: true,
            text: '稍等,数据加载中(●' + '◡' + '●)',
            spinner: 'el-icon-coffee-cup',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          GetMedicalRecordZyList(this.queryParamsZy).then(res => {
            this.tableZyList = res.data.electronicMedicalRetModels
            this.drawer = true
          }).finally(t => {
            loading.close()
          })
        }
      }
    },

    // 查看门诊PDF报告
    lookPdfMz(row) {
      GetMedicalRecordMzPdfList(row).then(res => {
        this.firPdfMz = res.data.outpatientServiceRetModels[0].imagePdfModel
        this.drawerMz = true
      })
    },

    // 查看住院PDF报告
    lookPdfZy(row) {

      GetMedicalRecordZyPdfList(row).then(res => {
        this.firPdfZy = res.data.imageAsyncRetModels[0].imageModel
        this.drawerZy = true
      })
    },

    // 时间不显示时分秒
    formatterTime(row, column) {
      let data = row[column.property]
      return /\d{4}-\d{1,2}-\d{1,2}/g.exec(data)
    }
  }
}
</script>

<style lang="scss" scoped>
.my-table {
  ::v-deep.el-table--medium .el-table__cell {
    padding: 0;
  }

  ::v-deep.el-table .el-table__header-wrapper th,
  .el-table .el-table__fixed-header-wrapper th {
    word-break: break-word;
    background-color: #f8f8f9;
    color: #303133;
    background-color: #1890ff38;
    height: 30px;
    font-size: 14px;
  }

  ::v-deep.el-table th.el-table__cell > .cell {
    padding: 0;
  }

  ::v-deep.el-table--border .el-table__cell:first-child .cell {
    padding: 0;
  }

  ::v-deep.el-button + .el-button {
    margin-left: 2px;
  }

  ::v-deep.el-table .cell {
    padding: 1px;
  }
}

.iframe {
  width: 100%;
  height: 600px;
}
</style>
