import request from '@/utils/request'

/**
 * 初始化校验
 * @param empNo 员工id
 * @returns {*} 结果
 * @constructor
 */
export function InitializationCheck(empNo) {
  return request({
    url: '/NurseRefundConfigCenter/InitializationCheck?empNo=' + empNo,
    method: 'get',
  })
}

export function GetGrantColumnList(data) {
  return request({
    url: '/NurseRefundConfigCenter/GetGrantColumnList',
    method: 'post',
    data: data,
  })
}

export function FastAuthorization(data) {
  return request({
    url: '/NurseRefundConfigCenter/FastAuthorization',
    method: 'post',
    data: data,
  })
}
