import request from "@/utils/request";

// 查询检查项目下拉选
export function GetExamTree(date) {
  return request({
    url: "/AppointmentRegister/CreateAppointmentRegister",
    method: "get",
    params: date,
  });
}

// 根据检查项目查询 可选择的检查详情集合
export function GetExamProjectParticularsList(begetterExamName, sonExamName) {
  return request({
    url: "/AppointmentRegister/GetExamProjectParticularsList?begetterExamName=" + begetterExamName + "&sonExamName=" + sonExamName,
    method: "get",
  });
}

// 查询检查项目详情信息
export function GetExamPatternParticulars(data) {
  return request({
    url: "/AppointmentRegister/GetExamPatternParticulars",
    method: "post",
    data: data,
  });
}

// 查询检查项目详情信息
export function GetExamPatternParticularsItem(data) {
  return request({
    url: "/AppointmentRegister/GetExamPatternParticularsItem",
    method: "post",
    data: data,
  });
}

// 保存检查项目信息
export function SaveExamAppointmentRegister(data) {
  return request({
    url: "/AppointmentRegister/SaveExamAppointmentRegister",
    method: "post",
    data: data,
  });
}

//获取预约检查列表
export function GetExamAppointmentRegisterList(data) {
  return request({
    url: "/AppointmentRegister/GetExamAppointmentRegisterList",
    method: "get",
    params: data,
  });
}

//获取检查树搜索下拉选
export function GetExamSelectTreeList(data) {
  return request({
    url: "/AppointmentRegister/GetExamSelectTreeList",
    method: "get",
    params: data,
  });
}

//获取检查树搜索下拉选
export function GetExamParticularsById(data) {
  return request({
    url: "/AppointmentRegister/GetExamParticularsById?id=" + data,
    method: "get",
  });
}

//点击 预约时间获取预约时间、字典、人数等信息
export function GetExamTimeDictNumber(examClassName, examSubClassName,date) {
  return request({
    url: "/AppointmentRegister/GetExamTimeDictNumber?examClassName=" + examClassName + "&examSubClassName=" + examSubClassName + '&dates='+date,
    method: "get",
  });
}
//检查时间流程图
export function GetExamFlowChart(id) {
  return request({
    url: "/AppointmentRegister/GetExamFlowChart?id=" + id,
    method: "get",
  });
}
//部位检查树
export function GetExamPositionItems() {
  return request({
    url: "/AppointmentRegister/GetExamPositionItems",
    method: "get",
  });
}

// 根据传入的主表 id   打印住院信息
export function GetExamHospitalizedPrint(id, type) {
  return request({
    url: "/AppointmentRegister/GetExamHospitalizedPrint?id=" + id + "&type=" + type,
    method: "get",
  });
}

// 批量打印
export function GetExamHospitalizedPrintAll(id, type) {
  return request({
    url: "/AppointmentRegister/GetExamHospitalizedPrintAll?id=" + id + "&type=" + type,
    method: "get",
  });
}

// 根据传入的主表 id   打印住院信息
export function Chargeback(data) {
  return request({
    url: "/AppointmentRegister/Chargeback",
    method: "post",
    data: data,
  });
}

//获取住院医嘱列表信息 GetAdviceList
export function GetAdviceList(patientId,visitId,type) {
  return request({
    url: "/AppointmentRegister/GetAdviceList?patientId=" + patientId + "&visitId=" + visitId + "&type=" + type,
    method: "get",
  });
}
//获取门诊病历列表 GetCseHistoryList
export function GetCseHistoryList(patientId,type) {
  return request({
    url: "/AppointmentRegister/GetCseHistoryList?patientId=" + patientId + "&type=" + type,
    method: "get",
  });
}


export function GetAiVerifyData(data) {
  return request({
    url: "/AppointmentRegister/GetAiVerifyData",
    method: "post",
    data: data,
  });
}

export function VerifyItemCodeMomoData(itemCode) {
  return request({
    url: "/AppointmentRegister/VerifyItemCodeMomoData?itemCode=" + itemCode,
    method: "get",
  });
}

export function GetSpecialItemList() {
  return request({
    url: "/AppointmentRegister/GetSpecialItemList",
    method: "get",
  });
}
