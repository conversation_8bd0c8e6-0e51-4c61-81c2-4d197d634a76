<template>
  <div>
    <!-- 物流通按钮操作 -->
  </div>
</template>

<script>
import { GetLogisticsReminder } from "@/api/checkAndConfirm/logisticsReminder";

export default {
  name: 'logisticsReminder',
  props: [],
  components: {},
  data() {
    return {
      data: {}
    }
  },
  created() {
  },
  mounted() {

  },
  methods: {
    LogisticsReminderButton() {
      let rowTable = this.$store.getters.rowTable;
      GetLogisticsReminder({
        exam_no: rowTable.examNo,
        from_dept: rowTable.performedBy,
        to_dept: rowTable.reqDept,
        name: rowTable.name,
        patient_id: rowTable.patientId,
        device: rowTable.device,
        user_name: this.$store.getters.empNo,
      }).then(t => {
        if (t.code === 200){
          this.$message.success(t.message);
        }
      })
    }
  },
}
</script>

<style scoped></style>
