import request from "@/utils/request";

// 查询字典类型列表
export function listType(query) {
  return request({
    url: "dict/listType",
    method: "get",
    params: query,
  });
}

// 查询字典类型详细
export function getType(dictId) {
  return request({
    url: "dict/getType?dictId=" + dictId,
    method: "get",
  });
}

// 新增字典类型
export function addType(data) {
  return request({
    url: "dict/addType",
    method: "post",
    data: data,
  });
}

// 修改字典类型
export function updateType(data) {
  return request({
    url: "dict/updateType",
    method: "post",
    data: data,
  });
}

// 删除字典类型
export function delType(dictIds) {
  return request({
    url: "dict/delType",
    method: "post",
    data: dictIds,
  });
}

// 刷新字典缓存
export function refreshCache() {
  return request({
    url: "dict/clearCache",
    method: "delete",
  });
}

// 获取字典选择框列表
export function optionselect() {
  return request({
    url: "dict/optionselect",
    method: "get",
  });
}
